#!/usr/bin/env python3
"""
测试 ConversationAgent 的时间日志功能
Test script for ConversationAgent timing logs
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加项目路径到 Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志格式，确保时间日志清晰可见
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'timing_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)

logger = logging.getLogger(__name__)

# 真实环境设置
def setup_real_environment():
    """设置真实的测试环境"""
    # 确保环境变量
    if not os.getenv('DEEPSEEK_API_KEY'):
        logger.warning("⚠️  DEEPSEEK_API_KEY 环境变量未设置，将使用默认配置")

    # 设置日志级别，确保能看到详细的时间日志
    logging.getLogger('profilerAgent.agent.services.conversation_agent').setLevel(logging.INFO)
    logging.getLogger('profilerAgent.agent.services.ai_service').setLevel(logging.INFO)

async def test_conversation_agent_timing():
    """测试 ConversationAgent 的时间日志功能"""

    logger.info("🚀 开始 ConversationAgent 时间日志测试")
    logger.info("=" * 60)

    try:
        # 设置真实环境
        setup_real_environment()

        # 导入 ConversationAgent 相关类
        from profilerAgent.agent.services.conversation_agent import (
            ConversationAgent,
            ConversationContext,
            AgentDecision
        )
        from profilerAgent.agent.services.ai_service import AIService
        from profilerAgent.agent.core.database import DatabaseManager

        # 创建真实的服务实例
        logger.info("🔧 初始化真实的 AI 服务...")
        ai_service = AIService()

        # 创建数据库管理器（可选，用于完整测试）
        db_manager = None  # 暂时禁用数据库以专注于 AI 性能测试

        # 创建 ConversationAgent 实例
        logger.info("🤖 创建 ConversationAgent 实例...")
        agent = ConversationAgent(
            ai_service=ai_service,
            db_manager=db_manager
        )
        
        # 创建测试对话上下文
        context = ConversationContext(
            call_sid="test_call_123",
            user_id="test_user_456",
            conversation_stage="greeting",
            collected_info={},
            conversation_history=[],
            emotional_state={"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5},
            time_remaining=300,
            questions_asked=0,
            successful_responses=0,
            timeout_count=0
        )
        
        # 测试场景：模拟完整对话流程
        test_inputs = [
            ("", "开始对话"),  # 初始问题生成
            ("Hi, my name is Alice and I'm from New York", "用户介绍自己"),
            ("I work as a software engineer at a tech company", "职业信息"),
            ("I'm more of an extrovert, I love meeting new people", "性格特征"),
            ("I enjoy reading books, coding, and hiking on weekends", "兴趣爱好"),
            ("I'm looking for a long-term relationship with someone who shares my values", "关系偏好")
        ]
        
        logger.info("📝 开始模拟对话流程...")
        logger.info("-" * 40)
        
        total_start_time = asyncio.get_event_loop().time()
        
        for i, (user_input, description) in enumerate(test_inputs, 1):
            logger.info(f"\n🔄 步骤 {i}: {description}")
            logger.info(f"用户输入: '{user_input}'")
            logger.info(f"当前阶段: {context.conversation_stage}")
            
            step_start_time = asyncio.get_event_loop().time()
            
            # 处理用户输入
            decision = await agent.process_input(user_input, context)
            
            step_end_time = asyncio.get_event_loop().time()
            step_duration = step_end_time - step_start_time
            
            logger.info(f"📤 Agent 回应: '{decision.content}'")
            logger.info(f"🎯 决策类型: {decision.action_type}")
            logger.info(f"📊 置信度: {decision.confidence:.2f}")
            logger.info(f"⏱️  步骤总耗时: {step_duration:.3f}s")
            
            # 更新对话阶段
            if decision.next_stage:
                context.conversation_stage = decision.next_stage
                logger.info(f"🔄 阶段转换: -> {decision.next_stage}")
            
            # 添加一些延迟来模拟真实对话
            await asyncio.sleep(0.1)
        
        total_end_time = asyncio.get_event_loop().time()
        total_duration = total_end_time - total_start_time
        
        logger.info("\n" + "=" * 60)
        logger.info("📊 测试总结")
        logger.info("=" * 60)
        logger.info(f"⏱️  总测试时间: {total_duration:.3f}s")
        logger.info(f"🔢 处理步骤数: {len(test_inputs)}")
        logger.info(f"📈 平均每步耗时: {total_duration/len(test_inputs):.3f}s")
        logger.info(f"💬 最终对话阶段: {context.conversation_stage}")
        logger.info(f"📝 收集的信息: {len(context.collected_info)} 项")
        logger.info(f"🎯 成功回应数: {context.successful_responses}")
        
        # 显示收集到的信息
        if context.collected_info:
            logger.info("\n📋 收集到的用户信息:")
            for key, value in context.collected_info.items():
                logger.info(f"  • {key}: {value}")
        
        # 显示情感状态
        logger.info(f"\n😊 最终情感状态:")
        for key, value in context.emotional_state.items():
            logger.info(f"  • {key}: {value:.2f}")
        
        logger.info("\n✅ 时间日志测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"错误详情:\n{traceback.format_exc()}")
        raise

async def test_individual_methods():
    """测试单个方法的时间性能"""

    logger.info("\n🔬 开始单个方法性能测试")
    logger.info("=" * 60)

    try:
        # 设置真实环境
        setup_real_environment()

        from profilerAgent.agent.services.conversation_agent import (
            ConversationAgent,
            ConversationContext
        )
        from profilerAgent.agent.services.ai_service import AIService

        # 使用真实的 AI 服务
        ai_service = AIService()
        agent = ConversationAgent(ai_service=ai_service, db_manager=None)
        
        context = ConversationContext(
            call_sid="test_call_method",
            user_id="test_user_method",
            conversation_stage="professional",
            collected_info={"name": "Alice"},
            conversation_history=[],
            emotional_state={"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5},
            time_remaining=300,
            questions_asked=2,
            successful_responses=1,
            timeout_count=0
        )
        
        test_input = "I work as a software engineer and I love coding"
        
        # 测试信息提取
        logger.info("🧪 测试信息提取方法...")
        await agent._extract_information_safe(test_input, context)
        
        # 测试情感状态更新
        logger.info("🧪 测试情感状态更新方法...")
        await agent._update_emotional_state_safe(test_input, context)
        
        # 测试决策制定
        logger.info("🧪 测试智能决策方法...")
        decision = await agent._make_smart_decision(context)
        logger.info(f"决策结果: {decision.content}")
        
        logger.info("✅ 单个方法测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 单个方法测试失败: {e}")
        raise

if __name__ == "__main__":
    print("🎯 ConversationAgent 时间日志测试")
    print("=" * 50)
    
    # 运行主要测试
    asyncio.run(test_conversation_agent_timing())
    
    # 运行单个方法测试
    asyncio.run(test_individual_methods())
    
    print("\n🎉 所有测试完成！请查看日志文件获取详细的时间分析。")
