#!/usr/bin/env python3
"""
分析 ConversationAgent 时间日志的性能分析工具
Performance analysis tool for ConversationAgent timing logs
"""

import re
import sys
import os
from collections import defaultdict, Counter
from datetime import datetime
import statistics

def parse_timing_log(log_file_path):
    """解析时间日志文件"""
    timing_data = []
    method_times = defaultdict(list)
    
    # 时间日志的正则表达式
    timing_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).*⏱️\s+(.+?)\s+completed in\s+(\d+\.\d+)s'
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                match = re.search(timing_pattern, line)
                if match:
                    timestamp_str, method_name, duration_str = match.groups()
                    duration = float(duration_str)
                    
                    # 解析时间戳
                    timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
                    
                    timing_data.append({
                        'line': line_num,
                        'timestamp': timestamp,
                        'method': method_name,
                        'duration': duration
                    })
                    
                    method_times[method_name].append(duration)
        
        return timing_data, method_times
    
    except FileNotFoundError:
        print(f"❌ 错误: 找不到日志文件 {log_file_path}")
        return [], {}
    except Exception as e:
        print(f"❌ 解析日志文件时出错: {e}")
        return [], {}

def analyze_performance(timing_data, method_times):
    """分析性能数据"""
    if not timing_data:
        print("❌ 没有找到时间日志数据")
        return
    
    print("📊 ConversationAgent 性能分析报告")
    print("=" * 60)
    
    # 总体统计
    total_calls = len(timing_data)
    total_time = sum(item['duration'] for item in timing_data)
    avg_time = total_time / total_calls if total_calls > 0 else 0
    
    print(f"🔢 总方法调用次数: {total_calls}")
    print(f"⏱️  总执行时间: {total_time:.3f}s")
    print(f"📈 平均执行时间: {avg_time:.3f}s")
    
    # 按方法分析
    print(f"\n📋 按方法分类的性能统计:")
    print("-" * 80)
    print(f"{'方法名':<35} {'调用次数':<8} {'总时间(s)':<10} {'平均(s)':<8} {'最大(s)':<8} {'最小(s)':<8}")
    print("-" * 80)
    
    # 按平均时间排序
    sorted_methods = sorted(method_times.items(), 
                          key=lambda x: statistics.mean(x[1]), 
                          reverse=True)
    
    for method_name, times in sorted_methods:
        count = len(times)
        total = sum(times)
        avg = statistics.mean(times)
        max_time = max(times)
        min_time = min(times)
        
        print(f"{method_name:<35} {count:<8} {total:<10.3f} {avg:<8.3f} {max_time:<8.3f} {min_time:<8.3f}")
    
    # 性能热点分析
    print(f"\n🔥 性能热点分析:")
    print("-" * 40)
    
    # 最耗时的方法调用
    slowest_calls = sorted(timing_data, key=lambda x: x['duration'], reverse=True)[:5]
    print("⚠️  最慢的5次方法调用:")
    for i, call in enumerate(slowest_calls, 1):
        print(f"  {i}. {call['method']}: {call['duration']:.3f}s")
    
    # 最频繁调用的方法
    method_counts = Counter(item['method'] for item in timing_data)
    most_frequent = method_counts.most_common(5)
    print(f"\n📞 调用最频繁的5个方法:")
    for i, (method, count) in enumerate(most_frequent, 1):
        avg_time = statistics.mean(method_times[method])
        print(f"  {i}. {method}: {count}次调用, 平均 {avg_time:.3f}s")

def generate_summary_report(timing_data, method_times):
    """生成简要性能报告"""
    if not timing_data:
        return
    
    print(f"\n📝 性能摘要:")
    print("=" * 40)
    
    # 关键指标
    total_time = sum(item['duration'] for item in timing_data)
    process_input_times = method_times.get('process_input', [])
    
    if process_input_times:
        avg_process_time = statistics.mean(process_input_times)
        print(f"🎯 平均对话处理时间: {avg_process_time:.3f}s")
        
        # 计算吞吐量 (每秒处理的对话轮次)
        throughput = 1 / avg_process_time if avg_process_time > 0 else 0
        print(f"📈 理论吞吐量: {throughput:.1f} 轮次/秒")
    
    # AI相关方法的总时间
    ai_methods = ['ai_decision', 'extract_information_detailed', 'update_emotional_state_detailed']
    ai_total_time = sum(sum(method_times.get(method, [])) for method in ai_methods)
    ai_percentage = (ai_total_time / total_time * 100) if total_time > 0 else 0
    
    print(f"🤖 AI处理时间占比: {ai_percentage:.1f}%")
    
    # 数据库相关时间
    db_methods = ['save_agent_data']
    db_total_time = sum(sum(method_times.get(method, [])) for method in db_methods)
    db_percentage = (db_total_time / total_time * 100) if total_time > 0 else 0
    
    print(f"💾 数据库操作时间占比: {db_percentage:.1f}%")
    
    # 性能建议
    print(f"\n💡 性能优化建议:")
    print("-" * 30)
    
    # 找出平均时间超过1秒的方法
    slow_methods = [(name, statistics.mean(times)) for name, times in method_times.items() 
                   if statistics.mean(times) > 1.0]
    
    if slow_methods:
        slow_methods.sort(key=lambda x: x[1], reverse=True)
        print("⚠️  以下方法执行时间较长，建议优化:")
        for method, avg_time in slow_methods:
            print(f"  • {method}: 平均 {avg_time:.3f}s")
    else:
        print("✅ 所有方法执行时间都在合理范围内")

def main():
    """主函数"""
    print("🔍 ConversationAgent 时间日志分析工具")
    print("=" * 50)
    
    # 查找最新的日志文件
    log_files = [f for f in os.listdir('.') if f.startswith('timing_test_') and f.endswith('.log')]
    
    if not log_files:
        print("❌ 没有找到时间日志文件")
        print("请先运行 test_conversation_agent_timing.py 生成日志文件")
        return
    
    # 使用最新的日志文件
    latest_log = sorted(log_files)[-1]
    print(f"📁 分析日志文件: {latest_log}")
    print()
    
    # 解析日志
    timing_data, method_times = parse_timing_log(latest_log)
    
    if not timing_data:
        print("❌ 没有找到有效的时间日志数据")
        return
    
    # 执行分析
    analyze_performance(timing_data, method_times)
    generate_summary_report(timing_data, method_times)
    
    print(f"\n✅ 分析完成！共分析了 {len(timing_data)} 条时间记录")

if __name__ == "__main__":
    main()
