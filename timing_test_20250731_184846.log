2025-07-31 18:48:46,951 - __main__ - INFO - 🚀 开始 ConversationAgent 时间日志测试
2025-07-31 18:48:46,951 - __main__ - INFO - ============================================================
2025-07-31 18:48:46,952 - __main__ - WARNING - ⚠️  DEEPSEEK_API_KEY 环境变量未设置，将使用默认配置
2025-07-31 18:48:47,158 - __main__ - INFO - 🔧 初始化真实的 AI 服务...
2025-07-31 18:48:47,158 - profilerAgent.agent.services.ai_service - INFO - AIService initialized - provider: openai, enabled: True
2025-07-31 18:48:47,158 - __main__ - INFO - 🤖 创建 ConversationAgent 实例...
2025-07-31 18:48:47,158 - profilerAgent.agent.services.conversation_agent - INFO - ConversationAgent initialized with improved performance settings
2025-07-31 18:48:47,158 - __main__ - INFO - 📝 开始模拟对话流程...
2025-07-31 18:48:47,158 - __main__ - INFO - ----------------------------------------
2025-07-31 18:48:47,158 - __main__ - INFO - 
🔄 步骤 1: 开始对话
2025-07-31 18:48:47,158 - __main__ - INFO - 用户输入: ''
2025-07-31 18:48:47,158 - __main__ - INFO - 当前阶段: greeting
2025-07-31 18:48:47,158 - profilerAgent.agent.services.ai_service - INFO - Generating AI question for domain: greeting
2025-07-31 18:48:49,059 - profilerAgent.agent.services.ai_service - INFO - AI question generated: What’s something you enjoy doing in your free time that really makes you happy?
2025-07-31 18:48:49,059 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 1.901s
2025-07-31 18:48:49,059 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 1.901s
2025-07-31 18:48:49,059 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  process_input completed in 1.901s
2025-07-31 18:48:49,059 - __main__ - INFO - 📤 Agent 回应: 'What’s something you enjoy doing in your free time that really makes you happy?'
2025-07-31 18:48:49,059 - __main__ - INFO - 🎯 决策类型: ask_question
2025-07-31 18:48:49,059 - __main__ - INFO - 📊 置信度: 0.80
2025-07-31 18:48:49,059 - __main__ - INFO - ⏱️  步骤总耗时: 1.901s
2025-07-31 18:48:49,059 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.000s
2025-07-31 18:48:49,161 - __main__ - INFO - 
🔄 步骤 2: 用户介绍自己
2025-07-31 18:48:49,161 - __main__ - INFO - 用户输入: 'Hi, my name is Alice and I'm from New York'
2025-07-31 18:48:49,161 - __main__ - INFO - 当前阶段: greeting
2025-07-31 18:48:50,830 - profilerAgent.agent.services.ai_service - INFO - AI extracted info: {'name': 'Alice', 'city': 'New York'}
2025-07-31 18:48:50,830 - profilerAgent.agent.services.conversation_agent - INFO - AI extracted and merged info: {'name': 'Alice', 'city': 'New York'}
2025-07-31 18:48:50,830 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 1.669s
2025-07-31 18:48:50,830 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 1.669s
2025-07-31 18:48:50,921 - profilerAgent.agent.services.ai_service - INFO - AI emotion analysis: {'engagement': 0.4, 'comfort': 0.7, 'enthusiasm': 0.3}
2025-07-31 18:48:50,922 - profilerAgent.agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.42999999999999994, 'comfort': 0.6399999999999999, 'enthusiasm': 0.36}
2025-07-31 18:48:50,922 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state_detailed completed in 1.761s
2025-07-31 18:48:50,922 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state completed in 1.761s
2025-07-31 18:48:50,924 - profilerAgent.agent.services.conversation_agent - INFO - Parallel AI processing completed in 1.76s
2025-07-31 18:48:50,924 - profilerAgent.agent.services.ai_service - INFO - Generating AI question for domain: greeting
2025-07-31 18:48:51,991 - profilerAgent.agent.services.ai_service - INFO - AI question generated: What do you enjoy most about living in New York, Alice?
2025-07-31 18:48:51,992 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 1.068s
2025-07-31 18:48:51,992 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 1.068s
2025-07-31 18:48:51,992 - profilerAgent.agent.services.conversation_agent - INFO - Decision generated in 1.07s, total processing: 2.83s
2025-07-31 18:48:51,992 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  async_optimization completed in 2.831s
2025-07-31 18:48:51,992 - profilerAgent.agent.services.conversation_agent - INFO - Agent transitioning stage: greeting -> professional
2025-07-31 18:48:51,992 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  process_input completed in 2.831s
2025-07-31 18:48:51,992 - __main__ - INFO - 📤 Agent 回应: 'What do you enjoy most about living in New York, Alice?'
2025-07-31 18:48:51,992 - __main__ - INFO - 🎯 决策类型: ask_question
2025-07-31 18:48:51,992 - __main__ - INFO - 📊 置信度: 0.80
2025-07-31 18:48:51,992 - __main__ - INFO - ⏱️  步骤总耗时: 2.831s
2025-07-31 18:48:51,992 - __main__ - INFO - 🔄 阶段转换: -> professional
2025-07-31 18:48:51,992 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.000s
2025-07-31 18:48:52,093 - __main__ - INFO - 
🔄 步骤 3: 职业信息
2025-07-31 18:48:52,093 - __main__ - INFO - 用户输入: 'I work as a software engineer at a tech company'
2025-07-31 18:48:52,093 - __main__ - INFO - 当前阶段: professional
2025-07-31 18:48:53,746 - profilerAgent.agent.services.ai_service - INFO - AI emotion analysis: {'engagement': 0.6, 'comfort': 0.7, 'enthusiasm': 0.5}
2025-07-31 18:48:53,746 - profilerAgent.agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.5489999999999999, 'comfort': 0.6819999999999999, 'enthusiasm': 0.45799999999999996}
2025-07-31 18:48:53,746 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state_detailed completed in 1.653s
2025-07-31 18:48:53,746 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state completed in 1.653s
2025-07-31 18:48:54,172 - profilerAgent.agent.services.ai_service - INFO - AI extracted info: {'name': None, 'city': None, 'profession': 'software engineer', 'personality_traits': [], 'interests': [], 'relationship_preferences': []}
2025-07-31 18:48:54,172 - profilerAgent.agent.services.conversation_agent - INFO - AI extracted and merged info: {'name': None, 'city': None, 'profession': 'software engineer', 'personality_traits': [], 'interests': [], 'relationship_preferences': []}
2025-07-31 18:48:54,172 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 2.078s
2025-07-31 18:48:54,172 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 2.078s
2025-07-31 18:48:54,172 - profilerAgent.agent.services.conversation_agent - INFO - Parallel AI processing completed in 2.08s
2025-07-31 18:48:54,172 - profilerAgent.agent.services.ai_service - INFO - Generating AI question for domain: professional
2025-07-31 18:48:55,865 - profilerAgent.agent.services.ai_service - INFO - AI question generated: What inspired you to become a software engineer, and what do you enjoy most about your work?
2025-07-31 18:48:55,865 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 1.692s
2025-07-31 18:48:55,865 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 1.692s
2025-07-31 18:48:55,865 - profilerAgent.agent.services.conversation_agent - INFO - Decision generated in 1.69s, total processing: 3.77s
2025-07-31 18:48:55,865 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  async_optimization completed in 3.771s
2025-07-31 18:48:55,865 - profilerAgent.agent.services.conversation_agent - INFO - Agent transitioning stage: professional -> personality
2025-07-31 18:48:55,865 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  process_input completed in 3.771s
2025-07-31 18:48:55,865 - __main__ - INFO - 📤 Agent 回应: 'What inspired you to become a software engineer, and what do you enjoy most about your work?'
2025-07-31 18:48:55,865 - __main__ - INFO - 🎯 决策类型: ask_question
2025-07-31 18:48:55,865 - __main__ - INFO - 📊 置信度: 0.80
2025-07-31 18:48:55,865 - __main__ - INFO - ⏱️  步骤总耗时: 3.771s
2025-07-31 18:48:55,865 - __main__ - INFO - 🔄 阶段转换: -> personality
2025-07-31 18:48:55,865 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.000s
2025-07-31 18:48:55,966 - __main__ - INFO - 
🔄 步骤 4: 性格特征
2025-07-31 18:48:55,966 - __main__ - INFO - 用户输入: 'I'm more of an extrovert, I love meeting new people'
2025-07-31 18:48:55,966 - __main__ - INFO - 当前阶段: personality
2025-07-31 18:48:57,664 - profilerAgent.agent.services.ai_service - INFO - AI emotion analysis: {'engagement': 0.7, 'comfort': 0.9, 'enthusiasm': 0.6}
2025-07-31 18:48:57,665 - profilerAgent.agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.6547, 'comfort': 0.8346, 'enthusiasm': 0.5574}
2025-07-31 18:48:57,665 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state_detailed completed in 1.699s
2025-07-31 18:48:57,665 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state completed in 1.699s
2025-07-31 18:48:57,908 - profilerAgent.agent.services.ai_service - INFO - AI extracted info: {'name': '', 'city': '', 'profession': '', 'personality_traits': ['extrovert'], 'interests': ['meeting new people'], 'relationship_preferences': []}
2025-07-31 18:48:57,908 - profilerAgent.agent.services.conversation_agent - INFO - AI extracted and merged info: {'name': '', 'city': '', 'profession': '', 'personality_traits': ['extrovert'], 'interests': ['meeting new people'], 'relationship_preferences': []}
2025-07-31 18:48:57,908 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 1.942s
2025-07-31 18:48:57,908 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 1.942s
2025-07-31 18:48:57,908 - profilerAgent.agent.services.conversation_agent - INFO - Parallel AI processing completed in 1.94s
2025-07-31 18:48:57,908 - profilerAgent.agent.services.ai_service - INFO - Generating AI question for domain: personality
2025-07-31 18:48:59,152 - profilerAgent.agent.services.ai_service - INFO - AI question generated: What qualities do you value most in your relationships with friends or partners?
2025-07-31 18:48:59,152 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 1.244s
2025-07-31 18:48:59,153 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 1.245s
2025-07-31 18:48:59,153 - profilerAgent.agent.services.conversation_agent - INFO - Decision generated in 1.24s, total processing: 3.19s
2025-07-31 18:48:59,153 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  async_optimization completed in 3.187s
2025-07-31 18:48:59,153 - profilerAgent.agent.services.conversation_agent - INFO - Agent transitioning stage: personality -> relationships
2025-07-31 18:48:59,153 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  process_input completed in 3.187s
2025-07-31 18:48:59,153 - __main__ - INFO - 📤 Agent 回应: 'What qualities do you value most in your relationships with friends or partners?'
2025-07-31 18:48:59,153 - __main__ - INFO - 🎯 决策类型: ask_question
2025-07-31 18:48:59,153 - __main__ - INFO - 📊 置信度: 0.80
2025-07-31 18:48:59,153 - __main__ - INFO - ⏱️  步骤总耗时: 3.187s
2025-07-31 18:48:59,153 - __main__ - INFO - 🔄 阶段转换: -> relationships
2025-07-31 18:48:59,153 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.000s
2025-07-31 18:48:59,254 - __main__ - INFO - 
🔄 步骤 5: 兴趣爱好
2025-07-31 18:48:59,255 - __main__ - INFO - 用户输入: 'I enjoy reading books, coding, and hiking on weekends'
2025-07-31 18:48:59,255 - __main__ - INFO - 当前阶段: relationships
2025-07-31 18:49:01,050 - profilerAgent.agent.services.ai_service - INFO - AI extracted info: {'name': '', 'city': '', 'profession': '', 'personality_traits': [], 'interests': ['reading books', 'coding', 'hiking'], 'relationship_preferences': []}
2025-07-31 18:49:01,050 - profilerAgent.agent.services.conversation_agent - INFO - AI extracted and merged info: {'name': '', 'city': '', 'profession': '', 'personality_traits': [], 'interests': ['reading books', 'coding', 'hiking'], 'relationship_preferences': []}
2025-07-31 18:49:01,050 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 1.795s
2025-07-31 18:49:01,050 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 1.795s
2025-07-31 18:49:01,553 - profilerAgent.agent.services.ai_service - INFO - AI emotion analysis: {'engagement': 0.7, 'comfort': 0.9, 'enthusiasm': 0.6}
2025-07-31 18:49:01,553 - profilerAgent.agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.68641, 'comfort': 0.8803799999999999, 'enthusiasm': 0.58722}
2025-07-31 18:49:01,553 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state_detailed completed in 2.298s
2025-07-31 18:49:01,554 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state completed in 2.298s
2025-07-31 18:49:01,554 - profilerAgent.agent.services.conversation_agent - INFO - Parallel AI processing completed in 2.30s
2025-07-31 18:49:01,554 - profilerAgent.agent.services.ai_service - INFO - Generating AI question for domain: relationships
2025-07-31 18:49:03,541 - profilerAgent.agent.services.ai_service - INFO - AI question generated: What do you think is the most important quality for a strong relationship, whether it's with friends or a partner?
2025-07-31 18:49:03,541 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 1.987s
2025-07-31 18:49:03,541 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 1.987s
2025-07-31 18:49:03,541 - profilerAgent.agent.services.conversation_agent - INFO - Decision generated in 1.99s, total processing: 4.29s
2025-07-31 18:49:03,541 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  async_optimization completed in 4.286s
2025-07-31 18:49:03,541 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  process_input completed in 4.286s
2025-07-31 18:49:03,541 - __main__ - INFO - 📤 Agent 回应: 'What do you think is the most important quality for a strong relationship, whether it's with friends or a partner?'
2025-07-31 18:49:03,541 - __main__ - INFO - 🎯 决策类型: ask_question
2025-07-31 18:49:03,541 - __main__ - INFO - 📊 置信度: 0.80
2025-07-31 18:49:03,541 - __main__ - INFO - ⏱️  步骤总耗时: 4.286s
2025-07-31 18:49:03,541 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.000s
2025-07-31 18:49:03,644 - __main__ - INFO - 
🔄 步骤 6: 关系偏好
2025-07-31 18:49:03,644 - __main__ - INFO - 用户输入: 'I'm looking for a long-term relationship with someone who shares my values'
2025-07-31 18:49:03,644 - __main__ - INFO - 当前阶段: relationships
2025-07-31 18:49:05,715 - profilerAgent.agent.services.ai_service - INFO - AI extracted info: {'name': None, 'city': None, 'profession': None, 'personality_traits': None, 'interests': None, 'relationship_preferences': ['long-term relationship', 'shares my values']}
2025-07-31 18:49:05,716 - profilerAgent.agent.services.conversation_agent - INFO - AI extracted and merged info: {'name': None, 'city': None, 'profession': None, 'personality_traits': None, 'interests': None, 'relationship_preferences': ['long-term relationship', 'shares my values']}
2025-07-31 18:49:05,716 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 2.071s
2025-07-31 18:49:05,716 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 2.071s
2025-07-31 18:49:06,261 - profilerAgent.agent.services.ai_service - INFO - AI emotion analysis: {'engagement': 0.8, 'comfort': 0.9, 'enthusiasm': 0.6}
2025-07-31 18:49:06,261 - profilerAgent.agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.7659229999999999, 'comfort': 0.894114, 'enthusiasm': 0.596166}
2025-07-31 18:49:06,261 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state_detailed completed in 2.616s
2025-07-31 18:49:06,262 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state completed in 2.616s
2025-07-31 18:49:06,262 - profilerAgent.agent.services.conversation_agent - INFO - Parallel AI processing completed in 2.62s
2025-07-31 18:49:06,262 - profilerAgent.agent.services.ai_service - INFO - Generating AI question for domain: relationships
2025-07-31 18:49:07,673 - profilerAgent.agent.services.ai_service - INFO - AI question generated: What values are most important to you in a partner, and how do you see them influencing your relationship?
2025-07-31 18:49:07,673 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 1.412s
2025-07-31 18:49:07,673 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 1.412s
2025-07-31 18:49:07,673 - profilerAgent.agent.services.conversation_agent - INFO - Decision generated in 1.41s, total processing: 4.03s
2025-07-31 18:49:07,673 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  async_optimization completed in 4.029s
2025-07-31 18:49:07,673 - profilerAgent.agent.services.conversation_agent - INFO - Agent transitioning stage: relationships -> closing
2025-07-31 18:49:07,673 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  process_input completed in 4.029s
2025-07-31 18:49:07,674 - __main__ - INFO - 📤 Agent 回应: 'What values are most important to you in a partner, and how do you see them influencing your relationship?'
2025-07-31 18:49:07,674 - __main__ - INFO - 🎯 决策类型: ask_question
2025-07-31 18:49:07,674 - __main__ - INFO - 📊 置信度: 0.80
2025-07-31 18:49:07,674 - __main__ - INFO - ⏱️  步骤总耗时: 4.029s
2025-07-31 18:49:07,674 - __main__ - INFO - 🔄 阶段转换: -> closing
2025-07-31 18:49:07,674 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.000s
2025-07-31 18:49:07,775 - __main__ - INFO - 
============================================================
2025-07-31 18:49:07,775 - __main__ - INFO - 📊 测试总结
2025-07-31 18:49:07,775 - __main__ - INFO - ============================================================
2025-07-31 18:49:07,775 - __main__ - INFO - ⏱️  总测试时间: 20.616s
2025-07-31 18:49:07,775 - __main__ - INFO - 🔢 处理步骤数: 6
2025-07-31 18:49:07,775 - __main__ - INFO - 📈 平均每步耗时: 3.436s
2025-07-31 18:49:07,775 - __main__ - INFO - 💬 最终对话阶段: closing
2025-07-31 18:49:07,775 - __main__ - INFO - 📝 收集的信息: 6 项
2025-07-31 18:49:07,775 - __main__ - INFO - 🎯 成功回应数: 5
2025-07-31 18:49:07,775 - __main__ - INFO - 
📋 收集到的用户信息:
2025-07-31 18:49:07,775 - __main__ - INFO -   • name: Alice
2025-07-31 18:49:07,775 - __main__ - INFO -   • city: New York
2025-07-31 18:49:07,775 - __main__ - INFO -   • profession: software engineer
2025-07-31 18:49:07,775 - __main__ - INFO -   • personality_traits: ['extrovert']
2025-07-31 18:49:07,775 - __main__ - INFO -   • interests: ['meeting new people', 'reading books', 'coding', 'hiking']
2025-07-31 18:49:07,775 - __main__ - INFO -   • relationship_preferences: ['long-term relationship', 'shares my values']
2025-07-31 18:49:07,775 - __main__ - INFO - 
😊 最终情感状态:
2025-07-31 18:49:07,775 - __main__ - INFO -   • engagement: 0.77
2025-07-31 18:49:07,776 - __main__ - INFO -   • comfort: 0.89
2025-07-31 18:49:07,776 - __main__ - INFO -   • enthusiasm: 0.60
2025-07-31 18:49:07,776 - __main__ - INFO - 
✅ 时间日志测试完成！
2025-07-31 18:49:07,776 - __main__ - INFO - 
🔬 开始单个方法性能测试
2025-07-31 18:49:07,776 - __main__ - INFO - ============================================================
2025-07-31 18:49:07,776 - profilerAgent.agent.services.ai_service - INFO - AIService initialized - provider: openai, enabled: True
2025-07-31 18:49:07,776 - profilerAgent.agent.services.conversation_agent - INFO - ConversationAgent initialized with improved performance settings
2025-07-31 18:49:07,776 - __main__ - INFO - 🧪 测试信息提取方法...
2025-07-31 18:49:09,582 - profilerAgent.agent.services.ai_service - INFO - AI extracted info: {'name': '', 'city': '', 'profession': 'software engineer', 'personality_traits': [], 'interests': ['coding'], 'relationship_preferences': []}
2025-07-31 18:49:09,582 - profilerAgent.agent.services.conversation_agent - INFO - AI extracted and merged info: {'name': '', 'city': '', 'profession': 'software engineer', 'personality_traits': [], 'interests': ['coding'], 'relationship_preferences': []}
2025-07-31 18:49:09,582 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 1.806s
2025-07-31 18:49:09,582 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 1.806s
2025-07-31 18:49:09,582 - __main__ - INFO - 🧪 测试情感状态更新方法...
2025-07-31 18:49:11,029 - profilerAgent.agent.services.ai_service - INFO - AI emotion analysis: {'engagement': 0.7, 'comfort': 0.9, 'enthusiasm': 0.8}
2025-07-31 18:49:11,029 - profilerAgent.agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.6399999999999999, 'comfort': 0.78, 'enthusiasm': 0.71}
2025-07-31 18:49:11,029 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state_detailed completed in 1.447s
2025-07-31 18:49:11,030 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state completed in 1.447s
2025-07-31 18:49:11,030 - __main__ - INFO - 🧪 测试智能决策方法...
2025-07-31 18:49:11,030 - profilerAgent.agent.services.ai_service - INFO - Generating AI question for domain: professional
2025-07-31 18:49:12,267 - profilerAgent.agent.services.ai_service - INFO - AI question generated: What do you enjoy most about being a software engineer, and how did you get started in this field?
2025-07-31 18:49:12,268 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 1.238s
2025-07-31 18:49:12,268 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 1.239s
2025-07-31 18:49:12,268 - __main__ - INFO - 决策结果: What do you enjoy most about being a software engineer, and how did you get started in this field?
2025-07-31 18:49:12,268 - __main__ - INFO - ✅ 单个方法测试完成！
