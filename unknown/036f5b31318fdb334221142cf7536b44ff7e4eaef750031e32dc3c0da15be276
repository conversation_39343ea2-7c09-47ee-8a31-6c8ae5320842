t=2025-07-29T00:43:55+0800 lvl=info msg="no configuration paths supplied"
t=2025-07-29T00:43:55+0800 lvl=info msg="using configuration at default config path" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml"
t=2025-07-29T00:43:55+0800 lvl=info msg="open config file" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml" err=nil
t=2025-07-29T00:43:55+0800 lvl=info msg="starting web service" obj=web addr=127.0.0.1:4040 allow_hosts=[]
t=2025-07-29T00:43:56+0800 lvl=info msg="update available" obj=updater
t=2025-07-29T00:43:56+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T00:43:56+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T00:43:57+0800 lvl=info msg="started tunnel" obj=tunnels name=command_line addr=http://localhost:8000 url=https://e36ed358d667.ngrok-free.app
t=2025-07-29T00:44:00+0800 lvl=info msg=start pg=/api/tunnels id=23ce6ace518ec7d3
t=2025-07-29T00:44:00+0800 lvl=info msg=end pg=/api/tunnels id=23ce6ace518ec7d3 status=200 dur=176.333µs
t=2025-07-29T00:44:19+0800 lvl=info msg="join connections" obj=join id=9ce1ab6e7e10 l=127.0.0.1:8000 r=*************:38500
t=2025-07-29T00:46:27+0800 lvl=info msg="join connections" obj=join id=f5a2119ced67 l=127.0.0.1:8000 r=*************:54842
t=2025-07-29T00:46:29+0800 lvl=info msg="join connections" obj=join id=d04a537a25bf l=127.0.0.1:8000 r=**************:55838
t=2025-07-29T00:46:37+0800 lvl=info msg="join connections" obj=join id=432916870e28 l=127.0.0.1:8000 r=**********:23762
t=2025-07-29T00:46:37+0800 lvl=info msg="join connections" obj=join id=29c8db91d665 l=127.0.0.1:8000 r=*************:42314
t=2025-07-29T00:47:01+0800 lvl=info msg="join connections" obj=join id=670cd4348ee5 l=127.0.0.1:8000 r=**************:27570
t=2025-07-29T00:47:29+0800 lvl=info msg="join connections" obj=join id=296f541e64bc l=127.0.0.1:8000 r=*************:48168
t=2025-07-29T00:47:54+0800 lvl=info msg="join connections" obj=join id=dcc742655cef l=127.0.0.1:8000 r=34.238.148.169:22868
t=2025-07-29T00:48:25+0800 lvl=info msg="join connections" obj=join id=1de980816558 l=127.0.0.1:8000 r=50.19.198.176:23792
t=2025-07-29T00:49:20+0800 lvl=info msg="join connections" obj=join id=7b8cbb21fff0 l=127.0.0.1:8000 r=18.206.195.2:23084
t=2025-07-29T00:49:59+0800 lvl=info msg="join connections" obj=join id=edd1080d9480 l=127.0.0.1:8000 r=98.81.5.17:64024
t=2025-07-29T00:50:42+0800 lvl=info msg="join connections" obj=join id=6324b35050c0 l=127.0.0.1:8000 r=3.89.183.0:33924
t=2025-07-29T00:51:19+0800 lvl=info msg="join connections" obj=join id=cad44ef46b6b l=127.0.0.1:8000 r=54.80.58.180:23508
t=2025-07-29T00:51:57+0800 lvl=info msg="join connections" obj=join id=2253c32d2038 l=127.0.0.1:8000 r=13.218.208.59:49360
t=2025-07-29T00:52:37+0800 lvl=info msg="join connections" obj=join id=1cf07c97d62d l=127.0.0.1:8000 r=13.222.252.60:44622
t=2025-07-29T00:53:17+0800 lvl=info msg="join connections" obj=join id=ff9d8329fb31 l=127.0.0.1:8000 r=18.204.222.161:36440
t=2025-07-29T00:53:48+0800 lvl=info msg="join connections" obj=join id=9e1c86b9339a l=127.0.0.1:8000 r=54.197.24.191:44312
t=2025-07-29T00:54:28+0800 lvl=info msg="join connections" obj=join id=5660891eb69a l=127.0.0.1:8000 r=13.221.90.181:40350
t=2025-07-29T00:54:45+0800 lvl=info msg="join connections" obj=join id=ad79dd6c375b l=127.0.0.1:8000 r=107.23.247.229:59714
t=2025-07-29T00:54:45+0800 lvl=info msg="join connections" obj=join id=843be06d0166 l=127.0.0.1:8000 r=34.234.65.228:24502
t=2025-07-29T01:13:01+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T01:13:03+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T01:13:03+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T01:13:24+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=db42748d00d5 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T01:31:52+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T01:31:54+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T01:31:54+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T01:32:09+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ed198dd51688 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T01:47:48+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T01:47:50+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T01:47:50+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T01:48:07+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3f6bddba93ba clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T01:55:43+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T01:55:45+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T01:55:45+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T01:56:06+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3765e13a2d70 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T02:12:18+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T02:12:20+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T02:12:20+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T02:12:43+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f135dd73e488 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T02:29:28+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T02:29:30+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T02:29:30+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T02:29:47+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=73e27fbc7f32 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T02:30:48+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T02:30:50+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T02:30:50+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T02:31:03+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9446b1ff7583 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T02:47:36+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T02:47:38+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T02:47:38+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T02:47:58+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a0f5615c8e90 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T03:09:58+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c49fd0aea754 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T03:09:58+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="session closed"
t=2025-07-29T03:10:00+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T03:10:00+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T03:14:40+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T03:14:42+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T03:14:42+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T03:14:55+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0bc2cecb7e42 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T03:31:06+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T03:31:08+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T03:31:08+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T03:31:23+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=57adfc096855 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T03:52:54+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T03:52:56+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T03:52:56+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T03:53:16+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=04a6cddbcac1 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T04:10:36+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T04:10:38+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T04:10:38+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T04:10:56+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=065f96798d35 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T04:15:36+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T04:15:38+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T04:15:38+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T04:15:51+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e46a71a844d2 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T05:32:33+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T05:32:35+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T05:32:35+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T05:32:49+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e424e6f63616 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T05:49:34+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T05:49:36+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T05:49:36+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T05:49:50+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ee42a6637edb clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T06:05:45+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T06:05:47+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T06:05:47+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T06:06:04+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=da1a50606055 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T06:16:32+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T06:16:34+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T06:16:34+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T06:16:51+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d65f5253ed34 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T06:32:33+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T06:32:34+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T06:32:34+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T06:32:51+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=faab22302fc0 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T06:50:29+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T06:50:31+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T06:50:31+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T06:50:46+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=49e28a20bc30 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T07:06:47+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T07:06:49+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T07:06:49+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T07:07:06+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d43810f052d5 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T07:17:32+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T07:17:34+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T07:17:34+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T07:17:53+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=0cfc73546144 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T08:34:08+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T08:34:10+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T08:34:10+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T08:34:29+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ac4841cfe609 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T08:52:44+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T08:52:46+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T08:52:46+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T08:53:01+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3109e3b9f308 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T10:08:43+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T10:08:45+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T10:08:45+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T10:09:01+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=788dfac61c31 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T10:17:50+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T10:17:52+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T10:17:52+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T10:18:07+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=96ff35b83106 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T12:17:21+0800 lvl=info msg="update available" obj=updater
t=2025-07-29T13:38:41+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T13:38:43+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T13:38:43+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T13:38:56+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=382f6d6aa5a1 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T13:55:14+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=dd231fe9e0ea clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T13:55:14+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="session closed"
t=2025-07-29T13:55:17+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T13:55:17+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T14:10:39+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T14:10:42+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T14:10:42+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T14:11:01+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=16e461ab4a38 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T14:26:48+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T14:26:51+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T14:26:51+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T14:27:12+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=cd0927e0e15d clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T14:44:54+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T14:44:57+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T14:44:57+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T14:45:13+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2f679079fce7 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T14:47:59+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=dfe1d669ab52 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T14:47:59+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="session closed"
t=2025-07-29T14:48:01+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T14:48:01+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T19:22:28+0800 lvl=info msg="update available" obj=updater
t=2025-07-29T20:39:23+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T20:39:26+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T20:39:26+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T20:39:47+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=6a8b90d11fcd clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T20:56:16+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T20:56:22+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T20:56:22+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T20:56:36+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=11eee41d61a4 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T21:12:50+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T21:12:52+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T21:12:52+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T21:13:09+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fc2366610a2a clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T23:05:48+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=bb0aae75bbb4 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-29T23:05:48+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="session closed"
t=2025-07-29T23:05:52+0800 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: EOF"
t=2025-07-29T23:06:06+0800 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: session closed"
t=2025-07-29T23:06:17+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T23:06:17+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T23:46:06+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-29T23:46:08+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-29T23:46:08+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-29T23:46:21+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=033d1f57297d clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T01:03:23+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T01:03:25+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T01:03:25+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T01:03:45+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=4299d01c21c1 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T01:20:25+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T01:20:27+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T01:20:27+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T01:20:43+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3d978db23e71 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T01:28:01+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T01:28:03+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T01:28:03+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T01:28:21+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8e71e8ff8395 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T01:43:53+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T01:43:55+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T01:43:55+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T01:44:12+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=7df141c80fca clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T02:01:06+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T02:01:08+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T02:01:08+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T02:01:29+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a73f9e9b54d8 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T02:17:56+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T02:17:58+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T02:17:58+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T02:18:12+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a039b6d32c60 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T02:29:01+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T02:29:03+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T02:29:03+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T02:29:20+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=5add2426db7e clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T02:34:51+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T02:34:52+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T02:34:52+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T02:35:06+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=bc157a426e21 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T02:51:32+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T02:51:34+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T02:51:34+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T02:51:51+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=053d642b1acc clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T03:08:16+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T03:08:18+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T03:08:18+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T03:08:35+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1e2589345cf4 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T03:26:57+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T03:26:59+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T03:26:59+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T03:27:17+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d67af6529d79 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T03:45:22+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T03:45:24+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T03:45:24+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T03:45:41+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d174f2bfffcc clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T04:06:31+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T04:06:33+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T04:06:33+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T04:06:48+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=78bb9d3c024d clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T04:29:59+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T04:30:01+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T04:30:01+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T04:30:19+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ff1d6f5cd469 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T04:47:37+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T04:47:39+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T04:47:39+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T04:47:56+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=04a7b2b44e08 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T06:04:03+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T06:04:05+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T06:04:05+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T06:04:22+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=73c843329cf4 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T06:22:33+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T06:22:35+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T06:22:35+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T06:22:52+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9e5106c6d600 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T06:25:00+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T06:25:02+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T06:25:02+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T06:25:15+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=3f0d17a8e9bd clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T06:41:21+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1e944ee52182 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T06:41:21+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="session closed"
t=2025-07-30T06:41:23+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T06:41:23+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T06:49:59+0800 lvl=info msg="update available" obj=updater
t=2025-07-30T07:57:49+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T07:57:51+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T07:57:51+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T07:58:11+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a3a75514f90a clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T09:13:48+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T09:13:50+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T09:13:50+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T09:14:11+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=c62f1bf3530c clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T09:25:56+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T09:25:58+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T09:25:58+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T09:26:16+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=eca5513b9df9 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T09:43:04+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T09:43:06+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T09:43:06+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T09:43:23+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ad5c0349e306 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T09:59:54+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T09:59:56+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T09:59:56+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T10:00:15+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=381b0835b179 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T10:16:38+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T10:16:40+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T10:16:40+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T10:16:57+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a222e1a19b36 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T10:26:56+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T10:26:58+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T10:26:58+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T10:27:15+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e0e1bc545d68 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T11:43:36+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T11:43:38+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T11:43:38+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T11:43:56+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fad30e16dfb5 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T12:48:07+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T12:48:09+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T12:48:09+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T12:48:22+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1626ad1521dd clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T14:50:07+0800 lvl=info msg="update available" obj=updater
t=2025-07-30T19:34:26+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=bcd77654036b clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T19:34:26+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="session closed"
t=2025-07-30T19:34:28+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T19:34:28+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T20:50:09+0800 lvl=info msg="update available" obj=updater
t=2025-07-30T21:10:03+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T21:10:05+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T21:10:05+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T21:10:20+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=685453181ee0 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T21:26:59+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T21:27:01+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T21:27:01+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T21:27:17+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f23e6d142475 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T21:44:54+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T21:44:58+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T21:44:58+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T21:45:16+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8b9ea496bee0 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T22:01:36+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b9bf775062b4 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T22:01:36+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="session closed"
t=2025-07-30T22:01:37+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T22:01:37+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T22:09:15+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T22:09:19+0800 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: EOF"
t=2025-07-30T22:09:24+0800 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: EOF"
t=2025-07-30T22:09:29+0800 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: EOF"
t=2025-07-30T22:09:30+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fd062bdcd907 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-30T22:09:35+0800 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: EOF"
t=2025-07-30T22:09:43+0800 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: EOF"
t=2025-07-30T22:09:55+0800 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: EOF"
t=2025-07-30T22:10:11+0800 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: EOF"
t=2025-07-30T22:10:41+0800 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: EOF"
t=2025-07-30T22:11:11+0800 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: EOF"
t=2025-07-30T22:11:43+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T22:11:43+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T22:39:28+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-30T22:39:30+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-30T22:39:30+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-30T22:39:53+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1c2a7d8c4541 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T03:13:34+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T03:13:36+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T03:13:36+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T03:13:58+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=d52d34a7ca4f clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T03:30:55+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T03:30:56+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T03:30:56+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T03:31:13+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=e9e2e857a6ee clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T03:52:25+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T03:52:26+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T03:52:26+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T03:52:41+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=cb0fbbd414c7 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T04:10:50+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T04:10:51+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T04:10:51+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T04:11:09+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=63c598323f53 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T04:26:41+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T04:26:42+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T04:26:42+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T04:27:00+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=01a74bf4879c clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T04:45:04+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T04:45:06+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T04:45:06+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T04:45:20+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9e159310a214 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T04:55:25+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T04:55:27+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T04:55:27+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T04:55:45+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=69de9a46b228 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T05:12:54+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T05:12:56+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T05:12:56+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T05:13:12+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=be67a4518442 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T05:30:04+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T05:30:06+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T05:30:06+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T05:30:21+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b9fea5e56e48 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T05:46:18+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T05:46:20+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T05:46:20+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T05:46:37+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=8d0e1dc23667 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T05:56:26+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T05:56:28+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T05:56:28+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T05:56:49+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=66e186b97be0 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T06:14:32+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T06:14:33+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T06:14:33+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T06:14:52+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=ed5984087fdf clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T06:32:17+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T06:32:19+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T06:32:19+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T06:32:39+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=19882804f2a3 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T06:50:04+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T06:50:05+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T06:50:05+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T06:50:22+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=508dc10c63f8 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T06:57:27+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T06:57:28+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T06:57:28+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T06:57:47+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=57ab41f0b3e1 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T07:14:58+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T07:15:00+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T07:15:00+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T07:15:17+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=541a7ded33cb clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T07:31:02+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T07:31:03+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T07:31:03+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T07:31:21+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=870f195f4804 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T07:50:52+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T07:50:53+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T07:50:53+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T07:51:15+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=1a3cd41e4547 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T07:58:28+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T07:58:30+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T07:58:30+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T07:58:46+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=2d5b3ebf1e3f clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T08:01:23+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T08:01:24+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T08:01:24+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T08:01:38+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b492d06220f3 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T08:19:18+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T08:19:19+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T08:19:19+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T08:19:33+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=4983417607b6 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T08:37:23+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T08:37:24+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T08:37:24+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T08:37:45+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=4a78f7f49427 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T08:54:28+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T08:54:29+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T08:54:29+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T08:54:47+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=a6259ec208bd clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T08:59:32+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T08:59:33+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T08:59:33+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T08:59:47+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=22146003e519 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T09:16:58+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T09:16:59+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T09:16:59+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T09:17:16+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=9d57d6b29ef7 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T09:24:32+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=fecd5d3ff05a clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T09:24:32+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="session closed"
t=2025-07-31T09:24:34+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T09:24:34+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T09:43:58+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T09:43:59+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T09:43:59+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T09:44:13+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=61332a26f0c5 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T10:00:30+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T10:00:31+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T10:00:31+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T10:00:52+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=b51f33b10942 clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T10:04:19+0800 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=5a35fa1cba64 err="read EOF from remote peer"
t=2025-07-31T10:04:20+0800 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-31T10:04:20+0800 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-31T10:04:34+0800 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=84977cef5d5c clientid=85d25bb38573972576bc0fe9332ad661
t=2025-07-31T10:16:00+0800 lvl=info msg="update available" obj=updater
t=2025-07-31T12:24:24+0800 lvl=info msg="join connections" obj=join id=f6caaed8b341 l=127.0.0.1:8000 r=13.222.192.44:37340
t=2025-07-31T12:24:25+0800 lvl=info msg="join connections" obj=join id=6ddc3d5faf17 l=127.0.0.1:8000 r=34.227.75.182:34792
t=2025-07-31T12:24:28+0800 lvl=info msg="join connections" obj=join id=11b6280c7115 l=127.0.0.1:8000 r=54.163.219.89:20384
t=2025-07-31T12:24:28+0800 lvl=info msg="join connections" obj=join id=541b104f0945 l=127.0.0.1:8000 r=54.198.28.63:29406
t=2025-07-31T12:24:50+0800 lvl=info msg="join connections" obj=join id=8269c39de5dc l=127.0.0.1:8000 r=3.92.50.80:26564
t=2025-07-31T12:25:10+0800 lvl=info msg="join connections" obj=join id=bf92a48e66a3 l=127.0.0.1:8000 r=3.86.237.27:62010
t=2025-07-31T12:29:24+0800 lvl=info msg="join connections" obj=join id=0ff5806c7d27 l=127.0.0.1:8000 r=*************:55984
