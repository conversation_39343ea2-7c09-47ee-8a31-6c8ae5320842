# Redis 配置文件 - 用于AI Dating应用

# 基础配置
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 日志配置
loglevel notice
logfile ""

# 数据库数量
databases 16

# 安全配置（开发环境）
# requirepass your_redis_password

# 性能优化
tcp-backlog 511
timeout 0
tcp-keepalive 300

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端连接
maxclients 10000

# AOF持久化（可选）
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# 压缩配置
rdbcompression yes
rdbchecksum yes

# 内存使用优化
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
