"""
ConversationRelay WebSocket API
处理 Twilio ConversationRelay 的实时语音交互
"""

import logging
import json
import asyncio
import time
import random
from typing import Dict, Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.responses import Response
import sys
import os

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/conversation-relay", tags=["ConversationRelay"])

# 活跃的WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.session_data: Dict[str, dict] = {}
    
    async def connect(self, websocket: WebSocket, session_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[session_id] = websocket
        logger.info(f"ConversationRelay connected: {session_id}")
    
    def disconnect(self, session_id: str):
        """断开WebSocket连接"""
        if session_id in self.active_connections:
            del self.active_connections[session_id]
        if session_id in self.session_data:
            del self.session_data[session_id]
        logger.info(f"ConversationRelay disconnected: {session_id}")
    
    async def send_message(self, session_id: str, message: dict):
        """发送消息到WebSocket"""
        if session_id in self.active_connections:
            websocket = self.active_connections[session_id]
            try:
                await websocket.send_text(json.dumps(message))
                return True
            except Exception as e:
                logger.error(f"Failed to send message to {session_id}: {e}")
                return False
        return False
    
    def get_session_data(self, session_id: str) -> Optional[dict]:
        """获取会话数据"""
        return self.session_data.get(session_id)
    
    def set_session_data(self, session_id: str, data: dict):
        """设置会话数据"""
        self.session_data[session_id] = data

# 全局连接管理器
manager = ConnectionManager()

def get_websocket_url() -> str:
    """获取WebSocket URL - 根据环境配置"""
    base_url = os.getenv("TWILIO_WEBHOOK_URL", "https://localhost:8000")

    # 将 https 替换为 wss 用于 WebSocket
    if base_url.startswith("https://"):
        websocket_url = base_url.replace("https://", "wss://")
    elif base_url.startswith("http://"):
        websocket_url = base_url.replace("http://", "ws://")
    else:
        websocket_url = f"wss://{base_url}"

    # 添加 WebSocket 路径
    if not websocket_url.endswith("/"):
        websocket_url += "/"
    websocket_url += "conversation-relay/voice"

    return websocket_url

def get_agent():
    """获取Agent实例"""
    try:
        from main import agent_instance
        if agent_instance is None:
            raise HTTPException(status_code=503, detail="Agent service not available")
        return agent_instance
    except ImportError:
        raise HTTPException(status_code=503, detail="Agent service not initialized")

@router.websocket("/voice")
async def conversation_relay_websocket(websocket: WebSocket):
    """
    ConversationRelay WebSocket端点
    处理Twilio ConversationRelay的实时语音交互
    """
    session_id = None
    
    try:
        # 等待连接
        await websocket.accept()
        logger.info("ConversationRelay WebSocket connection established")
        
        # 获取Agent实例
        agent = get_agent()
        
        async for message in websocket.iter_text():
            try:
                # 解析消息
                data = json.loads(message)
                message_type = data.get("type")
                
                logger.info(f"Received ConversationRelay message: {message_type}")
                logger.debug(f"Message data: {data}")
                
                if message_type == "setup":
                    # 处理初始化消息
                    session_id = data.get("sessionId")
                    call_sid = data.get("callSid")
                    
                    if not session_id or not call_sid:
                        logger.error("Missing sessionId or callSid in setup message")
                        continue
                    
                    # 注册连接
                    manager.active_connections[session_id] = websocket
                    
                    # 初始化会话数据
                    session_data = {
                        "session_id": session_id,
                        "call_sid": call_sid,
                        "call_status": "active",
                        "user_id": None,  # 需要从call_sid查找
                        "conversation_context": {}
                    }
                    manager.set_session_data(session_id, session_data)
                    
                    # 处理初始化
                    await handle_setup_message(agent, data, websocket)
                
                elif message_type == "prompt":
                    # 处理用户语音输入
                    if session_id:
                        await handle_prompt_message(agent, session_id, data, websocket)
                    else:
                        logger.error("Received prompt message without session setup")
                
                elif message_type == "interrupt":
                    # 处理用户中断
                    if session_id:
                        await handle_interrupt_message(agent, session_id, data, websocket)
                
                elif message_type == "dtmf":
                    # 处理DTMF按键
                    if session_id:
                        await handle_dtmf_message(agent, session_id, data, websocket)
                
                else:
                    logger.warning(f"Unknown message type: {message_type}")
                    
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse WebSocket message: {e}")
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                
    except WebSocketDisconnect:
        logger.info("ConversationRelay WebSocket disconnected")
    except Exception as e:
        logger.error(f"ConversationRelay WebSocket error: {e}")
    finally:
        # 清理连接
        if session_id:
            manager.disconnect(session_id)

async def handle_setup_message(agent, data: dict, websocket: WebSocket):
    """处理初始化消息"""
    try:
        session_id = data.get("sessionId")
        call_sid = data.get("callSid")
        
        logger.info(f"Setting up ConversationRelay session: {session_id}")
        
        # 从call_sid查找用户信息
        # 这里需要实现根据call_sid查找用户的逻辑
        user_id = await find_user_by_call_sid(agent, call_sid)
        
        if user_id:
            # 更新会话数据
            session_data = manager.get_session_data(session_id)
            if session_data:
                session_data["user_id"] = user_id
                manager.set_session_data(session_id, session_data)
            
            # 发送欢迎消息
            welcome_message = {
                "type": "text",
                "token": "Hi! I'm here to help you create your dating profile. Let's start with a quick conversation."
            }
            await websocket.send_text(json.dumps(welcome_message))
        else:
            logger.error(f"Could not find user for call_sid: {call_sid}")
            # 发送错误消息并结束会话
            error_message = {
                "type": "text", 
                "token": "Sorry, there was an error identifying your account. Please try again later."
            }
            await websocket.send_text(json.dumps(error_message))
            
            # 结束会话
            end_message = {"type": "endSession"}
            await websocket.send_text(json.dumps(end_message))
            
    except Exception as e:
        logger.error(f"Error in setup message handler: {e}")

async def handle_prompt_message(agent, session_id: str, data: dict, websocket: WebSocket):
    """处理用户语音输入消息"""
    try:
        user_text = data.get("text", "")
        confidence = data.get("confidence", 1.0)
        
        logger.info(f"Processing user input: {user_text} (confidence: {confidence})")
        
        # 渐进式响应策略
        await send_progressive_response(websocket, user_text)
        
        # 获取会话数据
        session_data = manager.get_session_data(session_id)
        if not session_data or not session_data.get("user_id"):
            logger.error(f"No session data or user_id for session: {session_id}")
            return
        
        user_id = session_data["user_id"]
        
        # 使用现有的ConversationAgent处理输入
        # 这里需要适配现有的agent接口
        context = session_data.get("conversation_context", {})
        
        # 调用现有的语音处理逻辑
        result = await process_conversation_input(agent, user_id, user_text, context)
        
        if result.get("success"):
            # 发送AI回复
            response_message = {
                "type": "text",
                "token": result.get("response", "Thank you for sharing that.")
            }
            await websocket.send_text(json.dumps(response_message))
            
            # 更新会话上下文
            session_data["conversation_context"] = result.get("context", {})
            manager.set_session_data(session_id, session_data)
            
            # 检查是否需要结束会话
            if result.get("should_end"):
                end_message = {"type": "endSession"}
                await websocket.send_text(json.dumps(end_message))
        else:
            # 发送错误回复
            error_message = {
                "type": "text",
                "token": "I'm sorry, I didn't quite catch that. Could you please repeat?"
            }
            await websocket.send_text(json.dumps(error_message))
            
    except Exception as e:
        logger.error(f"Error in prompt message handler: {e}")

async def send_progressive_response(websocket: WebSocket, user_text: str):
    """发送渐进式响应，填补AI处理时间"""
    try:
        # 单层：即时确认
        immediate_responses = [
            "I see...", "Mm-hmm...", "Interesting...", "Got it...",
            "Right...", "Okay...", "I understand..."
        ]

        # 根据用户输入选择合适的确认
        if len(user_text) > 50:
            ack = "I see, that's quite detailed..."
        elif "?" in user_text:
            ack = "That's a good question..."
        else:
            ack = random.choice(immediate_responses)

        await websocket.send_text(json.dumps({
            "type": "text",
            "token": ack
        }))

    except Exception as e:
        logger.error(f"Error in progressive response: {e}")

async def handle_interrupt_message(agent, session_id: str, data: dict, websocket: WebSocket):
    """处理用户中断消息"""
    try:
        logger.info(f"User interrupted in session: {session_id}")
        
        # 停止当前的TTS播放
        # ConversationRelay会自动处理中断，我们只需要准备好接收新的输入
        
        # 可以发送一个简短的确认
        ack_message = {
            "type": "text",
            "token": "Yes?"
        }
        await websocket.send_text(json.dumps(ack_message))
        
    except Exception as e:
        logger.error(f"Error in interrupt message handler: {e}")

async def handle_dtmf_message(agent, session_id: str, data: dict, websocket: WebSocket):
    """处理DTMF按键消息"""
    try:
        digit = data.get("digit")
        logger.info(f"DTMF digit pressed: {digit} in session: {session_id}")
        
        # 根据按键处理不同的逻辑
        if digit == "*":
            # 例如：重复上一个问题
            repeat_message = {
                "type": "text",
                "token": "Let me repeat the question..."
            }
            await websocket.send_text(json.dumps(repeat_message))
        elif digit == "#":
            # 例如：跳过当前问题
            skip_message = {
                "type": "text", 
                "token": "Okay, let's move on to the next question."
            }
            await websocket.send_text(json.dumps(skip_message))
            
    except Exception as e:
        logger.error(f"Error in DTMF message handler: {e}")

async def find_user_by_call_sid(agent, call_sid: str) -> Optional[str]:
    """根据call_sid查找用户ID"""
    try:
        # 这里需要实现查找逻辑
        # 可能需要在数据库中查找call_sid对应的用户
        # 或者从agent的现有方法中获取
        
        # 临时实现 - 需要根据实际的agent接口调整
        result = await agent.get_user_by_call_sid(call_sid)
        if result and result.get("success"):
            return result.get("user_id")
        return None
        
    except Exception as e:
        logger.error(f"Error finding user by call_sid {call_sid}: {e}")
        return None

async def process_conversation_input(agent, user_id: str, user_text: str, context: dict) -> dict:
    """处理对话输入 - 使用VoiceService的ConversationRelay支持"""
    try:
        # 获取call_sid从context或生成一个临时的
        call_sid = context.get("call_sid", f"relay_{user_id}_{int(time.time())}")

        # 使用VoiceService的新ConversationRelay方法
        result = await agent.process_voice_conversation(user_id, user_text, call_sid)

        return {
            "success": result.get("success", True),
            "response": result.get("response", "Thank you for sharing that."),
            "context": context,  # 保持原有context结构
            "should_end": result.get("should_end", False),
            "confidence": result.get("confidence", 0.8)
        }

    except Exception as e:
        logger.error(f"Error processing conversation input: {e}")
        return {
            "success": False,
            "response": "I'm sorry, there was an error processing your response.",
            "context": context,
            "should_end": False,
            "confidence": 0.3
        }

# TwiML端点 - 用于启动ConversationRelay
@router.post("/twiml/start")
async def start_conversation_relay():
    """
    返回启动ConversationRelay的TwiML
    """
    try:
        logger.info("🚀 ConversationRelay TwiML endpoint called!")
        logger.info("📞 Incoming call - starting ConversationRelay")
        # 获取WebSocket URL - 根据环境配置
        websocket_url = get_websocket_url()

        logger.info(f"ConversationRelay WebSocket URL: {websocket_url}")
        
        twiml = f'''<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Connect>
        <ConversationRelay url="{websocket_url}" 
                          welcomeGreeting="Hi! I'm here to help you create your dating profile."
                          interruptible="true"
                          language="en-US"
                          ttsProvider="ElevenLabs"
                          transcriptionProvider="Google" />
    </Connect>
</Response>'''
        
        return Response(content=twiml, media_type="application/xml")
        
    except Exception as e:
        logger.error(f"Error generating ConversationRelay TwiML: {e}")
        # 返回错误TwiML
        error_twiml = '''<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say>Sorry, there was an error starting the conversation. Please try again later.</Say>
    <Hangup/>
</Response>'''
        return Response(content=error_twiml, media_type="application/xml")
