025-07-27 13:38:33,474 - main - INFO - Agent system initialized with 8 components
2025-07-27 13:38:33,474 - main - INFO - Agent initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:54041 - "GET /voice/status/cfebf3ac-7e66-4a6a-91ed-a804b4300c2b HTTP/1.1" 200 OK
INFO:     127.0.0.1:54041 - "OPTIONS /voice/initiate/cfebf3ac-7e66-4a6a-91ed-a804b4300c2b HTTP/1.1" 200 OK
2025-07-27 13:39:02,449 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-07-27 13:39:02,449 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Calls.json
2025-07-27 13:39:02,449 - twilio.http_client - INFO - Headers:
2025-07-27 13:39:02,449 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-07-27 13:39:02,449 - twilio.http_client - INFO - Accept : application/json
2025-07-27 13:39:02,450 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.5 (Darwin arm64) Python/3.12.2
2025-07-27 13:39:02,450 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.5
2025-07-27 13:39:02,450 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-07-27 13:39:02,450 - twilio.http_client - INFO - -- END Twilio API Request --
2025-07-27 13:39:04,379 - twilio.http_client - INFO - Response Status Code: 201
2025-07-27 13:39:04,380 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '2011', 'Connection': 'keep-alive', 'Date': 'Sun, 27 Jul 2025 05:39:05 GMT', 'Location': 'https://call-service.us1.svc.twilio.com/v2/Accounts/CA4cb2e48f3681b5a9453621d57bc90f1a', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ616b6107db9e0fc1aa9e0ed62728624f', 'Twilio-Request-Duration': '0.090', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=********', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 8eba96584a898f8a357fb6316ffd8f16.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'LAX50-P3', 'X-Amz-Cf-Id': 'ROWDw0iWx-Hj3mouNHVn78iQ02-hjNvVx1Hk8T9jueeXlMCzgIO65w==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-07-27 13:39:04,382 - agent.services.voice_service - INFO - Outbound call initiated: CA4cb2e48f3681b5a9453621d57bc90f1a to +***********
INFO:     127.0.0.1:54041 - "POST /voice/initiate/cfebf3ac-7e66-4a6a-91ed-a804b4300c2b HTTP/1.1" 200 OK
2025-07-27 13:39:11,483 - api.voice - INFO - Outgoing call webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'CallerState': 'CA', 'ToZip': '', 'StirStatus': 'B', 'CallSid': 'CA4cb2e48f3681b5a9453621d57bc90f1a', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'StirVerstat': 'TN-Validation-Passed-B', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': '**********************************', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-27 13:39:11,483 - backend.database.connection - INFO - Async session factory created
2025-07-27 13:39:11,493 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:39:11,493 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:39:11,495 INFO sqlalchemy.engine.Engine SELECT * FROM users WHERE phone_number = $1
2025-07-27 13:39:11,495 - sqlalchemy.engine.Engine - INFO - SELECT * FROM users WHERE phone_number = $1
2025-07-27 13:39:11,495 INFO sqlalchemy.engine.Engine [generated in 0.00027s] ('+***********',)
2025-07-27 13:39:11,495 - sqlalchemy.engine.Engine - INFO - [generated in 0.00027s] ('+***********',)
2025-07-27 13:39:11,506 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-27 13:39:11,506 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-27 13:39:11,512 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:39:11,512 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:39:11,513 INFO sqlalchemy.engine.Engine 
                        INSERT INTO voice_sessions (
                            id, user_id, twilio_call_sid, session_data, session_status,
                            last_activity, from_number, current_stage, questions_asked,
                            responses_received, created_at
                        ) VALUES (
                            uuid_generate_v4(), $1, $2, $3,
                            'active', NOW(), $4, $5, $6,
                            $7, NOW()
                        )
                        ON CONFLICT (twilio_call_sid) DO UPDATE SET
                            session_data = EXCLUDED.session_data,
                            last_activity = NOW(),
                            current_stage = EXCLUDED.current_stage,
                            questions_asked = EXCLUDED.questions_asked,
                            responses_received = EXCLUDED.responses_received
                    
2025-07-27 13:39:11,513 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO voice_sessions (
                            id, user_id, twilio_call_sid, session_data, session_status,
                            last_activity, from_number, current_stage, questions_asked,
                            responses_received, created_at
                        ) VALUES (
                            uuid_generate_v4(), $1, $2, $3,
                            'active', NOW(), $4, $5, $6,
                            $7, NOW()
                        )
                        ON CONFLICT (twilio_call_sid) DO UPDATE SET
                            session_data = EXCLUDED.session_data,
                            last_activity = NOW(),
                            current_stage = EXCLUDED.current_stage,
                            questions_asked = EXCLUDED.questions_asked,
                            responses_received = EXCLUDED.responses_received
                    
2025-07-27 13:39:11,513 INFO sqlalchemy.engine.Engine [generated in 0.00028s] ('cfebf3ac-7e66-4a6a-91ed-a804b4300c2b', 'CA4cb2e48f3681b5a9453621d57bc90f1a', '{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (146 characters truncated) ... info": {}, "stage_attempts": {}, "last_question": "", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', '+***********', 'greeting', 0, 0)
2025-07-27 13:39:11,513 - sqlalchemy.engine.Engine - INFO - [generated in 0.00028s] ('cfebf3ac-7e66-4a6a-91ed-a804b4300c2b', 'CA4cb2e48f3681b5a9453621d57bc90f1a', '{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (146 characters truncated) ... info": {}, "stage_attempts": {}, "last_question": "", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', '+***********', 'greeting', 0, 0)
2025-07-27 13:39:11,519 INFO sqlalchemy.engine.Engine COMMIT
2025-07-27 13:39:11,519 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-27 13:39:11,521 - agent.services.voice_service - INFO - Session saved to database: CA4cb2e48f3681b5a9453621d57bc90f1a
2025-07-27 13:39:11,523 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:39:11,523 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:39:11,523 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:39:11,523 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:39:11,524 INFO sqlalchemy.engine.Engine [generated in 0.00025s] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (624 characters truncated) ... , and I\'ll ask you a few questions about yourself.", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'greeting', 1, 0, 'CA4cb2e48f3681b5a9453621d57bc90f1a')
2025-07-27 13:39:11,524 - sqlalchemy.engine.Engine - INFO - [generated in 0.00025s] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (624 characters truncated) ... , and I\'ll ask you a few questions about yourself.", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'greeting', 1, 0, 'CA4cb2e48f3681b5a9453621d57bc90f1a')
2025-07-27 13:39:11,526 INFO sqlalchemy.engine.Engine COMMIT
2025-07-27 13:39:11,526 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-27 13:39:11,527 - agent.services.voice_service - INFO - Started outgoing voice interview for user cfebf3ac-7e66-4a6a-91ed-a804b4300c2b, call CA4cb2e48f3681b5a9453621d57bc90f1a
INFO:     *************:0 - "POST /voice/webhook/outgoing HTTP/1.1" 200 OK
2025-07-27 13:39:33,948 - api.voice - INFO - Speech webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'SpeechResult': 'Okay.', 'CallerState': 'CA', 'Language': 'en-US', 'ToZip': '', 'Confidence': '0.5610633', 'CallSid': 'CA4cb2e48f3681b5a9453621d57bc90f1a', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': '**********************************', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-27 13:39:33,948 - agent.services.voice_service - INFO - Processing speech input for call: CA4cb2e48f3681b5a9453621d57bc90f1a
2025-07-27 13:39:33,948 - agent.services.voice_service - INFO - User speech: 'Okay.' (confidence: 0.5610633)
2025-07-27 13:39:33,948 - agent.services.voice_service - INFO - Session updated - Questions: 1, Responses: 1
2025-07-27 13:39:33,948 - agent.services.voice_service - INFO - Generated next question: 'That sounds great! What kind of work do you do?'
2025-07-27 13:39:33,951 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:39:33,951 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:39:33,951 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:39:33,951 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:39:33,951 INFO sqlalchemy.engine.Engine [cached since 22.43s ago] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (736 characters truncated) ... ": "That sounds great! What kind of work do you do?", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'professional', 2, 1, 'CA4cb2e48f3681b5a9453621d57bc90f1a')
2025-07-27 13:39:33,951 - sqlalchemy.engine.Engine - INFO - [cached since 22.43s ago] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (736 characters truncated) ... ": "That sounds great! What kind of work do you do?", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'professional', 2, 1, 'CA4cb2e48f3681b5a9453621d57bc90f1a')
2025-07-27 13:39:33,954 INFO sqlalchemy.engine.Engine COMMIT
2025-07-27 13:39:33,954 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-27 13:39:33,958 - agent.services.voice_service - INFO - Returning TwiML for next question
INFO:     *************:0 - "POST /voice/webhook/speech HTTP/1.1" 200 OK
2025-07-27 13:39:52,567 - api.voice - INFO - Speech webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'SpeechResult': 'um,  I do.  Ux design.', 'CallerState': 'CA', 'Language': 'en-US', 'ToZip': '', 'Confidence': '0.7616724', 'CallSid': 'CA4cb2e48f3681b5a9453621d57bc90f1a', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': '**********************************', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-27 13:39:52,567 - agent.services.voice_service - INFO - Processing speech input for call: CA4cb2e48f3681b5a9453621d57bc90f1a
2025-07-27 13:39:52,567 - agent.services.voice_service - INFO - User speech: 'um,  I do.  Ux design.' (confidence: 0.7616724)
2025-07-27 13:39:52,567 - agent.services.voice_service - INFO - Session updated - Questions: 2, Responses: 2
2025-07-27 13:39:52,567 - agent.services.voice_service - INFO - Generated next question: 'Do you prefer hanging out with friends or having some alone time to recharge?'
2025-07-27 13:39:52,569 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:39:52,569 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:39:52,569 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:39:52,569 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:39:52,569 INFO sqlalchemy.engine.Engine [cached since 41.05s ago] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (1067 characters truncated) ... with friends or having some alone time to recharge?", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'personality', 3, 2, 'CA4cb2e48f3681b5a9453621d57bc90f1a')
2025-07-27 13:39:52,569 - sqlalchemy.engine.Engine - INFO - [cached since 41.05s ago] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (1067 characters truncated) ... with friends or having some alone time to recharge?", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'personality', 3, 2, 'CA4cb2e48f3681b5a9453621d57bc90f1a')
2025-07-27 13:39:52,571 INFO sqlalchemy.engine.Engine COMMIT
2025-07-27 13:39:52,571 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-27 13:39:52,574 - agent.services.voice_service - INFO - Returning TwiML for next question
INFO:     **************:0 - "POST /voice/webhook/speech HTTP/1.1" 200 OK
2025-07-27 13:39:59,438 - api.voice - INFO - Speech webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'SpeechResult': 'ER,', 'CallerState': 'CA', 'Language': 'en-US', 'ToZip': '', 'Confidence': '0.*********', 'CallSid': 'CA4cb2e48f3681b5a9453621d57bc90f1a', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': '**********************************', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-27 13:39:59,438 - agent.services.voice_service - INFO - Processing speech input for call: CA4cb2e48f3681b5a9453621d57bc90f1a
2025-07-27 13:39:59,438 - agent.services.voice_service - INFO - User speech: 'ER,' (confidence: 0.*********)
2025-07-27 13:39:59,438 - agent.services.voice_service - INFO - Session updated - Questions: 3, Responses: 3
2025-07-27 13:39:59,438 - agent.services.voice_service - INFO - Generated next question: 'Is there something you've always wanted to learn or try but haven't started yet?'
2025-07-27 13:39:59,442 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:39:59,442 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:39:59,442 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:39:59,442 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:39:59,442 INFO sqlalchemy.engine.Engine [cached since 47.92s ago] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (1355 characters truncated) ... ys wanted to learn or try but haven\'t started yet?", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'interests', 4, 3, 'CA4cb2e48f3681b5a9453621d57bc90f1a')
2025-07-27 13:39:59,442 - sqlalchemy.engine.Engine - INFO - [cached since 47.92s ago] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (1355 characters truncated) ... ys wanted to learn or try but haven\'t started yet?", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'interests', 4, 3, 'CA4cb2e48f3681b5a9453621d57bc90f1a')
2025-07-27 13:39:59,448 INFO sqlalchemy.engine.Engine COMMIT
2025-07-27 13:39:59,448 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-27 13:39:59,451 - agent.services.voice_service - INFO - Returning TwiML for next question
INFO:     ************:0 - "POST /voice/webhook/speech HTTP/1.1" 200 OK
2025-07-27 13:40:19,924 - api.voice - INFO - Speech webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'SpeechResult': 'um,  maybe became a  influencer like a YouTuber.', 'CallerState': 'CA', 'Language': 'en-US', 'ToZip': '', 'Confidence': '0.9836976', 'CallSid': 'CA4cb2e48f3681b5a9453621d57bc90f1a', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': '**********************************', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-27 13:40:19,924 - agent.services.voice_service - INFO - Processing speech input for call: CA4cb2e48f3681b5a9453621d57bc90f1a
2025-07-27 13:40:19,924 - agent.services.voice_service - INFO - User speech: 'um,  maybe became a  influencer like a YouTuber.' (confidence: 0.9836976)
2025-07-27 13:40:19,924 - agent.services.voice_service - INFO - Session updated - Questions: 4, Responses: 4
2025-07-27 13:40:19,924 - agent.services.voice_service - INFO - Generated next question: 'What does an ideal relationship look like to you?'
2025-07-27 13:40:19,929 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:40:19,929 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:40:19,929 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:40:19,929 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:40:19,929 INFO sqlalchemy.engine.Engine [cached since 68.41s ago] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (1625 characters truncated) ...  "What does an ideal relationship look like to you?", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'relationships', 5, 4, 'CA4cb2e48f3681b5a9453621d57bc90f1a')
2025-07-27 13:40:19,929 - sqlalchemy.engine.Engine - INFO - [cached since 68.41s ago] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (1625 characters truncated) ...  "What does an ideal relationship look like to you?", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'relationships', 5, 4, 'CA4cb2e48f3681b5a9453621d57bc90f1a')
2025-07-27 13:40:19,933 INFO sqlalchemy.engine.Engine COMMIT
2025-07-27 13:40:19,933 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-27 13:40:19,936 - agent.services.voice_service - INFO - Returning TwiML for next question
INFO:     **************:0 - "POST /voice/webhook/speech HTTP/1.1" 200 OK
2025-07-27 13:40:42,996 - api.voice - INFO - Speech webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'SpeechResult': 'um, being able to  um,  100% yourself into the relationship.', 'CallerState': 'CA', 'Language': 'en-US', 'ToZip': '', 'Confidence': '0.********', 'CallSid': 'CA4cb2e48f3681b5a9453621d57bc90f1a', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': '**********************************', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-27 13:40:42,996 - agent.services.voice_service - INFO - Processing speech input for call: CA4cb2e48f3681b5a9453621d57bc90f1a
2025-07-27 13:40:42,996 - agent.services.voice_service - INFO - User speech: 'um, being able to  um,  100% yourself into the relationship.' (confidence: 0.********)
2025-07-27 13:40:42,996 - agent.services.voice_service - INFO - Session updated - Questions: 5, Responses: 5
2025-07-27 13:40:42,996 - agent.services.voice_service - INFO - Generated next question: 'Perfect! I'll analyze our conversation and update your profile with the personality insights.'
2025-07-27 13:40:43,007 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:40:43,007 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:40:43,008 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:40:43,008 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:40:43,008 INFO sqlalchemy.engine.Engine [cached since 91.48s ago] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (2018 characters truncated) ...  update your profile with the personality insights.", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'closing', 6, 5, 'CA4cb2e48f3681b5a9453621d57bc90f1a')
2025-07-27 13:40:43,008 - sqlalchemy.engine.Engine - INFO - [cached since 91.48s ago] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (2018 characters truncated) ...  update your profile with the personality insights.", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'closing', 6, 5, 'CA4cb2e48f3681b5a9453621d57bc90f1a')
2025-07-27 13:40:43,011 INFO sqlalchemy.engine.Engine COMMIT
2025-07-27 13:40:43,011 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-27 13:40:43,019 - agent.services.voice_service - INFO - Returning TwiML for next question
INFO:     **************:0 - "POST /voice/webhook/speech HTTP/1.1" 200 OK
2025-07-27 13:40:59,779 - api.voice - INFO - Speech webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'SpeechResult': 'Nice.', 'CallerState': 'CA', 'Language': 'en-US', 'ToZip': '', 'Confidence': '0.********', 'CallSid': 'CA4cb2e48f3681b5a9453621d57bc90f1a', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': '**********************************', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-27 13:40:59,779 - agent.services.voice_service - INFO - Processing speech input for call: CA4cb2e48f3681b5a9453621d57bc90f1a
2025-07-27 13:40:59,779 - agent.services.voice_service - INFO - User speech: 'Nice.' (confidence: 0.********)
2025-07-27 13:40:59,779 - agent.services.voice_service - INFO - Session updated - Questions: 6, Responses: 6
2025-07-27 13:40:59,779 - agent.services.voice_service - INFO - Generated next question: 'None'
2025-07-27 13:40:59,781 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:40:59,781 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:40:59,781 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:40:59,781 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:40:59,781 INFO sqlalchemy.engine.Engine [cached since 108.3s ago] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (2122 characters truncated) ...  update your profile with the personality insights.", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'completed', 6, 6, 'CA4cb2e48f3681b5a9453621d57bc90f1a')
2025-07-27 13:40:59,781 - sqlalchemy.engine.Engine - INFO - [cached since 108.3s ago] ('{"call_sid": "CA4cb2e48f3681b5a9453621d57bc90f1a", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (2122 characters truncated) ...  update your profile with the personality insights.", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'completed', 6, 6, 'CA4cb2e48f3681b5a9453621d57bc90f1a')