2025-07-27 13:08:10,670 - backend.database.connection - INFO - Async session factory created
2025-07-27 13:08:10,674 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:08:10,674 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:08:10,675 INFO sqlalchemy.engine.Engine SELECT * FROM users WHERE phone_number = $1
2025-07-27 13:08:10,675 - sqlalchemy.engine.Engine - INFO - SELECT * FROM users WHERE phone_number = $1
2025-07-27 13:08:10,675 INFO sqlalchemy.engine.Engine [generated in 0.00020s] ('+***********',)
2025-07-27 13:08:10,675 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ('+***********',)
2025-07-27 13:08:10,679 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-27 13:08:10,679 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-27 13:08:10,682 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:08:10,682 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:08:10,683 INFO sqlalchemy.engine.Engine 
                        INSERT INTO voice_sessions (
                            id, user_id, twilio_call_sid, session_data, session_status,
                            last_activity, from_number, current_stage, questions_asked,
                            responses_received, created_at
                        ) VALUES (
                            uuid_generate_v4(), $1, $2, $3,
                            'active', NOW(), $4, $5, $6,
                            $7, NOW()
                        )
                        ON CONFLICT (twilio_call_sid) DO UPDATE SET
                            session_data = EXCLUDED.session_data,
                            last_activity = NOW(),
                            current_stage = EXCLUDED.current_stage,
                            questions_asked = EXCLUDED.questions_asked,
                            responses_received = EXCLUDED.responses_received
                    
2025-07-27 13:08:10,683 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO voice_sessions (
                            id, user_id, twilio_call_sid, session_data, session_status,
                            last_activity, from_number, current_stage, questions_asked,
                            responses_received, created_at
                        ) VALUES (
                            uuid_generate_v4(), $1, $2, $3,
                            'active', NOW(), $4, $5, $6,
                            $7, NOW()
                        )
                        ON CONFLICT (twilio_call_sid) DO UPDATE SET
                            session_data = EXCLUDED.session_data,
                            last_activity = NOW(),
                            current_stage = EXCLUDED.current_stage,
                            questions_asked = EXCLUDED.questions_asked,
                            responses_received = EXCLUDED.responses_received
                    
2025-07-27 13:08:10,683 INFO sqlalchemy.engine.Engine [generated in 0.00012s] ('cfebf3ac-7e66-4a6a-91ed-a804b4300c2b', 'CA6dd653811ccdb4b91f51d79100d67f27', '{"call_sid": "CA6dd653811ccdb4b91f51d79100d67f27", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (126 characters truncated) ... pt": [], "collected_info": {}, "stage_attempts": {}, "last_question": "", "recording_url": null, "recording_duration": 0, "call_status": "initiated"}', '+***********', 'greeting', 0, 0)
2025-07-27 13:08:10,683 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] ('cfebf3ac-7e66-4a6a-91ed-a804b4300c2b', 'CA6dd653811ccdb4b91f51d79100d67f27', '{"call_sid": "CA6dd653811ccdb4b91f51d79100d67f27", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (126 characters truncated) ... pt": [], "collected_info": {}, "stage_attempts": {}, "last_question": "", "recording_url": null, "recording_duration": 0, "call_status": "initiated"}', '+***********', 'greeting', 0, 0)
2025-07-27 13:08:10,689 INFO sqlalchemy.engine.Engine COMMIT
2025-07-27 13:08:10,689 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-27 13:08:10,690 - agent.services.voice_service - INFO - Session saved to database: CA6dd653811ccdb4b91f51d79100d67f27
2025-07-27 13:08:10,692 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:08:10,692 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:08:10,692 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:08:10,692 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:08:10,692 INFO sqlalchemy.engine.Engine [generated in 0.00011s] ('{"call_sid": "CA6dd653811ccdb4b91f51d79100d67f27", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (604 characters truncated) ... Just speak naturally, and I\'ll ask you a few questions about yourself.", "recording_url": null, "recording_duration": 0, "call_status": "initiated"}', 'greeting', 1, 0, 'CA6dd653811ccdb4b91f51d79100d67f27')
2025-07-27 13:08:10,692 - sqlalchemy.engine.Engine - INFO - [generated in 0.00011s] ('{"call_sid": "CA6dd653811ccdb4b91f51d79100d67f27", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (604 characters truncated) ... Just speak naturally, and I\'ll ask you a few questions about yourself.", "recording_url": null, "recording_duration": 0, "call_status": "initiated"}', 'greeting', 1, 0, 'CA6dd653811ccdb4b91f51d79100d67f27')
2025-07-27 13:08:10,694 INFO sqlalchemy.engine.Engine COMMIT
2025-07-27 13:08:10,694 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-27 13:08:10,696 - agent.services.voice_service - INFO - Started outgoing voice interview for user cfebf3ac-7e66-4a6a-91ed-a804b4300c2b, call CA6dd653811ccdb4b91f51d79100d67f27
INFO:     *************:0 - "POST /voice/webhook/outgoing HTTP/1.1" 200 OK
2025-07-27 13:08:29,679 - api.voice - INFO - Speech webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'SpeechResult': "Yes, I'm ready.", 'CallerState': 'CA', 'Language': 'en-US', 'ToZip': '', 'Confidence': '0.8074983', 'CallSid': 'CA6dd653811ccdb4b91f51d79100d67f27', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': 'AC98fddc0d9bf796ef17233cf22bbe31f5', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-27 13:08:29,679 - agent.services.voice_service - INFO - Processing speech input for call: CA6dd653811ccdb4b91f51d79100d67f27
2025-07-27 13:08:29,679 - agent.services.voice_service - INFO - User speech: 'Yes, I'm ready.' (confidence: 0.8074983)
2025-07-27 13:08:29,679 - agent.services.voice_service - INFO - Session updated - Questions: 1, Responses: 1
2025-07-27 13:08:29,679 - agent.services.voice_service - INFO - Generated next question: 'What's your role like day-to-day? Are you more hands-on or strategic?'
2025-07-27 13:08:29,682 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:08:29,682 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:08:29,682 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:08:29,682 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:08:29,682 INFO sqlalchemy.engine.Engine [cached since 18.99s ago] ('{"call_sid": "CA6dd653811ccdb4b91f51d79100d67f27", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (795 characters truncated) ... "What\'s your role like day-to-day? Are you more hands-on or strategic?", "recording_url": null, "recording_duration": 0, "call_status": "initiated"}', 'professional', 2, 1, 'CA6dd653811ccdb4b91f51d79100d67f27')
2025-07-27 13:08:29,682 - sqlalchemy.engine.Engine - INFO - [cached since 18.99s ago] ('{"call_sid": "CA6dd653811ccdb4b91f51d79100d67f27", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (795 characters truncated) ... "What\'s your role like day-to-day? Are you more hands-on or strategic?", "recording_url": null, "recording_duration": 0, "call_status": "initiated"}', 'professional', 2, 1, 'CA6dd653811ccdb4b91f51d79100d67f27')
2025-07-27 13:08:29,685 INFO sqlalchemy.engine.Engine COMMIT
2025-07-27 13:08:29,685 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-27 13:08:29,688 - agent.services.voice_service - INFO - Returning TwiML for next question
INFO:     *************:0 - "POST /voice/webhook/speech HTTP/1.1" 200 OK
2025-07-27 13:08:38,111 - api.voice - INFO - Speech webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'SpeechResult': "I'm more handsome.", 'CallerState': 'CA', 'Language': 'en-US', 'ToZip': '', 'Confidence': '0.6796631', 'CallSid': 'CA6dd653811ccdb4b91f51d79100d67f27', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': 'AC98fddc0d9bf796ef17233cf22bbe31f5', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-27 13:08:38,112 - agent.services.voice_service - INFO - Processing speech input for call: CA6dd653811ccdb4b91f51d79100d67f27
2025-07-27 13:08:38,112 - agent.services.voice_service - INFO - User speech: 'I'm more handsome.' (confidence: 0.6796631)
2025-07-27 13:08:38,112 - agent.services.voice_service - INFO - Session updated - Questions: 2, Responses: 2
2025-07-27 13:08:38,112 - agent.services.voice_service - INFO - Generated next question: 'How do you typically handle stress or pressure?'
2025-07-27 13:08:38,119 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:08:38,119 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:08:38,120 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:08:38,120 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:08:38,120 INFO sqlalchemy.engine.Engine [cached since 27.43s ago] ('{"call_sid": "CA6dd653811ccdb4b91f51d79100d67f27", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (1040 characters truncated) ... : {}, "last_question": "How do you typically handle stress or pressure?", "recording_url": null, "recording_duration": 0, "call_status": "initiated"}', 'personality', 3, 2, 'CA6dd653811ccdb4b91f51d79100d67f27')
2025-07-27 13:08:38,120 - sqlalchemy.engine.Engine - INFO - [cached since 27.43s ago] ('{"call_sid": "CA6dd653811ccdb4b91f51d79100d67f27", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (1040 characters truncated) ... : {}, "last_question": "How do you typically handle stress or pressure?", "recording_url": null, "recording_duration": 0, "call_status": "initiated"}', 'personality', 3, 2, 'CA6dd653811ccdb4b91f51d79100d67f27')
2025-07-27 13:08:38,122 INFO sqlalchemy.engine.Engine COMMIT
2025-07-27 13:08:38,122 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-27 13:08:38,126 - agent.services.voice_service - INFO - Returning TwiML for next question
INFO:     ************:0 - "POST /voice/webhook/speech HTTP/1.1" 200 OK
2025-07-27 13:08:48,046 - api.voice - INFO - Speech webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'SpeechResult': 'I go to gym, do some workout.', 'CallerState': 'CA', 'Language': 'en-US', 'ToZip': '', 'Confidence': '0.********', 'CallSid': 'CA6dd653811ccdb4b91f51d79100d67f27', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': 'AC98fddc0d9bf796ef17233cf22bbe31f5', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-27 13:08:48,046 - agent.services.voice_service - INFO - Processing speech input for call: CA6dd653811ccdb4b91f51d79100d67f27
2025-07-27 13:08:48,046 - agent.services.voice_service - INFO - User speech: 'I go to gym, do some workout.' (confidence: 0.********)
2025-07-27 13:08:48,047 - agent.services.voice_service - INFO - Session updated - Questions: 3, Responses: 3
2025-07-27 13:08:48,047 - agent.services.voice_service - INFO - Generated next question: 'How do you like to spend your weekends? Any favorite activities?'
2025-07-27 13:08:48,051 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:08:48,051 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:08:48,051 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:08:48,051 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:08:48,052 INFO sqlalchemy.engine.Engine [cached since 37.36s ago] ('{"call_sid": "CA6dd653811ccdb4b91f51d79100d67f27", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (1348 characters truncated) ... ion": "How do you like to spend your weekends? Any favorite activities?", "recording_url": null, "recording_duration": 0, "call_status": "initiated"}', 'interests', 4, 3, 'CA6dd653811ccdb4b91f51d79100d67f27')
2025-07-27 13:08:48,052 - sqlalchemy.engine.Engine - INFO - [cached since 37.36s ago] ('{"call_sid": "CA6dd653811ccdb4b91f51d79100d67f27", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (1348 characters truncated) ... ion": "How do you like to spend your weekends? Any favorite activities?", "recording_url": null, "recording_duration": 0, "call_status": "initiated"}', 'interests', 4, 3, 'CA6dd653811ccdb4b91f51d79100d67f27')
2025-07-27 13:08:48,064 INFO sqlalchemy.engine.Engine COMMIT
2025-07-27 13:08:48,064 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-27 13:08:48,068 - agent.services.voice_service - INFO - Returning TwiML for next question
INFO:     *************:0 - "POST /voice/webhook/speech HTTP/1.1" 200 OK
2025-07-27 13:09:01,736 - api.voice - INFO - Speech webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'SpeechResult': 'I have to go out and eat, go to gym and do some cardio.', 'CallerState': 'CA', 'Language': 'en-US', 'ToZip': '', 'Confidence': '0.********', 'CallSid': 'CA6dd653811ccdb4b91f51d79100d67f27', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': 'AC98fddc0d9bf796ef17233cf22bbe31f5', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-27 13:09:01,736 - agent.services.voice_service - INFO - Processing speech input for call: CA6dd653811ccdb4b91f51d79100d67f27
2025-07-27 13:09:01,736 - agent.services.voice_service - INFO - User speech: 'I have to go out and eat, go to gym and do some cardio.' (confidence: 0.********)
2025-07-27 13:09:01,736 - agent.services.voice_service - INFO - Session updated - Questions: 4, Responses: 4
2025-07-27 13:09:01,737 - agent.services.voice_service - INFO - Generated next question: 'What qualities matter most to you in a partner?'
2025-07-27 13:09:01,741 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-27 13:09:01,741 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-27 13:09:01,744 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:09:01,744 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-27 13:09:01,746 INFO sqlalchemy.engine.Engine [cached since 51.05s ago] ('{"call_sid": "CA6dd653811ccdb4b91f51d79100d67f27", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (1639 characters truncated) ... : {}, "last_question": "What qualities matter most to you in a partner?", "recording_url": null, "recording_duration": 0, "call_status": "initiated"}', 'relationships', 5, 4, 'CA6dd653811ccdb4b91f51d79100d67f27')
2025-07-27 13:09:01,746 - sqlalchemy.engine.Engine - INFO - [cached since 51.05s ago] ('{"call_sid": "CA6dd653811ccdb4b91f51d79100d67f27", "user_id": "cfebf3ac-7e66-4a6a-91ed-a804b4300c2b", "from_number": "+***********", "start_time": "2 ... (1639 characters truncated) ... : {}, "last_question": "What qualities matter most to you in a partner?", "recording_url": null, "recording_duration": 0, "call_status": "initiated"}', 'relationships', 5, 4, 'CA6dd653811ccdb4b91f51d79100d67f27')
2025-07-27 13:09:01,750 INFO sqlalchemy.engine.Engine COMMIT
2025-07-27 13:09:01,750 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-27 13:09:01,753 - agent.services.voice_service - INFO - Returning TwiML for next question
INFO:     *************:0 - "POST /voice/webhook/speech HTTP/1.1" 200 OK