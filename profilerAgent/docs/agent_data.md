025-07-31 16:44:23,432 INFO sqlalchemy.engine.Engine SELECT call_sid, user_id, conversation_stage, total_exchanges, successful_responses, timeout_count, engagement_score, created_at FROM agent_conversations ORDER BY created_at DESC LIMIT 5
2025-07-31 16:44:23,432 INFO sqlalchemy.engine.Engine [generated in 0.00006s] ()
Call: CA8990cd... | Stage: greeting | Exchanges: 8 | Responses: 8 | Timeouts: 0 | Engagement: 0.66 | Created: 2025-07-31 07:06:43.268068

=== AGENT_DECISIONS ===
2025-07-31 16:44:23,438 INFO sqlalchemy.engine.Engine SELECT call_sid, decision_type, confidence, reasoning, created_at FROM agent_decisions ORDER BY created_at DESC LIMIT 5
2025-07-31 16:44:23,438 INFO sqlalchemy.engine.Engine [generated in 0.00008s] ()
Call: CA8990cd... | Type: question_generate | Confidence: 0.80 | Reasoning: AI generated question based on conversation contex... | Created: 2025-07-31 07:09:44.239463
Call: CA8990cd... | Type: question_generate | Confidence: 0.80 | Reasoning: AI generated question based on conversation contex... | Created: 2025-07-31 07:08:26.109148
Call: CA8990cd... | Type: question_generate | Confidence: 0.80 | Reasoning: AI generated question based on conversation contex... | Created: 2025-07-31 07:06:43.256093

=== VOICE_SESSIONS ===
2025-07-31 16:44:23,442 INFO sqlalchemy.engine.Engine SELECT user_id, twilio_call_sid, call_duration, completed_at, created_at FROM voice_sessions ORDER BY created_at DESC LIMIT 5
2025-07-31 16:44:23,442 INFO sqlalchemy.engine.Engine [generated in 0.00004s] ()
User: 8beb42d4-23b6-4dbf-a3ea-869044eb8881 | Call: CA8990cd... | Duration: 271s | Completed: 2025-07-31 07:10:47.108650+00:00 | Created: 2025-07-31 07:06:15.820622+00:00

=== AGENT_GOALS ===
2025-07-31 16:44:23,446 INFO sqlalchemy.engine.Engine SELECT call_sid, goal_name, goal_status, goal_progress, created_at FROM agent_goals ORDER BY created_at DESC LIMIT 10
2025-07-31 16:44:23,446 INFO sqlalchemy.engine.Engine [generated in 0.00008s] ()
Call: CA8990cd... | Goal: collect_basic_info | Status: active | Progress: 0.00 | Created: 2025-07-31 07:06:43.275147
Call: CA8990cd... | Goal: collect_profession | Status: completed | Progress: 1.00 | Created: 2025-07-31 07:06:43.275147
Call: CA8990cd... | Goal: collect_personality | Status: completed | Progress: 1.00 | Created: 2025-07-31 07:06:43.275147
Call: CA8990cd... | Goal: collect_interests | Status: completed | Progress: 1.00 | Created: 2025-07-31 07:06:43.275147
Call: CA8990cd... | Goal: collect_relationships | Status: active | Progress: 0.00 | Created: 2025-07-31 07:06:43.275147



2025-07-31 16:44:52,861 INFO sqlalchemy.engine.Engine SELECT call_sid, conversation_stage, topic_transitions, emotional_state, total_exchanges, successful_responses FROM agent_conversations WHERE call_sid LIKE 'CA8990cd%'
2025-07-31 16:44:52,861 INFO sqlalchemy.engine.Engine [generated in 0.00005s] ()
Call: CA8990cdb7384db008790387a3f97cb8a6
  Stage: greeting
  Topic transitions: ['greeting']
  Emotional state: {'comfort': 0.44049999999999995, 'engagement': 0.6588999999999999, 'enthusiasm': 0.5461999999999999}
  Exchanges: 8, Responses: 8


2025-07-31 16:46:50,959 INFO sqlalchemy.engine.Engine [generated in 0.00005s] ()
Call: CA8990cdb7384db008790387a3f97cb8a6
  Memory type: user_profile
  Collected info keys: ['interests', 'profession', 'personality_traits']
    interests: ['sports', 'adventure', 'catching fish']
    profession: ux designer
    personality_traits: ['open-minded']

=== VOICE_SESSIONS ANALYSIS_DATA ===
2025-07-31 16:46:50,966 INFO sqlalchemy.engine.Engine SELECT twilio_call_sid, analysis_data FROM voice_sessions WHERE twilio_call_sid LIKE 'CA8990cd%'
2025-07-31 16:46:50,966 INFO sqlalchemy.engine.Engine [generated in 0.00010s] ()
Call: CA8990cdb7384db008790387a3f97cb8a6
  Analysis collected_info keys: ['interests', 'profession', 'personality_traits']
    interests: ['sports', 'adventure', 'catching fish']
    profession: ux designer
    personality_traits: ['open-minded']
  Transcript length: 9
  Recent exchanges:
    1. Q: 
       A: I I would play basketball pickle ball.  I play the ping pong. That's all Chinese does.  Baton, of course.  Yeah.  Well, what was the question again?  I mean, the second question.
       Stage: greeting
    2. Q: 
       A: Of the favorite adventure.  Um, favorite adventure, I would say.  using the cats tonight to catch fishes in the lake and then eat it without doing research about if the water quality, or there's any bacteria in the water and  if, if the fish got affected, um,
       Stage: greeting
    3. Q: 
       A: Uh, I was doing. I was doing cash and the host and master when I was in college.  I don't know, just go because he was doing, um, quarantine so we shouldn't, we shouldn't  go to like public and public spaces, like coffee place or restaurant, we have to wear masks. So what I decided to do is I just go out in nature, you know, experience with the  with some, some, some nature, um, events engaging in that
       Stage: greeting