# Frontend 设计规范文档

## 项目概述

基于 AI 的约会应用前端设计规范，采用简约设计 + 轻度游戏化的理念，优先开发 Web 端，后续扩展到移动应用。

## 设计参考

**主要参考**: [HeyEmily.ai](https://www.heyemily.ai/)
- 专业的蓝色基调
- 现代简约的卡片设计
- 清晰的视觉层次
- 优秀的响应式布局

## 技术栈

### Web 端技术选型
```
框架: Next.js 14 (App Router)
语言: TypeScript
样式: Tailwind CSS
动画: Framer Motion
状态: Zustand
API: React Query
表单: React Hook Form
```

### 开发工具
```
包管理: pnpm
代码规范: ESLint + Prettier
类型检查: TypeScript
构建工具: Next.js (Turbopack)
部署: Vercel
```

## 设计系统

### 配色方案

#### 主色调 (基于 HeyEmily 优化)
```css
/* 主色系 - 专业蓝紫渐变 */
--primary-50: #EEF2FF
--primary-100: #E0E7FF
--primary-500: #667EEA  /* 主色 */
--primary-600: #5B21B6
--primary-700: #764BA2  /* 深紫 */

/* 强调色 - 温暖约会感 */
--accent-400: #FF6B9D   /* 温暖粉 */
--accent-500: #F472B6   /* 主强调色 */
--accent-600: #EC4899

/* 渐变色 */
--gradient-primary: linear-gradient(135deg, #667EEA 0%, #764BA2 100%)
--gradient-accent: linear-gradient(135deg, #667EEA 0%, #FF6B9D 100%)
```

#### 中性色系
```css
/* 背景色 */
--bg-primary: #FAFBFC    /* 主背景 */
--bg-secondary: #FFFFFF  /* 卡片背景 */
--bg-tertiary: #F8FAFC   /* 次要背景 */

/* 边框色 */
--border-light: #E5E7EB
--border-medium: #D1D5DB
--border-dark: #9CA3AF

/* 文字色 */
--text-primary: #1F2937    /* 主要文字 */
--text-secondary: #6B7280  /* 次要文字 */
--text-tertiary: #9CA3AF   /* 辅助文字 */
```

#### 状态色
```css
/* 功能色 */
--success: #10B981
--warning: #F59E0B
--error: #EF4444
--info: #3B82F6
```

### 字体系统

#### 字体族
```css
/* 主字体 */
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif
--font-mono: 'JetBrains Mono', 'Fira Code', monospace

/* 字体大小 */
--text-xs: 0.75rem     /* 12px */
--text-sm: 0.875rem    /* 14px */
--text-base: 1rem      /* 16px */
--text-lg: 1.125rem    /* 18px */
--text-xl: 1.25rem     /* 20px */
--text-2xl: 1.5rem     /* 24px */
--text-3xl: 1.875rem   /* 30px */
--text-4xl: 2.25rem    /* 36px */

/* 字重 */
--font-normal: 400
--font-medium: 500
--font-semibold: 600
--font-bold: 700
```

### 间距系统

#### 标准间距
```css
/* 基础间距 (4px 基准) */
--space-1: 0.25rem   /* 4px */
--space-2: 0.5rem    /* 8px */
--space-3: 0.75rem   /* 12px */
--space-4: 1rem      /* 16px */
--space-5: 1.25rem   /* 20px */
--space-6: 1.5rem    /* 24px */
--space-8: 2rem      /* 32px */
--space-10: 2.5rem   /* 40px */
--space-12: 3rem     /* 48px */
--space-16: 4rem     /* 64px */
--space-20: 5rem     /* 80px */
```

### 圆角系统
```css
--radius-sm: 4px
--radius-md: 8px
--radius-lg: 12px
--radius-xl: 16px
--radius-2xl: 24px
--radius-full: 9999px
```

### 阴影系统
```css
/* 卡片阴影 */
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05)
--shadow-md: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)
--shadow-lg: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)
--shadow-xl: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)

/* 交互阴影 */
--shadow-hover: 0 4px 12px rgba(102, 126, 234, 0.15)
--shadow-focus: 0 0 0 3px rgba(102, 126, 234, 0.1)
```

## 组件设计规范

### 按钮组件
```css
/* 主要按钮 */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  padding: 12px 24px;
  border-radius: var(--radius-lg);
  font-weight: var(--font-medium);
  transition: all 200ms ease;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-hover);
}

.btn-primary:active {
  transform: translateY(0);
}

/* 次要按钮 */
.btn-secondary {
  background: white;
  color: var(--primary-500);
  border: 1px solid var(--border-light);
  padding: 12px 24px;
  border-radius: var(--radius-lg);
}

/* 按钮尺寸 */
.btn-sm { padding: 8px 16px; font-size: var(--text-sm); }
.btn-md { padding: 12px 24px; font-size: var(--text-base); }
.btn-lg { padding: 16px 32px; font-size: var(--text-lg); }
```

### 卡片组件
```css
.card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  box-shadow: var(--shadow-md);
  transition: all 200ms ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-500);
}

/* 卡片变体 */
.card-interactive { cursor: pointer; }
.card-highlighted { border-color: var(--primary-500); }
.card-success { border-color: var(--success); }
```

### 输入框组件
```css
.input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  transition: all 200ms ease;
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: var(--shadow-focus);
}

.input:error {
  border-color: var(--error);
}
```

## 布局系统

### 响应式断点
```css
/* 移动端优先 */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* 断点定义 */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
```

### 导航系统

#### 移动端导航 (优先)
```
底部导航栏 (80px 高度)
├── 🏠 首页
├── 💕 匹配 (需认证)
├── 💬 聊天 (需认证)
├── 👤 档案
└── ⚙️ 设置

顶部状态栏 (60px 高度)
├── 进度指示器
├── 当前步骤说明
└── 帮助按钮
```

#### 桌面端导航
```
顶部导航栏 (64px 高度)
├── Logo
├── 导航菜单
├── 用户头像
└── 通知图标

侧边栏 (可选, 240px 宽度)
├── 主要功能
├── 快捷操作
└── 设置选项
```

## 游戏化设计

### 简约游戏化原则
- **微妙不突兀**: 游戏化元素不干扰主要功能
- **有意义的反馈**: 每个游戏化元素都有实际价值
- **渐进式揭示**: 随着用户进度逐步展示功能

### 进度系统
```
认证进度 (4步)
┌─────────────────────────────┐
│ ●●●○ 75% 完成               │
│ 📱 SMS → 🎤 语音 → 💼 LinkedIn │
│ 还差一步就能开始匹配了！      │
└─────────────────────────────┘

档案完整度
┌─────────────────────────────┐
│ 🎯 档案完整度: 85%          │
│ ████████▒▒                 │
│ 添加更多信息提高匹配度       │
└─────────────────────────────┘
```

### 成就系统 (极简)
```
基础徽章 (最多6个)
├── 🎉 欢迎新人 (完成注册)
├── 📱 验证达人 (SMS验证)
├── 🎤 语音勇士 (语音面试)
├── 💼 职场精英 (LinkedIn)
├── 💕 初次心动 (首次匹配)
└── 🔥 活跃用户 (连续使用)
```

### 微交互动画
```css
/* 按钮点击反馈 */
.btn:active {
  transform: scale(0.98);
}

/* 卡片悬停效果 */
.card:hover {
  transform: translateY(-2px);
}

/* 加载状态 */
.loading {
  animation: pulse 2s infinite;
}

/* 成功状态 */
.success-check {
  animation: checkmark 0.5s ease-in-out;
}
```

## 页面架构

### 核心页面列表
```
认证流程
├── / (欢迎页)
├── /register (注册)
├── /verify-sms (SMS验证)
├── /voice-interview (语音面试)
├── /linkedin-auth (LinkedIn验证)
└── /profile-setup (档案设置)

主要功能
├── /dashboard (仪表板)
├── /matches (匹配推荐)
├── /chat (聊天)
├── /profile (个人档案)
└── /settings (设置)

辅助页面
├── /help (帮助)
├── /privacy (隐私政策)
├── /terms (服务条款)
└── /404 (错误页面)
```

### 页面布局模板
```
认证页面布局
├── 顶部进度指示 (60px)
├── 主内容区域 (flex-1)
│   ├── 步骤标题
│   ├── 内容卡片
│   └── 操作按钮
└── 底部帮助链接 (40px)

主应用布局
├── 顶部状态栏 (60px) [移动端]
├── 主内容区域 (flex-1)
└── 底部导航栏 (80px) [移动端]
```

## 开发规范

### 组件命名规范
```
组件文件: PascalCase (Button.tsx)
组件目录: kebab-case (user-card/)
样式文件: kebab-case (button.module.css)
工具函数: camelCase (formatDate.ts)
```

### 文件结构
```
src/
├── app/                 # Next.js App Router
├── components/          # 组件库
│   ├── ui/             # 基础UI组件
│   ├── forms/          # 表单组件
│   ├── layout/         # 布局组件
│   └── features/       # 功能组件
├── lib/                # 工具库
│   ├── api/            # API调用
│   ├── stores/         # 状态管理
│   ├── utils/          # 工具函数
│   └── hooks/          # 自定义Hooks
├── styles/             # 样式文件
├── types/              # TypeScript类型
└── constants/          # 常量定义
```

### 代码规范
```typescript
// 组件定义
interface ButtonProps {
  variant?: 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
  children: React.ReactNode
  onClick?: () => void
}

export function Button({ 
  variant = 'primary', 
  size = 'md', 
  children, 
  onClick 
}: ButtonProps) {
  return (
    <button 
      className={cn(
        'btn',
        `btn-${variant}`,
        `btn-${size}`
      )}
      onClick={onClick}
    >
      {children}
    </button>
  )
}
```

## 开发计划

### 第一阶段: 基础设施 (Week 1-2)
- [ ] 项目初始化和配置
- [ ] 设计系统组件库
- [ ] 基础布局组件
- [ ] API集成层

### 第二阶段: 认证流程 (Week 3-4)
- [ ] 欢迎页面
- [ ] 注册流程
- [ ] SMS验证
- [ ] 语音面试引导

### 第三阶段: 核心功能 (Week 5-6)
- [ ] LinkedIn集成
- [ ] 用户档案
- [ ] 匹配推荐
- [ ] 基础聊天

### 第四阶段: 优化完善 (Week 7-8)
- [ ] 游戏化元素
- [ ] 性能优化
- [ ] 响应式完善
- [ ] 测试和发布

## 质量标准

### 性能指标
- First Contentful Paint < 1.5s
- Largest Contentful Paint < 2.5s
- Cumulative Layout Shift < 0.1
- First Input Delay < 100ms

### 可访问性
- WCAG 2.1 AA 标准
- 键盘导航支持
- 屏幕阅读器兼容
- 色彩对比度 4.5:1

### 浏览器兼容性
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

---

**文档版本**: v1.0  
**最后更新**: 2025-07-23  
**负责人**: 开发团队
