# 🤖 AI集成使用指南

## 📋 概述

最简单的DeepSeek AI集成，在语音通话的第3和第5个问题时使用AI生成个性化问题。

## 🔧 配置

### 1. 环境变量设置

在你的 `.env` 文件中添加：

```bash
# DeepSeek API配置
DEEPSEEK_API_KEY=your-deepseek-api-key-here

# AI功能开关（可选，默认为true）
AI_ENABLED=true

# AI超时设置（可选，默认为2秒）
AI_TIMEOUT=2
```

### 2. 获取DeepSeek API Key

1. 访问 [DeepSeek官网](https://platform.deepseek.com/)
2. 注册账号并获取API Key
3. 将API Key添加到环境变量中

## 🧪 测试AI集成

### 1. 基础功能测试

```bash
cd profilerAgent
python test_ai_integration.py
```

**预期输出：**
```
🤖 Testing AI Service Integration
==================================================
✅ AI Service initialized - enabled: True
⏱️  Timeout setting: 2s

🧪 Test 1/4: Professional Domain
   User said: "I'm a product manager at a tech startup"
   ✅ AI Question: "What's the most exciting product challenge you're working on right now?"
   ⏱️  Response time: 1.23s
   ✅ Question validation passed
```

### 2. 语音通话测试

启动服务并发起语音通话：

```bash
cd profilerAgent/backend
python -m uvicorn main:app --reload --port 8000
```

**AI触发时机：**
- 第3个问题：基于用户的职业回答生成个性化问题
- 第5个问题：基于用户的兴趣/关系回答生成个性化问题

## 📊 监控和日志

### 关键日志信息

**AI调用成功：**
```
INFO - Attempting AI question generation for call: CA123456
INFO - AI question generated successfully: What's your favorite aspect of product management?
```

**AI调用失败（自动降级）：**
```
WARNING - AI question generation failed, falling back to template
INFO - Generated next question: 'How do you typically handle stress or pressure?'
```

**延迟监控：**
```
DEBUG - Should use AI for question 3: True
INFO - AI question generated successfully: ... (response time logged in test)
```

## 🎯 工作原理

### 1. AI触发逻辑

```python
def _should_use_ai(session):
    # 在第3和第5个问题时使用AI
    return session.questions_asked in [3, 5]
```

### 2. 问题生成流程

```
用户回答 → 判断是否使用AI → 调用DeepSeek API → 验证问题质量 → 返回问题
                ↓ (失败时)
            使用预设问题 (无缝降级)
```

### 3. 问题质量控制

- 长度：10-200字符
- 格式：必须以?结尾
- 词数：最多30个词
- 内容：不包含敏感词汇

## 🚨 故障排除

### 常见问题

**1. AI服务未启用**
```
❌ AI Service is disabled. Please check DEEPSEEK_API_KEY configuration.
```
**解决：** 检查 `.env` 文件中的 `DEEPSEEK_API_KEY` 设置

**2. API调用超时**
```
WARNING - DeepSeek API timeout after 2s
```
**解决：** 增加 `AI_TIMEOUT` 值或检查网络连接

**3. API调用失败**
```
ERROR - DeepSeek API error: 401
```
**解决：** 检查API Key是否正确，是否有足够的配额

**4. 问题验证失败**
```
WARNING - AI question validation failed: [question content]
```
**解决：** AI生成的问题不符合质量标准，系统会自动使用预设问题

### 调试技巧

**1. 启用详细日志**
```python
import logging
logging.getLogger('agent.services.ai_service').setLevel(logging.DEBUG)
```

**2. 临时禁用AI**
```bash
export AI_ENABLED=false
```

**3. 调整超时时间**
```bash
export AI_TIMEOUT=5  # 增加到5秒
```

## 📈 性能指标

### 预期性能

- **AI调用成功率：** >90%
- **平均响应时间：** 1.5-2.5秒
- **问题验证通过率：** >95%
- **用户感知延迟：** <2秒（通过填充语句优化）

### 监控建议

1. **记录AI调用次数和成功率**
2. **监控平均响应时间**
3. **跟踪问题质量评分**
4. **收集用户对AI问题的反馈**

## 🔄 下一步优化

### 可能的改进方向

1. **更智能的触发逻辑**
   - 基于用户回答质量决定是否使用AI
   - 动态调整AI使用频率

2. **上下文记忆**
   - AI记住之前的对话内容
   - 生成更连贯的问题

3. **多模型支持**
   - 支持GPT-4、Claude等其他模型
   - 根据场景选择最适合的模型

4. **实时优化**
   - 根据用户反馈调整问题生成策略
   - A/B测试不同的AI配置

## 📞 支持

如果遇到问题：

1. 查看日志文件中的错误信息
2. 运行测试脚本验证配置
3. 检查网络连接和API配额
4. 确认环境变量设置正确

**记住：** 系统设计了完整的降级机制，即使AI完全失败，语音通话也会正常进行，只是使用预设问题。
