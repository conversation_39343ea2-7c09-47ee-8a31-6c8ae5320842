# Backend 模块架构文档

## 概述

Backend 模块是基于 FastAPI 的 HTTP 服务层，为前端提供 RESTful API 接口。它作为 Agent 系统的 HTTP 包装器，处理 Web 请求、认证、CORS 等 Web 相关功能。

## 目录结构

```
backend/
├── main.py                     # FastAPI 主应用入口
├── requirements.txt            # Python 依赖
├── api/                        # API 路由模块
│   ├── __init__.py
│   ├── auth.py                # 认证相关 API
│   ├── health.py              # 健康检查 API
│   ├── linkedin.py            # LinkedIn OAuth API
│   └── voice.py               # 语音服务 API
├── database/                   # 数据库连接管理
│   ├── __init__.py
│   ├── connection.py          # 数据库连接池
│   ├── init.sql              # 数据库初始化脚本
│   └── migrations/           # 数据库迁移脚本
├── middleware/                 # 中间件
│   ├── __init__.py
│   ├── auth.py               # 认证中间件
│   └── cors.py               # CORS 中间件
├── models/                     # API 数据模型
│   ├── __init__.py
│   └── api_models.py         # Pydantic 模型定义
├── routers/                    # 路由组织（预留）
└── services/                   # 业务服务层（预留）
```

## 核心组件

### 1. 主应用 (main.py)
**职责**: FastAPI 应用初始化和生命周期管理

**主要功能**:
- Agent 系统集成（使用 AgentFactory）
- 应用生命周期管理
- 全局异常处理
- 路由注册

**生命周期流程**:
```python
启动 → 初始化Agent系统 → 验证健康状态 → 服务就绪
关闭 → 清理Agent资源 → 关闭数据库连接 → 完成
```

**关键特性**:
- 使用新的 AgentFactory 模式
- 异步初始化和清理
- 全局异常处理
- 统一的错误响应格式

### 2. API 路由模块 (api/)

#### 认证 API (auth.py)
**路径前缀**: `/api/v1/auth`

**端点列表**:
```
POST /register              # 用户注册
POST /verify-sms           # SMS 验证
POST /resend-sms           # 重发 SMS
POST /login                # 用户登录
```

**认证流程**:
```
注册 → 自动发送SMS → 验证SMS → 生成JWT → 登录成功
```

**特性**:
- 自动 SMS 发送
- JWT Token 生成
- 用户状态检查
- 错误处理和重试机制

#### 语音服务 API (voice.py)
**路径前缀**: `/api/v1/voice`

**端点列表**:
```
GET  /status/{user_id}      # 获取语音状态
POST /initiate/{user_id}    # 发起语音通话
POST /webhook/incoming      # Twilio 来电 webhook
POST /webhook/recording     # Twilio 录音 webhook
POST /webhook/status        # Twilio 状态 webhook
GET  /analysis/{user_id}    # 获取语音分析结果
POST /retry/{user_id}       # 重新开始语音面试
```

**Webhook 处理**:
- 返回 TwiML XML 响应
- 处理录音完成事件
- 通话状态更新
- 错误恢复机制

#### LinkedIn OAuth API (linkedin.py)
**路径前缀**: `/api/v1/linkedin`

**端点列表**:
```
GET  /auth                  # 开始 OAuth 流程
GET  /callback              # OAuth 回调处理
GET  /status/{user_id}      # 获取验证状态
POST /verify/{user_id}      # 手动提交资料
DELETE /disconnect/{user_id} # 断开连接
GET  /profile/{user_id}     # 获取资料信息
```

**OAuth 流程**:
```
前端请求 → 生成授权URL → 用户授权 → 回调处理 → 资料获取 → 验证完成
```

#### 健康检查 API (health.py)
**端点列表**:
```
GET /health                 # 系统整体健康检查
GET /health/database        # 数据库健康检查
GET /health/redis          # Redis 健康检查
GET /health/agent          # Agent 系统健康检查
```

**健康检查内容**:
- 数据库连接状态
- Redis 连接状态
- Agent 系统状态（8个组件）
- 外部服务状态

### 3. 数据库连接管理 (database/)

#### 连接管理器 (connection.py)
**职责**: 数据库连接池和会话管理

**主要功能**:
- SQLAlchemy 引擎管理
- Redis 客户端管理
- 连接池配置
- 健康检查

**配置特性**:
- 连接池大小控制
- 超时设置
- 自动重连
- 连接验证

### 4. 中间件 (middleware/)

#### CORS 中间件 (cors.py)
**功能**: 跨域请求处理
**配置**: 支持本地开发环境

#### 认证中间件 (auth.py)
**功能**: JWT Token 验证
**特性**: Bearer Token 支持

### 5. API 数据模型 (models/api_models.py)

#### 基础模型
```python
BaseResponse           # 基础响应模型
ErrorResponse         # 错误响应模型
SuccessResponse       # 成功响应模型
```

#### 认证相关模型
```python
RegisterRequest       # 注册请求
RegisterResponse      # 注册响应
VerifySMSRequest     # SMS验证请求
VerifySMSResponse    # SMS验证响应
LoginRequest         # 登录请求
LoginResponse        # 登录响应
```

#### 语音相关模型
```python
VoiceCallResponse    # 语音通话响应
VoiceStatusResponse  # 语音状态响应
VoiceAnalysisResponse # 语音分析响应
```

#### LinkedIn 相关模型
```python
LinkedInAuthResponse     # LinkedIn授权响应
LinkedInCallbackResponse # LinkedIn回调响应
LinkedInProfileResponse  # LinkedIn资料响应
```

## API 设计规范

### 请求格式
```json
{
  "phone_number": "+15005550006",
  "first_name": "Test",
  "last_name": "User"
}
```

### 响应格式
```json
{
  "success": true,
  "message": "Operation successful",
  "timestamp": "2025-07-23T14:14:26.147107",
  "data": {
    "user_id": "d2ac080f-d9be-49e4-b793-a0bcf0373f48",
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

### 错误响应
```json
{
  "success": false,
  "error": "User not found",
  "message": "The specified user does not exist",
  "timestamp": "2025-07-23T14:14:26.147107"
}
```

## 集成特性

### Agent 系统集成
- 使用 AgentFactory 统一初始化
- 8个组件健康监控
- 异步操作支持
- 错误传播和处理

### 外部服务集成
- **Twilio**: SMS + 语音 webhooks
- **LinkedIn**: OAuth 2.0 流程
- **DeepSeek**: AI 分析（通过 Agent）

### 安全特性
- JWT Token 认证
- CORS 保护
- 输入验证（Pydantic）
- 错误信息过滤

## 部署配置

### 环境要求
```bash
Python 3.8+
FastAPI 0.104+
SQLAlchemy 2.0+
Redis 6.0+
PostgreSQL 13+
```

### 启动方式
```bash
# 开发环境
cd backend
python main.py

# 生产环境
uvicorn main:app --host 0.0.0.0 --port 8000
```

### 环境变量
```bash
# 从 profilerAgent/.env 自动加载
DATABASE_URL=postgresql://...
REDIS_URL=redis://...
TWILIO_*=...
LINKEDIN_*=...
```

## API 文档

### Swagger UI
- **URL**: http://localhost:8000/docs
- **功能**: 交互式 API 文档
- **特性**: 在线测试、模型展示

### OpenAPI JSON
- **URL**: http://localhost:8000/openapi.json
- **用途**: API 规范导出

## 测试

### 健康检查测试
```bash
curl http://localhost:8000/health
```

### 认证流程测试
```bash
# 注册用户
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"phone_number": "+15005550006", "first_name": "Test", "last_name": "User"}'
```

### 集成测试
```bash
python profilerAgent/test_backend_integration.py
```

## 性能特点

- **异步处理**: 全异步 FastAPI 应用
- **连接池**: 数据库连接池管理
- **缓存**: Redis 缓存支持
- **并发**: 支持高并发请求
- **监控**: 完整的健康检查体系

## 扩展点

### 新增 API 端点
1. 在 `api/` 目录创建新的路由文件
2. 在 `models/api_models.py` 添加数据模型
3. 在 `main.py` 注册路由

### 新增中间件
1. 在 `middleware/` 目录创建中间件
2. 在 `main.py` 注册中间件

### 新增业务逻辑
1. 通过 Agent 系统扩展
2. 在 API 层调用 Agent 接口

## 与前端集成

### API Base URL
```
http://localhost:8000/api/v1
```

### 认证流程
```javascript
// 1. 注册
POST /auth/register

// 2. 验证SMS
POST /auth/verify-sms

// 3. 登录
POST /auth/login

// 4. 使用JWT访问其他API
Authorization: Bearer <token>
```

### WebSocket 支持（规划中）
- 实时通知
- 匹配更新
- 聊天功能

## 监控和日志

### 日志级别
- INFO: 正常操作
- WARNING: 警告信息
- ERROR: 错误信息

### 健康监控
- 系统状态：/health
- 组件状态：/health/agent
- 数据库状态：/health/database

### 性能指标
- 响应时间
- 错误率
- 并发连接数
