# Database Schema Documentation

## Overview
This document describes the complete database schema for the Dating App Profiler Agent application. The database uses PostgreSQL with UUID primary keys and includes comprehensive user management, profile analysis, matching, and communication features.

## Tables Summary
- **users** - Main user table with basic information and verification status
- **user_profiles** - AI-generated user profiles with MBTI and analysis data
- **profile_cards** - Individual personality cards generated for users
- **linkedin_profiles** - LinkedIn profile data and verification
- **user_matches** - User matching and recommendation system
- **user_preferences** - User dating preferences and filters
- **match_recommendations** - Batch recommendation tracking
- **user_chat_messages** - Chat messages between matched users
- **voice_sessions** - Voice call analysis sessions
- **sms_verifications** - SMS verification codes
- **user_feedback** - User feedback on profile cards
- **agent_decisions** - Agent decision-making records during voice calls
- **agent_goals** - Agent goal management and progress tracking
- **agent_memory** - Agent memory storage for different contexts
- **agent_metrics** - Agent performance metrics and analytics
- **agent_strategies** - Agent strategy configurations and settings
- **agent_conversations** - Agent conversation context and state tracking
- **agent_learning** - Agent learning records and feedback processing

---

## Table Definitions

### 1. users (Main User Table)

**Purpose**: Core user information and verification status

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | UUID | PRIMARY KEY, NOT NULL | uuid_generate_v4() | Unique user identifier |
| phone_number | VARCHAR(20) | NOT NULL, UNIQUE | - | User's phone number |
| email | VARCHAR(255) | - | - | User's email address |
| first_name | VARCHAR(100) | NOT NULL | - | User's first name |
| last_name | VARCHAR(100) | NOT NULL | - | User's last name |
| age | INTEGER | CHECK (age >= 18 AND age <= 100) | - | User's age |
| city | VARCHAR(100) | - | - | User's city |
| profession | VARCHAR(200) | - | - | User's profession |
| sms_verified | BOOLEAN | - | false | SMS verification status |
| voice_call_completed | BOOLEAN | - | false | Voice call completion status |
| linkedin_verified | BOOLEAN | - | false | LinkedIn verification status |
| verification_status | VARCHAR(20) | CHECK constraint | 'pending' | Overall verification status |
| verification_level | VARCHAR(20) | CHECK constraint | 'unverified' | Verification level |
| status | VARCHAR(20) | CHECK constraint | 'active' | User account status |
| last_login | TIMESTAMP WITH TIME ZONE | - | now() | Last login timestamp |
| created_at | TIMESTAMP WITH TIME ZONE | - | now() | Account creation timestamp |
| updated_at | TIMESTAMP WITH TIME ZONE | - | now() | Last update timestamp |

**Check Constraints**:
- `verification_status`: 'pending', 'sms_verified', 'voice_completed', 'linkedin_verified', 'fully_verified'
- `verification_level`: 'unverified', 'partial', 'verified'
- `status`: 'active', 'inactive', 'suspended'

**Indexes**:
- `users_pkey` (PRIMARY KEY)
- `idx_users_email`
- `idx_users_phone_number`
- `idx_users_verification_status`
- `users_phone_number_key` (UNIQUE)

**Triggers**:
- `update_users_updated_at` - Updates `updated_at` on row changes

---

### 2. user_profiles (AI Profile Analysis)

**Purpose**: Stores AI-generated user profiles and analysis data

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| user_id | UUID | PRIMARY KEY, NOT NULL, FK | - | Reference to users.id |
| voice_analysis | JSONB | - | - | Voice analysis results |
| linkedin_data | JSONB | - | - | LinkedIn profile data |
| final_profile | JSONB | - | - | Final compiled profile |
| confidence_scores | JSONB | - | - | AI confidence scores |
| mbti_type | VARCHAR(4) | - | - | MBTI personality type |
| verification_level | VARCHAR(20) | CHECK constraint | - | Profile verification level |
| last_updated | TIMESTAMP WITH TIME ZONE | - | now() | Last profile update |
| created_at | TIMESTAMP WITH TIME ZONE | - | now() | Profile creation timestamp |

**Check Constraints**:
- `verification_level`: 'high', 'medium', 'low'

**Indexes**:
- `user_profiles_pkey` (PRIMARY KEY)
- `idx_user_profiles_mbti`
- `idx_user_profiles_user_id`

**Foreign Keys**:
- `user_id` → `users(id)` ON DELETE CASCADE

---

### 3. profile_cards (Personality Cards)

**Purpose**: Individual personality cards generated for users

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | UUID | PRIMARY KEY, NOT NULL | uuid_generate_v4() | Unique card identifier |
| user_id | UUID | FK | - | Reference to users.id |
| card_type | VARCHAR(50) | NOT NULL, CHECK constraint | - | Type of personality card |
| title | VARCHAR(255) | NOT NULL | - | Card title |
| description | TEXT | - | - | Card description |
| confidence | NUMERIC(3,2) | CHECK (0.00 <= confidence <= 1.00) | - | AI confidence score |
| tags | TEXT[] | - | - | Associated tags |
| evidence | TEXT[] | - | - | Supporting evidence |
| created_at | TIMESTAMP WITH TIME ZONE | - | now() | Card creation timestamp |
| updated_at | TIMESTAMP WITH TIME ZONE | - | now() | Last update timestamp |

**Check Constraints**:
- `card_type`: 'personality_mbti', 'interests', 'lifestyle', 'social_style', 'relationship_goals'
- `confidence`: 0.00 to 1.00 range

**Indexes**:
- `profile_cards_pkey` (PRIMARY KEY)
- `idx_profile_cards_type`
- `idx_profile_cards_user_id`

**Foreign Keys**:
- `user_id` → `users(id)` ON DELETE CASCADE

**Triggers**:
- `update_profile_cards_updated_at` - Updates `updated_at` on row changes

---

### 4. linkedin_profiles (LinkedIn Data)

**Purpose**: LinkedIn profile data and verification status

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| user_id | UUID | PRIMARY KEY, NOT NULL, FK | - | Reference to users.id |
| linkedin_url | VARCHAR(500) | - | - | LinkedIn profile URL |
| profile_data | JSONB | - | - | Raw LinkedIn profile data |
| verification_status | VARCHAR(20) | CHECK constraint | 'pending' | Verification status |
| consistency_score | NUMERIC(3,2) | - | - | Data consistency score |
| verified_at | TIMESTAMP WITH TIME ZONE | - | - | Verification timestamp |
| created_at | TIMESTAMP WITH TIME ZONE | - | now() | Creation timestamp |

**Check Constraints**:
- `verification_status`: 'pending', 'verified', 'inconsistent'

**Indexes**:
- `linkedin_profiles_pkey` (PRIMARY KEY)
- `idx_linkedin_profiles_user_id`

**Foreign Keys**:
- `user_id` → `users(id)` ON DELETE CASCADE

---

### 5. user_matches (Matching System)

**Purpose**: User matching and recommendation tracking

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | UUID | PRIMARY KEY, NOT NULL | uuid_generate_v4() | Unique match identifier |
| user1_id | UUID | FK | - | First user reference |
| user2_id | UUID | FK | - | Second user reference |
| status | VARCHAR(20) | CHECK constraint | 'recommended' | Match status |
| match_score | INTEGER | CHECK (0 <= score <= 100) | - | Compatibility score |
| recommendation_reason | TEXT | - | - | Why they were matched |
| user1_action | VARCHAR(20) | CHECK constraint | - | User1's action |
| user2_action | VARCHAR(20) | CHECK constraint | - | User2's action |
| matched_at | TIMESTAMP WITH TIME ZONE | - | - | Match timestamp |
| created_at | TIMESTAMP WITH TIME ZONE | - | now() | Creation timestamp |

**Check Constraints**:
- `status`: 'recommended', 'interested', 'matched', 'passed', 'blocked'
- `user1_action`, `user2_action`: 'interested', 'passed'
- `user1_id <> user2_id` (users cannot match themselves)

**Indexes**:
- `user_matches_pkey` (PRIMARY KEY)
- `idx_user_matches_status`
- `idx_user_matches_user1`
- `idx_user_matches_user2`
- `user_matches_user1_id_user2_id_key` (UNIQUE)

**Foreign Keys**:
- `user1_id` → `users(id)` ON DELETE CASCADE
- `user2_id` → `users(id)` ON DELETE CASCADE

---

### 6. user_preferences (Dating Preferences)

**Purpose**: User dating preferences and filters

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| user_id | UUID | PRIMARY KEY, NOT NULL, FK | - | Reference to users.id |
| age_range_min | INTEGER | - | 18 | Minimum age preference |
| age_range_max | INTEGER | - | 45 | Maximum age preference |
| max_distance | INTEGER | - | 50 | Maximum distance (km) |
| preferred_mbti_types | VARCHAR(4)[] | - | - | Preferred MBTI types |
| deal_breakers | TEXT[] | - | - | Deal breaker list |
| interests | TEXT[] | - | - | Interest preferences |
| updated_at | TIMESTAMP WITH TIME ZONE | - | now() | Last update timestamp |
| created_at | TIMESTAMP WITH TIME ZONE | - | now() | Creation timestamp |

**Indexes**:
- `user_preferences_pkey` (PRIMARY KEY)
- `idx_user_preferences_user_id`

**Foreign Keys**:
- `user_id` → `users(id)` ON DELETE CASCADE

**Triggers**:
- `update_user_preferences_updated_at` - Updates `updated_at` on row changes

---

### 7. match_recommendations (Recommendation Batches)

**Purpose**: Tracks recommendation batches sent to users

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | UUID | PRIMARY KEY, NOT NULL | uuid_generate_v4() | Unique recommendation ID |
| user_id | UUID | FK | - | User receiving recommendation |
| recommended_user_id | UUID | FK | - | User being recommended |
| recommendation_batch | VARCHAR(50) | - | - | Batch identifier |
| sent_at | TIMESTAMP WITH TIME ZONE | - | now() | When recommendation was sent |
| user_response | VARCHAR(20) | CHECK constraint | - | User's response |
| response_at | TIMESTAMP WITH TIME ZONE | - | - | Response timestamp |
| created_at | TIMESTAMP WITH TIME ZONE | - | now() | Creation timestamp |

**Check Constraints**:
- `user_response`: 'interested', 'passed', 'no_response'

**Indexes**:
- `match_recommendations_pkey` (PRIMARY KEY)
- `idx_match_recommendations_batch`
- `idx_match_recommendations_user_id`

**Foreign Keys**:
- `user_id` → `users(id)` ON DELETE CASCADE
- `recommended_user_id` → `users(id)` ON DELETE CASCADE

---

### 8. user_chat_messages (Chat System)

**Purpose**: Chat messages between matched users

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | UUID | PRIMARY KEY, NOT NULL | uuid_generate_v4() | Unique message ID |
| match_id | UUID | FK | - | Reference to user_matches.id |
| sender_id | UUID | FK | - | Message sender |
| content | TEXT | NOT NULL | - | Message content |
| message_type | VARCHAR(20) | CHECK constraint | 'text' | Type of message |
| timestamp | TIMESTAMP WITH TIME ZONE | - | now() | Message timestamp |
| read_at | TIMESTAMP WITH TIME ZONE | - | - | Read timestamp |

**Check Constraints**:
- `message_type`: 'text', 'image', 'emoji'

**Indexes**:
- `user_chat_messages_pkey` (PRIMARY KEY)
- `idx_user_chat_messages_match_id`
- `idx_user_chat_messages_timestamp`

**Foreign Keys**:
- `match_id` → `user_matches(id)` ON DELETE CASCADE
- `sender_id` → `users(id)` ON DELETE CASCADE

---

### 9. voice_sessions (Voice Analysis)

**Purpose**: Voice call sessions and analysis data

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | UUID | PRIMARY KEY, NOT NULL | uuid_generate_v4() | Unique session ID |
| user_id | UUID | FK | - | Reference to users.id |
| twilio_call_sid | VARCHAR(255) | - | - | Twilio call identifier |
| call_duration | INTEGER | - | - | Call duration in seconds |
| analysis_data | JSONB | - | - | Voice analysis results |
| completed_at | TIMESTAMP WITH TIME ZONE | - | - | Session completion time |
| created_at | TIMESTAMP WITH TIME ZONE | - | now() | Session creation time |

**Indexes**:
- `voice_sessions_pkey` (PRIMARY KEY)
- `idx_voice_sessions_user_id`

**Foreign Keys**:
- `user_id` → `users(id)` ON DELETE CASCADE

---

### 10. sms_verifications (SMS Verification)

**Purpose**: SMS verification codes and tracking

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | UUID | PRIMARY KEY, NOT NULL | uuid_generate_v4() | Unique verification ID |
| phone_number | VARCHAR(20) | NOT NULL | - | Phone number to verify |
| verification_code | VARCHAR(10) | NOT NULL | - | Verification code |
| expires_at | TIMESTAMP WITH TIME ZONE | NOT NULL | - | Code expiration time |
| verified_at | TIMESTAMP WITH TIME ZONE | - | - | Verification timestamp |
| attempts | INTEGER | - | 0 | Number of attempts |
| created_at | TIMESTAMP WITH TIME ZONE | - | now() | Creation timestamp |

**Indexes**:
- `sms_verifications_pkey` (PRIMARY KEY)
- `idx_sms_verifications_expires`
- `idx_sms_verifications_phone`

---

### 11. user_feedback (Profile Card Feedback)

**Purpose**: User feedback on AI-generated profile cards

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | UUID | PRIMARY KEY, NOT NULL | uuid_generate_v4() | Unique feedback ID |
| user_id | UUID | FK | - | User providing feedback |
| card_id | UUID | FK | - | Profile card being rated |
| feedback_type | VARCHAR(20) | CHECK constraint | - | Type of feedback |
| comment | TEXT | - | - | Optional comment |
| created_at | TIMESTAMP WITH TIME ZONE | - | now() | Feedback timestamp |

**Check Constraints**:
- `feedback_type`: 'like', 'dislike', 'accurate', 'inaccurate'

**Indexes**:
- `user_feedback_pkey` (PRIMARY KEY)
- `idx_user_feedback_card_id`
- `idx_user_feedback_user_id`

**Foreign Keys**:
- `user_id` → `users(id)` ON DELETE CASCADE
- `card_id` → `profile_cards(id)` ON DELETE CASCADE

---

### 12. agent_decisions (Agent Decision Records)

**Purpose**: Records AI agent decision-making during voice calls

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | SERIAL | PRIMARY KEY, NOT NULL | nextval() | Unique decision ID |
| call_sid | VARCHAR(255) | NOT NULL | - | Twilio call session ID |
| decision_type | VARCHAR(50) | NOT NULL, CHECK constraint | - | Type of decision made |
| decision_data | JSONB | NOT NULL | - | Decision details and context |
| reasoning | TEXT | - | - | AI reasoning for the decision |
| confidence | DOUBLE PRECISION | CHECK constraint | 1.0 | Decision confidence score |
| created_at | TIMESTAMP WITHOUT TIME ZONE | - | now() | Decision timestamp |

**Check Constraints**:
- `decision_type`: 'stage_advance', 'question_generate', 'strategy_change', 'time_management', 'quality_assessment'
- `confidence`: 0.0 to 1.0 range

**Indexes**:
- `agent_decisions_pkey` (PRIMARY KEY)
- `idx_agent_decisions_call_sid`

---

### 13. agent_goals (Agent Goal Management)

**Purpose**: Manages AI agent goals and progress during voice calls

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | SERIAL | PRIMARY KEY, NOT NULL | nextval() | Unique goal ID |
| call_sid | VARCHAR(255) | NOT NULL | - | Twilio call session ID |
| goal_name | VARCHAR(100) | NOT NULL | - | Name of the goal |
| goal_status | VARCHAR(50) | CHECK constraint | 'active' | Current goal status |
| goal_progress | DOUBLE PRECISION | CHECK constraint | 0.0 | Progress percentage |
| goal_priority | INTEGER | - | 1 | Goal priority level |
| goal_data | JSONB | - | - | Additional goal data |
| created_at | TIMESTAMP WITHOUT TIME ZONE | - | now() | Goal creation time |
| updated_at | TIMESTAMP WITHOUT TIME ZONE | - | now() | Last update time |

**Check Constraints**:
- `goal_status`: 'active', 'completed', 'failed', 'skipped'
- `goal_progress`: 0.0 to 1.0 range

**Indexes**:
- `agent_goals_pkey` (PRIMARY KEY)
- `idx_agent_goals_call_sid`
- `agent_goals_call_sid_goal_name_key` (UNIQUE)

---

### 14. agent_memory (Agent Memory Storage)

**Purpose**: Stores different types of AI agent memory during voice calls

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | SERIAL | PRIMARY KEY, NOT NULL | nextval() | Unique memory ID |
| call_sid | VARCHAR(255) | NOT NULL | - | Twilio call session ID |
| memory_type | VARCHAR(50) | NOT NULL, CHECK constraint | - | Type of memory |
| memory_data | JSONB | NOT NULL | - | Memory content |
| created_at | TIMESTAMP WITHOUT TIME ZONE | - | now() | Memory creation time |
| updated_at | TIMESTAMP WITHOUT TIME ZONE | - | now() | Last update time |

**Check Constraints**:
- `memory_type`: 'short_term', 'long_term', 'context', 'user_profile'

**Indexes**:
- `agent_memory_pkey` (PRIMARY KEY)
- `idx_agent_memory_call_sid`
- `agent_memory_call_sid_memory_type_key` (UNIQUE)

---

### 15. agent_metrics (Agent Performance Metrics)

**Purpose**: Tracks AI agent performance metrics during voice calls

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | SERIAL | PRIMARY KEY, NOT NULL | nextval() | Unique metric ID |
| call_sid | VARCHAR(255) | NOT NULL | - | Twilio call session ID |
| metric_name | VARCHAR(100) | NOT NULL | - | Name of the metric |
| metric_value | DOUBLE PRECISION | NOT NULL | - | Metric value |
| metric_unit | VARCHAR(20) | - | - | Unit of measurement |
| created_at | TIMESTAMP WITHOUT TIME ZONE | - | now() | Metric timestamp |

**Indexes**:
- `agent_metrics_pkey` (PRIMARY KEY)
- `idx_agent_metrics_call_sid`
- `agent_metrics_call_sid_metric_name_created_at_key` (UNIQUE)

---

### 16. agent_strategies (Agent Strategy Configuration)

**Purpose**: Manages AI agent strategies and configurations

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | SERIAL | PRIMARY KEY, NOT NULL | nextval() | Unique strategy ID |
| call_sid | VARCHAR(255) | NOT NULL | - | Twilio call session ID |
| strategy_name | VARCHAR(100) | NOT NULL | - | Name of the strategy |
| strategy_config | JSONB | NOT NULL | - | Strategy configuration |
| is_active | BOOLEAN | - | true | Whether strategy is active |
| created_at | TIMESTAMP WITHOUT TIME ZONE | - | now() | Strategy creation time |
| updated_at | TIMESTAMP WITHOUT TIME ZONE | - | now() | Last update time |

**Indexes**:
- `agent_strategies_pkey` (PRIMARY KEY)
- `idx_agent_strategies_call_sid`
- `agent_strategies_call_sid_strategy_name_key` (UNIQUE)

---

### 17. agent_conversations (Agent Conversation Context)

**Purpose**: Tracks AI agent conversation context and state during voice calls

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | SERIAL | PRIMARY KEY, NOT NULL | nextval() | Unique conversation ID |
| call_sid | VARCHAR(255) | NOT NULL, UNIQUE | - | Twilio call session ID |
| user_id | UUID | FK, NOT NULL | - | Reference to users.id |
| conversation_stage | VARCHAR(50) | NOT NULL, CHECK constraint | - | Current conversation stage |
| topic_transitions | JSONB | - | []::jsonb | Topic transition history |
| emotional_state | JSONB | - | {}::jsonb | User emotional state analysis |
| engagement_score | DOUBLE PRECISION | CHECK constraint | 0.0 | User engagement level |
| conversation_quality | DOUBLE PRECISION | CHECK constraint | 0.0 | Overall conversation quality |
| total_exchanges | INTEGER | CHECK constraint | 0 | Total question-answer exchanges |
| successful_responses | INTEGER | CHECK constraint | 0 | Successful user responses |
| timeout_count | INTEGER | CHECK constraint | 0 | Number of timeouts |
| created_at | TIMESTAMP WITHOUT TIME ZONE | - | now() | Creation timestamp |
| updated_at | TIMESTAMP WITHOUT TIME ZONE | - | now() | Last update timestamp |

**Check Constraints**:
- `conversation_stage`: 'greeting', 'professional', 'personality', 'interests', 'relationships', 'closing', 'completed'
- `engagement_score`: 0.0 to 1.0 range
- `conversation_quality`: 0.0 to 1.0 range
- `total_exchanges`: >= 0
- `successful_responses`: >= 0
- `timeout_count`: >= 0

**Indexes**:
- `agent_conversations_pkey` (PRIMARY KEY)
- `idx_agent_conversations_call_sid`
- `idx_agent_conversations_user_id`
- `idx_agent_conversations_stage`
- `idx_agent_conversations_created_at`
- `agent_conversations_call_sid_key` (UNIQUE)

**Foreign Keys**:
- `user_id` → `users(id)` ON DELETE CASCADE

**Triggers**:
- `update_agent_conversations_updated_at` - Updates `updated_at` on row changes

---

### 18. agent_learning (Agent Learning Records)

**Purpose**: Records AI agent learning from conversations and feedback

| Column | Type | Constraints | Default | Description |
|--------|------|-------------|---------|-------------|
| id | SERIAL | PRIMARY KEY, NOT NULL | nextval() | Unique learning record ID |
| call_sid | VARCHAR(255) | NOT NULL | - | Twilio call session ID |
| user_id | UUID | FK, NOT NULL | - | Reference to users.id |
| learning_type | VARCHAR(50) | NOT NULL, CHECK constraint | - | Type of learning event |
| trigger_event | VARCHAR(100) | NOT NULL | - | Event that triggered learning |
| input_data | JSONB | NOT NULL | - | Input data for learning |
| learning_outcome | JSONB | NOT NULL | - | Learning results and insights |
| confidence_change | DOUBLE PRECISION | CHECK constraint | 0.0 | Confidence adjustment |
| effectiveness_score | DOUBLE PRECISION | CHECK constraint | - | Learning effectiveness score |
| applied_immediately | BOOLEAN | - | false | Whether learning was applied immediately |
| created_at | TIMESTAMP WITHOUT TIME ZONE | - | now() | Learning timestamp |

**Check Constraints**:
- `learning_type`: 'user_feedback', 'conversation_outcome', 'strategy_effectiveness', 'question_success', 'emotional_response'
- `confidence_change`: -1.0 to 1.0 range
- `effectiveness_score`: 0.0 to 1.0 range

**Indexes**:
- `agent_learning_pkey` (PRIMARY KEY)
- `idx_agent_learning_call_sid`
- `idx_agent_learning_user_id`
- `idx_agent_learning_type`
- `idx_agent_learning_created_at`

**Foreign Keys**:
- `user_id` → `users(id)` ON DELETE CASCADE

---

## Database Relationships

### Key Relationships:
1. **users** is the central table with foreign key relationships to most user-related tables
2. **user_profiles** has a 1:1 relationship with **users**
3. **profile_cards** has a 1:many relationship with **users**
4. **user_matches** creates many:many relationships between **users**
5. **user_chat_messages** depends on **user_matches** for context
6. **user_feedback** links **users** and **profile_cards** for improvement
7. **Agent tables** (agent_decisions, agent_goals, agent_memory, agent_metrics, agent_strategies) are linked by `call_sid` for voice session context
8. **voice_sessions** connects user data with agent processing through `user_id` and `twilio_call_sid`

### Cascade Behavior:
- Most foreign keys use `ON DELETE CASCADE` to maintain referential integrity
- Deleting a user will remove all associated data across user-related tables
- Agent tables are independent and linked by `call_sid` rather than foreign keys
- Agent data persists independently for analysis and debugging purposes

---

## Notes

- Most tables use UUID primary keys for better scalability and security
- Agent tables use SERIAL primary keys for performance optimization
- JSONB columns store flexible data structures for AI analysis results
- Comprehensive indexing supports efficient queries on common access patterns
- Check constraints ensure data integrity and valid enum values
- Triggers automatically update timestamp fields where appropriate
- Agent tables are designed for high-frequency writes during voice calls
- The `call_sid` field links agent operations to specific Twilio voice sessions
