# AI Dating App - Web应用产品设计文档

## 项目概述

基于Web的AI智能交友应用，通过多平台数据 + AI语音对话构建高质量用户画像，为寻求真实关系的成年人提供精准匹配服务。采用现代web技术栈，后续可扩展为移动应用。

## 目标用户

- **寻求真实关系的成年人**：18-45岁，有稳定生活的单身人士
- **接入方式**：通过Web界面注册，手机短信验证
- **用户特点**：重视真实性、寻求深度连接、愿意投入时间了解匹配对象

## 核心价值主张

- **便捷注册**：手机号快速注册，无需复杂流程
- **AI超级红娘**：基于深度画像分析的智能匹配
- **真实可靠**：手机验证 + LinkedIn验证，减少虚假信息
- **个性化服务**：基于MBTI和兴趣的深度兼容性分析

# MVP 版本设计

## MVP 用户流程

```
手机号注册 → 短信验证 → Web界面引导 → AI语音通话(一次) → LinkedIn验证 → 生成完整画像 → Web界面匹配服务
```

### 完整用户旅程
1. **手机号注册** - 用户输入手机号，接收短信验证码
2. **账户激活** - 验证码确认后创建账户，进入引导流程
3. **基础信息收集** - Web界面收集姓名、年龄、城市等基础信息
4. **AI语音通话邀请** - 引导用户拨打AI热线进行5-10分钟深度分析
5. **LinkedIn身份验证** - 通话后在Web界面提交LinkedIn链接验证
6. **生成完整画像** - 基于语音分析+LinkedIn信息生成画像卡片
7. **Web匹配服务** - 在Web界面浏览匹配、查看画像、管理偏好

### 交互模式设计
- **📱 手机短信验证**：安全的身份验证机制
- **💻 Web界面**：注册、信息收集、匹配浏览、画像展示
- **📞 AI语音通话**：一次性深度个性分析（5-10分钟）
- **🔗 LinkedIn验证**：确保信息真实性和匹配质量
- **🎯 精准匹配**：基于深度画像提供高质量推荐

## MVP 技术架构

### Web应用架构
```
Web Frontend (React/Vue) ←→ FastAPI后端 ←→ AI分析引擎
                              ├── 用户认证服务 (SMS)
                              ├── 用户管理服务
                              ├── 语音通话服务 (Twilio)
                              ├── LinkedIn验证服务
                              ├── 画像生成服务
                              └── 匹配推荐服务
```

### 核心API端点 (FastAPI)
```
/auth/register         # 手机号注册
/auth/verify-sms       # 短信验证码验证
/auth/login           # 用户登录
/webhook/twilio       # Twilio语音通话处理
/voice/analyze        # 处理AI语音通话分析
/linkedin/verify      # LinkedIn链接验证
/profile/generate     # 生成完整用户画像
/profile/cards        # 获取用户画像卡片
/matches/recommend    # 智能匹配推荐
/matches/feedback     # 匹配反馈
```

### 数据存储 (PostgreSQL + Redis)
```sql
-- PostgreSQL核心表
users (id, phone_number, email, sms_verified, voice_call_completed, linkedin_verified, created_at)
sms_verifications (id, phone_number, verification_code, expires_at, verified_at)
voice_sessions (id, user_id, call_duration, analysis_data_jsonb, completed_at)
linkedin_profiles (user_id, linkedin_url, profile_data_jsonb, verification_status)
user_profiles (user_id, voice_analysis_jsonb, linkedin_data_jsonb, final_profile_jsonb, confidence_scores_jsonb)
user_matches (user1_id, user2_id, match_score, status, created_at)

-- Redis缓存
sms_verification:{phone_number}    # 短信验证状态
user_session:{user_id}            # 用户会话状态
voice_session:{user_id}           # 语音通话会话状态
match_cache:{user_id}             # 匹配结果缓存
```

### 用户认证流程
```python
# 手机短信验证流程
sms_verification = {
    "注册流程": "用户输入手机号 → 发送验证码 → 验证码确认 → 账户创建",
    "验证码": "6位数字，5分钟有效期",
    "安全措施": "频率限制、IP限制、验证码加密存储",
    "后续验证": "LinkedIn验证（可选但推荐）"
}

# LinkedIn验证和信息对比
linkedin_verification = {
    "数据收集": "用户在Web界面提交LinkedIn链接后自动抓取公开信息",
    "关键对比维度": {
        "基本信息": "姓名、城市、年龄范围",
        "职业背景": "当前职位、公司、工作经历",
        "教育背景": "学校、专业、毕业时间",
        "技能兴趣": "专业技能、关注领域、活动参与",
        "个性特征": "自我描述、价值观表达"
    },
    "验证等级": "高度一致(>90%) / 部分一致(60-90%) / 不一致(<60%)",
    "用户标识": "✅已验证 / ⏳部分验证 / ❓未验证"
}
```

## MVP 画像系统

### Dating专用画像维度（主要通过语音分析）
```python
dating_profile_dimensions = {
    "性格特质": "通过语音对话风格和回答模式分析MBTI",
    "兴趣爱好": "从语音对话中提及的活动和偏好提取",
    "生活方式": "从日常描述推断 (宅家型/社交型/户外型)",
    "社交能量": "从语音互动风格和表达方式分析",
    "关系期待": "通过语音对话了解交友目标和价值观",
    "地理位置": "注册信息 + 语音对话中提及的城市和活动区域",
    "价值观": "从语音表达内容和选择偏好分析",
    "沟通风格": "语音语调、语速、表达习惯分析"
}
```

### MVP卡片系统 (5种核心卡片) - 语音分析驱动

#### 1. 性格特质卡 (MBTI核心)
- **数据源**：AI语音对话分析为主
- **示例**："ENFP - 热情的灵感催化剂" / "INTJ - 独立的战略思考者"
- **生成条件**：通过5-10分钟语音对话收集足够的性格线索
- **重要性**：匹配算法的核心依据，影响30%的匹配分数

#### 2. 兴趣爱好卡
- **数据源**：语音对话中提及的兴趣和活动
- **示例**："音乐爱好者 | 徒步达人 | 美食探索家"
- **生成条件**：语音对话中识别出3个以上明确兴趣

#### 3. 生活方式卡
- **数据源**：语音对话中描述的日常生活和偏好
- **示例**："都市探索者 | 咖啡文化爱好者"
- **生成条件**：收集到足够的生活方式信息

#### 4. 社交风格卡
- **数据源**：语音互动风格和社交偏好表达
- **示例**："深度交流偏好 | 小圈子社交"
- **生成条件**：通过语音对话风格分析得出社交特征

#### 5. 关系期待卡
- **数据源**：语音对话中的关系目标和价值观表达
- **示例**："寻求长期关系 | 重视精神契合"
- **生成条件**：用户在语音对话中表达明确的关系期待

### 分阶段交互流程

#### 阶段1：Web注册和基础信息收集
- **目标**：快速注册，收集基础信息，引导语音通话
- **流程**：
  - 手机号注册 + 短信验证
  - 基础信息填写：姓名、年龄、城市、职业
  - 引导页面：解释AI语音分析的价值和流程

#### 阶段2：AI语音通话深度分析（一次性，5-10分钟）
- **通话内容**：
  - **基本背景确认**：确认注册信息，了解职业详情
  - **MBTI性格识别**：通过语音对话风格和回答模式分析
  - **职业和成长**：工作热情、职业规划、成就感来源
  - **兴趣爱好深挖**：了解真实的兴趣和热情所在
  - **价值观探索**：通过假设情境了解核心价值观
  - **关系期待**：了解对感情和伴侣的期待
  - **生活方式**：从日常选择推断生活方式偏好

- **AI语音策略**：
  - **语言**: 英语（MVP版本）
  - **语调**: 友好温暖，像朋友聊天
  - **风格**: 自然了解职业信息（不像面试）
  - **适应性**: 根据用户回答动态调整问题
  - **时长**: 5-10分钟内获得核心个性和背景信息

#### 阶段3：LinkedIn验证（Web界面）
- **验证流程**：
  - Web界面提示："完善你的档案！添加LinkedIn链接提升匹配质量"
  - 用户在Web表单中提交LinkedIn链接
  - 自动抓取LinkedIn公开信息
  - 对比语音内容与LinkedIn资料的一致性
  - 在Web界面显示验证状态

#### 阶段4：Web匹配服务
- **画像展示**：在Web界面展示生成的个性画像卡片
- **匹配浏览**：卡片式匹配界面，支持喜欢/跳过操作
- **偏好设置**：用户可调整匹配偏好和筛选条件
- **消息功能**：匹配成功后的站内消息系统

## MVP 匹配系统

### Dating专用匹配算法
```python
def calculate_dating_match_score(user1, user2):
    score = 0

    # 地理位置匹配 (25%)
    location_score = calculate_location_compatibility(user1['location'], user2['location'])
    score += location_score * 25

    # MBTI互补性 (30%) - Dating中更重要
    mbti_compatibility = calculate_mbti_dating_compatibility(user1['mbti'], user2['mbti'])
    score += mbti_compatibility * 30

    # 兴趣爱好重叠 (25%)
    interest_overlap = calculate_interest_overlap(user1['interests'], user2['interests'])
    score += interest_overlap * 25

    # 生活方式匹配 (20%)
    lifestyle_match = calculate_lifestyle_compatibility(user1['lifestyle'], user2['lifestyle'])
    score += lifestyle_match * 20

    return min(100, score)
```

### Web匹配展示
- **卡片式界面**：类似Tinder的卡片滑动界面，展示匹配对象画像
- **详细信息**：点击查看完整画像卡片和匹配原因
- **筛选功能**：年龄、距离、职业、兴趣等多维度筛选
- **每日推荐**：2-3个高质量匹配，避免选择疲劳
- **匹配解释**：AI解释为什么推荐这个人，增加透明度

## MVP 界面和技术设计

### Web应用 + Twilio语音集成
1. **Web注册阶段** - 手机号注册、短信验证、基础信息收集
2. **Twilio语音通话** - 用户拨打AI热线进行深度个性分析
3. **LinkedIn验证阶段** - 在Web界面提交LinkedIn链接验证身份
4. **Web匹配服务** - 所有匹配浏览和交互都在Web界面进行

### 关键技术功能
- **React/Vue前端**：响应式Web界面、卡片式匹配、实时更新
- **Twilio SMS API**：手机短信验证、安全认证
- **Twilio Voice API**：AI语音通话（英语）、实时语音处理、多并发支持
- **LinkedIn数据抓取**：自动获取公开资料、信息一致性验证
- **DeepSeek AI分析**：语音内容分析（英语）、个性画像生成、匹配算法

### 用户验证体系
- **✅ 已验证用户**：手机验证 + 语音通话 + LinkedIn验证，信息一致性>90%
- **⏳ 部分验证用户**：手机验证 + 语音通话，LinkedIn信息部分匹配
- **❓ 基础用户**：仅完成手机验证，未进行深度验证

### Web界面功能
- 用户注册和认证流程
- 个人画像详细展示
- 卡片式匹配浏览界面
- 语音通话状态和结果展示
- LinkedIn验证状态管理
- 匹配历史和偏好设置
- 站内消息系统

## MVP 开发计划

### Week 1-2: 基础架构和认证系统
- Web前端框架搭建（React/Vue）
- FastAPI后端框架搭建
- PostgreSQL + Redis数据库设计
- Twilio SMS API集成（手机验证）
- 用户注册和认证流程

### Week 3-4: AI语音系统开发
- Twilio Voice API集成和测试
- 实时语音识别和AI对话
- DeepSeek分析引擎集成
- 语音分析数据存储和处理

### Week 5-6: 验证和匹配系统
- LinkedIn数据抓取和验证
- 信息一致性检查算法
- 用户画像生成和卡片系统
- Web界面匹配功能开发

### Week 7-8: 完整Web界面和优化
- 卡片式匹配界面完善
- 用户画像展示页面
- 匹配推荐算法优化
- 端到端流程测试和用户体验优化

# 未来功能扩展

## 高级画像分析

### 深度心理维度
```python
advanced_dimensions = {
    "依恋风格": "安全型/焦虑型/回避型/混乱型",
    "情感调节": "情绪稳定性、恢复速度、表达控制",
    "共情能力": "认知共情、情感共情、同情反应",
    "压力应对": "应对策略、支持寻求、心理韧性",
    "价值观体系": "人生优先级、道德框架、成功定义"
}
```

### 多模态分析
- **语言风格分析**：词汇复杂度、句式偏好、标点习惯
- **时间模式分析**：回复速度、对话频率、活跃时段
- **表情符号分析**：使用频率、情感表达范围

### LinkedIn深度挖掘
```python
advanced_linkedin_analysis = {
    "职业轨迹分析": "跳槽频率、晋升速度、行业转换",
    "社交网络质量": "连接质量、推荐信分析、影响力评估",
    "内容创作分析": "发帖频率、内容类型、互动模式",
    "学习成长轨迹": "技能发展、认证获取、教育投资"
}
```

## 高级MBTI系统

### MBTI深度分析
```python
advanced_mbti_analysis = {
    "认知功能栈": {
        "主导功能": "Te/Ti/Fe/Fi/Se/Si/Ne/Ni",
        "辅助功能": "支持主导功能的认知模式",
        "第三功能": "中年发展的认知能力",
        "劣势功能": "压力下的表现模式"
    },
    "发展阶段": {
        "青年期": "主导-辅助功能发展",
        "中年期": "第三功能整合",
        "成熟期": "四功能平衡发展"
    },
    "压力反应": {
        "轻度压力": "主导功能过度使用",
        "中度压力": "辅助功能失调",
        "重度压力": "劣势功能爆发"
    }
}
```

### MBTI匹配兼容性矩阵
```python
mbti_compatibility_matrix = {
    "完美匹配": {
        "ENFP": ["INTJ", "INFJ"],  # 理想主义者 + 建筑师
        "ENTP": ["INTJ", "INFJ"],  # 辩论家 + 建筑师
        "INFP": ["ENFJ", "ENTJ"],  # 调停者 + 主人公
        "INTP": ["ENFJ", "ENTJ"]   # 逻辑学家 + 指挥官
    },
    "高度兼容": {
        "相同类型": "深度理解，但可能缺乏互补",
        "功能互补": "主导-辅助功能互补组合",
        "认知相似": "相同认知偏好但不同表达方式"
    },
    "挑战组合": {
        "对立类型": "需要更多理解和包容",
        "功能冲突": "认知方式差异较大",
        "价值观差异": "判断功能冲突(T vs F)"
    }
}
```

### MBTI动态识别算法
```python
mbti_detection_algorithm = {
    "多轮对话验证": {
        "一致性检查": "多次对话中的类型稳定性",
        "情境测试": "不同场景下的反应模式",
        "压力测试": "压力状态下的行为表现"
    },
    "隐式特征识别": {
        "语言模式": "词汇选择、句式结构、表达习惯",
        "决策过程": "问题解决的思维路径",
        "价值表达": "重要性排序、道德判断",
        "时间管理": "计划性、灵活性、截止日期态度"
    },
    "LinkedIn数据验证": {
        "职业选择": "不同类型偏好的行业和角色",
        "职业发展": "晋升路径、跳槽模式",
        "技能组合": "硬技能vs软技能的平衡",
        "社交模式": "网络建设、内容分享风格"
    }
}
```

## 高级匹配算法

### MBTI核心匹配逻辑
```python
def calculate_mbti_compatibility(type1, type2):
    compatibility_score = 0

    # 1. 认知功能互补性 (40%)
    cognitive_complement = analyze_cognitive_functions(type1, type2)
    compatibility_score += cognitive_complement * 0.4

    # 2. 沟通风格匹配 (25%)
    communication_match = analyze_communication_styles(type1, type2)
    compatibility_score += communication_match * 0.25

    # 3. 生活方式协调 (20%)
    lifestyle_harmony = analyze_lifestyle_preferences(type1, type2)
    compatibility_score += lifestyle_harmony * 0.2

    # 4. 成长互补性 (15%)
    growth_potential = analyze_growth_compatibility(type1, type2)
    compatibility_score += growth_potential * 0.15

    return compatibility_score
```

### 关系动态预测
```python
relationship_dynamics = {
    "蜜月期": {
        "NT+NF": "思想碰撞，激发创造力",
        "ST+SF": "实用互补，稳定发展",
        "相同类型": "深度理解，快速建立连接"
    },
    "磨合期": {
        "T+F冲突": "理性vs感性的决策分歧",
        "J+P差异": "计划性vs灵活性的生活节奏",
        "E+I平衡": "社交需求的协调"
    },
    "稳定期": {
        "互补发展": "帮助对方发展弱势功能",
        "共同成长": "在关系中实现个人发展",
        "冲突解决": "基于类型特点的沟通策略"
    }
}
```

### 关系兼容性分析
```python
relationship_compatibility = {
    "沟通风格匹配": "直接性、情感同步、冲突处理",
    "亲密度偏好": "身体亲密、情感开放、个人空间",
    "生活节奏同步": "工作强度、社交频率、休息偏好",
    "成长互补性": "学习意愿、挑战支持、变化适应",
    "MBTI深度匹配": "认知功能互补、发展阶段协调、压力支持"
}
```

### 长期关系预测
- **MBTI发展轨迹匹配**：不同年龄段的类型发展预测
- **认知功能互补评估**：主导-劣势功能的相互支持
- **压力应对兼容性**：压力状态下的相互理解和支持
- **价值观一致性评估**：基于判断功能(T/F)的价值观匹配
- **生活目标匹配度**：基于感知功能(S/N)的未来规划一致性
- **冲突解决能力预测**：基于态度功能(J/P)的问题处理方式
- **关系稳定性评分**：综合MBTI兼容性的长期关系预测

## 高级用户体验

### 智能对话系统
- **情境感知对话**：根据时间、心情、话题深度调整
- **个性化问题生成**：基于用户背景定制问题
- **情感状态追踪**：识别用户当前情绪并适应

### 高级游戏化
```python
advanced_gamification = {
    "稀有卡片系统": "限时解锁、特殊条件触发",
    "画像进化树": "技能点分配、成长路径选择",
    "社交互动": "卡片交换、好友评价、群组挑战",
    "成就系统": "里程碑奖励、特殊徽章、排行榜"
}
```

### 实时适应性
- **动态阈值调整**：根据用户反馈调整分析敏感度
- **多面性格识别**：工作状态 vs 生活状态的不同表现
- **季节性模式**：识别用户行为的周期性变化

## 商业化功能

### 高级会员功能
- **无限匹配推荐**
- **高级筛选条件**
- **画像深度分析报告**
- **专属稀有卡片**
- **优先客服支持**

### 企业服务
- **团队匹配服务**：为公司提供团队建设分析
- **HR招聘辅助**：基于画像的人才匹配
- **培训定制**：个性化职业发展建议

## 技术架构升级

### 微服务架构
```
用户服务 (User Service)
画像服务 (Profile Service)
匹配服务 (Matching Service)
对话服务 (Chat Service)
推荐服务 (Recommendation Service)
通知服务 (Notification Service)
```

### AI模型优化
- **多模型集成**：DeepSeek + Claude + 本地模型
- **实时学习**：在线学习算法，持续优化
- **A/B测试框架**：不同算法效果对比

### 数据安全与隐私
- **端到端加密**：敏感数据加密存储
- **数据匿名化**：用户隐私保护
- **GDPR合规**：数据删除权、访问权实现

---

*本文档记录了从MVP到完整产品的发展路径，将根据用户反馈和市场需求持续更新*
