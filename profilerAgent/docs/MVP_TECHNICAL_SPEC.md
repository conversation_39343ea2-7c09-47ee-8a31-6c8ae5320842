# AI Dating App - Web应用 MVP 技术规格文档

## 项目概述

基于Web + Twilio语音的AI智能交友应用MVP版本。用户通过Web界面注册（手机短信验证），然后进行一次AI语音通话深度分析，结合LinkedIn验证确保真实性，最终生成精准的个性画像并在Web界面提供高质量匹配推荐。

## 技术栈

### 前端技术
- **框架**: React 18+ 或 Vue 3+
- **构建工具**: Vite
- **UI组件**: Tailwind CSS + Headless UI
- **状态管理**: Zustand (React) 或 Pinia (Vue)
- **路由**: React Router 或 Vue Router
- **HTTP客户端**: Axios

### 用户认证
- **手机验证**: Twilio SMS API
- **认证方式**: JWT Token + Refresh Token
- **会话管理**: HTTP-only Cookies + Local Storage
- **安全措施**: CSRF保护、频率限制

### Twilio集成
- **SMS验证**: Twilio SMS API
- **语音通话**: Twilio Voice API
- **功能**: AI语音通话、实时语音处理、多并发支持
- **语音识别**: 实时ASR (Azure/Google Speech) - 英语优先
- **语音合成**: 实时TTS (ElevenLabs/Azure TTS) - 英语语音

### 后端核心
- **框架**: FastAPI + Python 3.9+
- **异步处理**: asyncio + uvicorn
- **用户识别**: 手机号 + JWT Token
- **消息队列**: Celery + Redis
- **API文档**: 自动生成的OpenAPI/Swagger

### 数据存储
- **主数据库**: PostgreSQL 14+
- **缓存**: Redis 6+
- **文件存储**: 本地存储 (MVP阶段)
- **会话存储**: Redis (用户会话和临时数据)

### 外部服务
- **SMS服务**: Twilio SMS API
- **语音服务**: Twilio Voice API
- **多平台数据**: Bright Data API (LinkedIn等)
- **AI分析**: DeepSeek API

## 系统架构

### 完整系统架构图
```
[Web浏览器] ←→ [React/Vue前端] ←→ [FastAPI后端] ←→ [Twilio Voice API] ←→ [用户电话]
                                      ├── 用户认证服务 (SMS)
                                      ├── 用户管理服务
                                      ├── AI语音通话处理
                                      ├── LinkedIn验证服务
                                      ├── 画像分析服务
                                      ├── 匹配推荐服务
                                      └── 数据存储服务
                            ↓
          [PostgreSQL] + [Redis] + [DeepSeek API] + [LinkedIn API] + [Celery队列]
```

### 用户交互流程设计
```
1. Web注册流程
   手机号输入 → SMS验证码 → 验证确认 → 账户创建 → 基础信息填写

2. AI语音通话（一次性）
   Web界面引导 → 用户拨打Twilio号码 → AI语音分析 → 结果存储

3. LinkedIn验证
   Web表单提交 → 自动抓取验证 → 一致性检查 → 状态更新

4. Web匹配服务
   匹配算法 → Web界面展示 → 用户操作反馈 → 偏好更新
```

## 数据库设计

### 核心表结构

#### 用户相关
```sql
-- 用户基础信息
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone_number VARCHAR(20) UNIQUE NOT NULL, -- 主要身份标识
    email VARCHAR(255), -- 可选
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    age INTEGER,
    city VARCHAR(100),
    profession VARCHAR(200),
    sms_verified BOOLEAN DEFAULT FALSE,
    voice_call_completed BOOLEAN DEFAULT FALSE,
    linkedin_verified BOOLEAN DEFAULT FALSE,
    verification_status VARCHAR(20) DEFAULT 'pending', -- pending, sms_verified, voice_completed, linkedin_verified, fully_verified
    last_login TIMESTAMP DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW()
);

-- 短信验证
CREATE TABLE sms_verifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone_number VARCHAR(20) NOT NULL,
    verification_code VARCHAR(10) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    verified_at TIMESTAMP,
    attempts INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 语音通话会话
CREATE TABLE voice_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    twilio_call_sid VARCHAR(255),
    call_duration INTEGER, -- 通话时长（秒）
    analysis_data JSONB, -- AI分析结果
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- LinkedIn验证
CREATE TABLE linkedin_profiles (
    user_id UUID REFERENCES users(id) PRIMARY KEY,
    linkedin_url VARCHAR(500),
    profile_data JSONB, -- LinkedIn抓取的数据
    verification_status VARCHAR(20) DEFAULT 'pending', -- pending, verified, inconsistent
    consistency_score DECIMAL(3,2), -- 与语音信息的一致性分数
    verified_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 画像相关
```sql
-- 用户完整画像
CREATE TABLE user_profiles (
    user_id UUID REFERENCES users(id) PRIMARY KEY,
    voice_analysis JSONB, -- 语音通话分析结果
    linkedin_data JSONB, -- LinkedIn验证数据
    final_profile JSONB, -- 综合生成的最终画像
    confidence_scores JSONB, -- 各维度置信度
    mbti_type VARCHAR(4), -- MBTI性格类型
    verification_level VARCHAR(20), -- high, medium, low
    last_updated TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 用户偏好设置
CREATE TABLE user_preferences (
    user_id UUID REFERENCES users(id) PRIMARY KEY,
    age_range_min INTEGER DEFAULT 18,
    age_range_max INTEGER DEFAULT 45,
    max_distance INTEGER DEFAULT 50, -- 公里
    preferred_mbti_types VARCHAR(4)[],
    deal_breakers TEXT[],
    interests TEXT[],
    updated_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 画像和反馈相关
```sql
-- 用户画像卡片
CREATE TABLE profile_cards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    card_type VARCHAR(50) NOT NULL, -- 'personality', 'interests', 'lifestyle', 'social', 'relationship'
    title VARCHAR(255) NOT NULL,
    description TEXT,
    confidence DECIMAL(3,2), -- 0.00-1.00
    tags TEXT[],
    evidence TEXT[], -- 来自对话的支持证据
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 用户反馈（简化）
CREATE TABLE user_feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    card_id UUID REFERENCES profile_cards(id),
    feedback_type VARCHAR(20), -- 'like', 'dislike', 'accurate', 'inaccurate'
    comment TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 匹配相关
```sql
-- 用户匹配关系
CREATE TABLE user_matches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user1_id UUID REFERENCES users(id),
    user2_id UUID REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'recommended', -- recommended, interested, matched, passed, blocked
    match_score INTEGER, -- 0-100
    recommendation_reason TEXT, -- AI解释推荐原因
    user1_action VARCHAR(20), -- interested, passed, null
    user2_action VARCHAR(20), -- interested, passed, null
    matched_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user1_id, user2_id)
);

-- 匹配推荐历史
CREATE TABLE match_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    recommended_user_id UUID REFERENCES users(id),
    recommendation_batch VARCHAR(50), -- 每日推荐批次标识
    sent_via_whatsapp BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP,
    user_response VARCHAR(20), -- interested, passed, no_response
    response_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Redis缓存设计
```
# 用户会话管理
user_session:{user_id} → {jwt_token, login_time, last_activity, device_info}

# 短信验证缓存
sms_verification:{phone_number} → {code, expires_at, attempts, last_sent}

# Twilio语音会话状态
voice_session:{user_id} → {call_sid, session_data, analysis_progress}

# LinkedIn验证状态
linkedin_verification:{user_id} → {url, scraping_status, verification_result}

# 匹配推荐缓存
daily_recommendations:{user_id}:{date} → [recommended_user_ids]

# 匹配算法缓存
match_scores:{user_id} → {calculated_matches, last_updated, verification_level}

# API频率限制
rate_limit:{endpoint}:{user_id} → {request_count, window_start}
rate_limit:{endpoint}:{ip} → {request_count, window_start}
```

## API设计

### 用户认证相关API
```
POST /auth/register          # 手机号注册
POST /auth/send-sms          # 发送短信验证码
POST /auth/verify-sms        # 验证短信验证码
POST /auth/login             # 用户登录
POST /auth/logout            # 用户登出
POST /auth/refresh-token     # 刷新JWT Token
GET /auth/me                 # 获取当前用户信息
```

### 用户管理相关API
```
GET /users/profile           # 获取用户基础信息
PUT /users/profile           # 更新用户基础信息
GET /users/preferences       # 获取用户偏好设置
PUT /users/preferences       # 更新用户偏好设置
```

### Twilio语音相关API
```
POST /webhook/twilio         # 接收Twilio语音事件
POST /voice/handle-call      # 处理来电
POST /voice/process-speech   # 处理用户语音输入
POST /voice/end-call         # 结束通话并分析
GET /voice/status/{user_id}  # 获取语音通话状态
```

### LinkedIn验证相关API
```
POST /linkedin/submit        # 提交LinkedIn链接
GET /linkedin/verify/{user_id}  # 获取验证状态
POST /linkedin/scrape        # 抓取LinkedIn数据
POST /linkedin/validate      # 验证信息一致性
```

### 画像和匹配相关API
```
GET /profile/cards           # 获取用户画像卡片
POST /profile/generate       # 生成最终画像（语音+LinkedIn）
GET /matches/recommendations # 获取匹配推荐
POST /matches/feedback       # 匹配反馈 (喜欢/跳过)
GET /matches/history         # 获取匹配历史
```

### 管理相关API
```
GET /admin/users             # 列出用户（管理员）
GET /admin/stats/verification # 验证统计
GET /admin/stats/voice-calls  # 语音通话统计
```

## 核心业务逻辑

### 用户注册和认证流程
```python
async def register_user(phone_number: str):
    """用户注册流程"""

    # 1. 验证手机号格式
    if not is_valid_phone_number(phone_number):
        raise ValueError("Invalid phone number format")

    # 2. 检查用户是否已存在
    existing_user = await get_user_by_phone(phone_number)
    if existing_user:
        raise ValueError("User already exists")

    # 3. 生成并发送验证码
    verification_code = generate_verification_code()
    await send_sms_verification(phone_number, verification_code)

    # 4. 存储验证码（Redis缓存）
    await store_verification_code(phone_number, verification_code)

    return {"message": "Verification code sent", "expires_in": 300}

async def verify_sms_and_create_user(phone_number: str, code: str, user_data: dict):
    """验证短信并创建用户"""

    # 1. 验证验证码
    is_valid = await verify_sms_code(phone_number, code)
    if not is_valid:
        raise ValueError("Invalid or expired verification code")

    # 2. 创建用户账户
    user = await create_user({
        "phone_number": phone_number,
        "first_name": user_data["first_name"],
        "last_name": user_data["last_name"],
        "age": user_data["age"],
        "city": user_data["city"],
        "profession": user_data.get("profession"),
        "sms_verified": True
    })

    # 3. 生成JWT Token
    access_token = create_access_token(user.id)
    refresh_token = create_refresh_token(user.id)

    return {
        "user": user,
        "access_token": access_token,
        "refresh_token": refresh_token
    }

async def handle_voice_call_completion(user_id: str, call_analysis: dict):
    """处理语音通话完成后的流程"""

    # 1. 保存语音分析结果
    await save_voice_analysis(user_id, call_analysis)

    # 2. 更新用户状态
    await update_user_voice_status(user_id, completed=True)

    # 3. 发送LinkedIn验证请求
    await send_linkedin_verification_request(user_id)

    return call_analysis

async def handle_linkedin_verification(user_id: str, linkedin_url: str):
    """处理LinkedIn验证流程"""

    # 1. 抓取LinkedIn数据
    linkedin_data = await scrape_linkedin_profile(linkedin_url)

    # 2. 获取语音分析数据
    voice_analysis = await get_voice_analysis(user_id)

    # 3. 验证信息一致性
    consistency_score = await verify_information_consistency(
        voice_analysis, linkedin_data
    )

    # 4. 更新验证状态
    await update_linkedin_verification(user_id, linkedin_data, consistency_score)

    # 5. 生成最终画像
    if consistency_score > 0.6:
        final_profile = await generate_final_profile(user_id)
        await send_profile_cards(user_id, final_profile)
        await start_matching_service(user_id)

    return consistency_score

async def verify_information_consistency(voice_analysis: dict, linkedin_data: dict):
    """验证语音信息与LinkedIn信息的一致性"""

    consistency_checks = {
        "basic_info": {
            "weight": 0.3,
            "checks": [
                compare_names(voice_analysis.get("name"), linkedin_data.get("name")),
                compare_locations(voice_analysis.get("city"), linkedin_data.get("location")),
                compare_age_range(voice_analysis.get("age_hints"), linkedin_data.get("experience_years"))
            ]
        },
        "professional": {
            "weight": 0.4,
            "checks": [
                compare_job_titles(voice_analysis.get("job_title"), linkedin_data.get("current_position")),
                compare_companies(voice_analysis.get("company"), linkedin_data.get("current_company")),
                compare_industries(voice_analysis.get("industry_mentions"), linkedin_data.get("industry")),
                compare_career_stage(voice_analysis.get("career_level"), linkedin_data.get("experience_level"))
            ]
        },
        "interests_skills": {
            "weight": 0.2,
            "checks": [
                compare_interests(voice_analysis.get("interests"), linkedin_data.get("interests")),
                compare_skills(voice_analysis.get("mentioned_skills"), linkedin_data.get("skills")),
                compare_activities(voice_analysis.get("activities"), linkedin_data.get("volunteer_experience"))
            ]
        },
        "personality": {
            "weight": 0.1,
            "checks": [
                compare_communication_style(voice_analysis.get("communication_style"), linkedin_data.get("summary_tone")),
                compare_values(voice_analysis.get("values"), linkedin_data.get("causes"))
            ]
        }
    }

    total_score = 0
    for category, data in consistency_checks.items():
        category_score = sum(data["checks"]) / len(data["checks"])
        weighted_score = category_score * data["weight"]
        total_score += weighted_score

    return total_score
```

### Twilio语音通话处理流程
```python
async def handle_twilio_voice_call(call_sid: str, from_number: str):
    """处理Twilio语音通话"""

    # 1. 识别用户（通过电话号码）
    user = await identify_user_by_phone(from_number)
    if not user:
        await play_error_message(call_sid, "请先通过WhatsApp联系我们")
        return

    # 2. 开始AI语音对话
    conversation_data = await conduct_ai_voice_interview(call_sid, user.id)

    # 3. 分析语音内容
    voice_analysis = await analyze_voice_conversation(conversation_data)

    # 4. 保存分析结果
    await save_voice_session(user.id, call_sid, voice_analysis)

    # 5. 通知WhatsApp语音分析完成
    await notify_voice_completion(user.whatsapp_id)

    return voice_analysis

async def conduct_ai_voice_interview(call_sid: str, user_id: str):
    """进行AI语音面试"""

    # 结构化对话流程（英语版本）
    conversation_stages = {
        "opening": [
            "Hi! Great to chat with you! Just relax, this is like talking to a friend.",
            "Could you start by telling me a bit about yourself? Like your name and which city you're in?"
        ],
        "professional": [
            "That sounds great! What kind of work do you do?",
            "Do you enjoy your current job? What gives you a sense of achievement?",
            "How did you get into this field? Any interesting experiences along the way?"
        ],
        "personality": [
            "Do you prefer hanging out with friends or having some alone time?",
            "When making important decisions, how do you usually approach it?",
            "How do you typically handle stress or pressure?"
        ],
        "interests": [
            "Outside of work, what gets you most excited?",
            "How do you like to spend your weekends?",
            "Is there something you've always wanted to learn but haven't started yet?"
        ],
        "relationships": [
            "What does an ideal relationship look like to you?",
            "What qualities matter most to you in a partner?"
        ]
    }

    conversation_data = []
    current_stage = "opening"

    for stage, questions in conversation_stages.items():
        for question in questions:
            # AI说话
            await speak_to_caller(call_sid, question)

            # 听用户回答
            user_response = await listen_for_response(call_sid)

            # 实时分析
            analysis = await analyze_response_realtime(user_response, stage)
            conversation_data.append({
                "stage": stage,
                "question": question,
                "answer": user_response,
                "analysis": analysis
            })

            # 根据回答动态调整后续问题
            if should_ask_followup(analysis):
                followup = await generate_followup_question(user_response, stage)
                await speak_to_caller(call_sid, followup)
                followup_response = await listen_for_response(call_sid)
                conversation_data.append({
                    "stage": stage,
                    "question": followup,
                    "answer": followup_response,
                    "analysis": await analyze_response_realtime(followup_response, stage)
                })

    # 结束通话（英语）
    await speak_to_caller(call_sid, "Thank you so much for sharing! I'll send the analysis results to your WhatsApp.")
    await end_call(call_sid)

    return conversation_data


```

### Dating匹配算法
```python
def calculate_dating_match_score(user1: User, user2: User) -> int:
    score = 0

    # 地理位置匹配 (25%)
    location_score = calculate_location_compatibility(user1.city, user2.city)
    score += location_score * 0.25

    # MBTI兼容性 (30%) - Dating中更重要
    mbti_match = calculate_mbti_dating_compatibility(user1.mbti, user2.mbti)
    score += mbti_match * 0.30

    # 兴趣爱好重叠 (25%)
    interest_match = calculate_interest_overlap(user1.interests, user2.interests)
    score += interest_match * 0.25

    # 生活方式匹配 (20%)
    lifestyle_match = calculate_lifestyle_compatibility(user1.lifestyle, user2.lifestyle)
    score += lifestyle_match * 0.20

    return min(100, int(score))

async def daily_matching_recommendations(user_id: str):
    # 1. 获取用户画像
    user_profile = await get_user_complete_profile(user_id)

    # 2. 筛选候选用户
    candidates = await get_potential_matches(user_id, user_profile)

    # 3. 计算匹配分数
    scored_matches = []
    for candidate in candidates:
        score = calculate_dating_match_score(user_profile, candidate)
        if score >= 70:  # 只推荐高分匹配
            scored_matches.append((candidate, score))

    # 4. 排序并选择前3名
    top_matches = sorted(scored_matches, key=lambda x: x[1], reverse=True)[:3]

    # 5. 生成推荐消息并发送
    for match, score in top_matches:
        recommendation_msg = await generate_match_recommendation_message(match, score)
        await send_whatsapp_message(user_profile.whatsapp_id, recommendation_msg)
        await log_recommendation(user_id, match.id, score)

    return top_matches
```

## 部署架构

### MVP部署方案
```
单服务器部署:
├── Nginx (反向代理 + SSL终端)
├── FastAPI应用 (Uvicorn + Gunicorn)
├── PostgreSQL数据库
├── Redis缓存
├── Celery Worker (异步任务处理)
└── WhatsApp Business API集成
```

### 环境配置
```bash
# 生产环境变量
DATABASE_URL=postgresql://user:pass@localhost/dating_app
REDIS_URL=redis://localhost:6379
DEEPSEEK_API_KEY=your_deepseek_key

# Twilio配置
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number  # 语音通话号码
TWILIO_SMS_SERVICE_SID=your_sms_service_sid   # SMS服务ID
TWILIO_WEBHOOK_URL=your_webhook_url

# 语音服务配置
VOICE_LANGUAGE=en-US  # MVP版本使用英语
VOICE_MODEL=elevenlabs_english  # 英语语音模型
ASR_LANGUAGE=en-US  # 语音识别语言

# LinkedIn数据抓取
LINKEDIN_SCRAPER_API=your_linkedin_scraper_api
BRIGHT_DATA_API_KEY=your_bright_data_key

# 安全配置
JWT_SECRET_KEY=your_jwt_secret_key
JWT_REFRESH_SECRET_KEY=your_jwt_refresh_secret
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
WEBHOOK_SECRET=your_webhook_secret

# 前端配置
FRONTEND_URL=http://localhost:3000  # 开发环境
CORS_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]

# SMS验证配置
SMS_CODE_EXPIRE_MINUTES=5
SMS_MAX_ATTEMPTS=3
SMS_RATE_LIMIT_PER_HOUR=5
```

## 性能和扩展考虑

### 性能目标
- Web API响应时间: < 500ms
- 用户认证响应: < 200ms
- AI语音分析: < 5秒
- 匹配算法计算: < 2秒
- 数据库查询: < 100ms
- 并发Web用户: 1000+
- 前端页面加载: < 2秒

### 扩展策略
- 数据库读写分离
- Redis集群 (会话和缓存)
- Celery分布式任务队列
- CDN静态资源加速
- AI分析结果缓存
- 前端代码分割和懒加载

## 安全考虑

### 数据安全
- 所有API使用HTTPS
- WhatsApp Webhook签名验证
- JWT token过期机制
- 敏感数据加密存储
- SQL注入防护

### 隐私保护
- 最小化数据收集原则
- 用户明确授权机制
- 敏感数据加密存储
- 数据删除权实现 (GDPR合规)
- 用户隐私设置控制

### Web应用安全
- HTTPS强制使用
- CSRF保护
- XSS防护
- SQL注入防护
- 速率限制
- JWT Token安全管理

## 监控和日志

### 关键指标
- 用户注册转化率
- SMS验证成功率
- 语音通话完成率
- AI画像分析准确率
- 匹配推荐点击率
- Web应用响应时间
- 用户留存率
- 错误率和异常监控

### 日志策略
- 结构化日志 (JSON格式)
- 分级日志 (ERROR, WARN, INFO, DEBUG)
- 用户行为分析日志 (脱敏)
- API请求日志
- 性能监控日志
- 安全事件日志
- 日志轮转和归档

---

*本文档将随着MVP开发进展持续更新*
