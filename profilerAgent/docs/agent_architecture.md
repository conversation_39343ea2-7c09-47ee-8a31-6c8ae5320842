# Agent 模块架构文档

## 概述

Agent 模块是 AI 约会应用的核心智能系统，负责用户认证、AI 分析、匹配推荐等核心业务逻辑。采用模块化设计，支持工厂模式初始化和统一管理。

## 目录结构

```
agent/
├── __init__.py                 # 模块入口，导出主要组件
├── agent_interface.py          # 主要 API 接口
├── agent_factory.py           # 工厂模式，统一初始化管理
├── config/                     # 配置管理
│   ├── __init__.py
│   ├── core_config.py         # 核心配置（数据库、Redis、API密钥）
│   ├── service_config.py      # 服务配置（Twilio、LinkedIn）
│   └── templates.py           # AI 模板和提示词
├── core/                      # 核心业务逻辑
│   ├── __init__.py
│   ├── analysis_engine.py     # AI 分析引擎（DeepSeek集成）
│   ├── database.py           # 数据库管理器
│   ├── match_engine.py       # 匹配算法引擎
│   └── user_manager.py       # 用户管理和对话管理
├── services/                  # 外部服务集成
│   ├── __init__.py
│   ├── linkedin_service.py   # LinkedIn OAuth 和数据抓取
│   ├── sms_service.py        # SMS 验证服务（Twilio）
│   └── voice_service.py      # 语音通话服务（Twilio）
└── utils/                    # 工具和数据模型
    ├── __init__.py
    └── data_models.py        # 数据模型定义
```

## 核心组件

### 1. AgentFactory (agent_factory.py)
**职责**: 统一初始化和管理所有 Agent 组件

**主要功能**:
- 工厂模式初始化整个系统
- 组件依赖注入和配置
- 系统生命周期管理
- 健康状态监控

**关键方法**:
```python
async def initialize_system(redis_client=None) -> Dict[str, Any]
def get_component(component_name: str)
def get_agent_interface() -> AgentInterface
async def cleanup()
```

### 2. AgentInterface (agent_interface.py)
**职责**: 对外提供统一的 API 接口

**主要功能**:
- 用户注册和认证流程
- SMS 验证管理
- 语音通话处理
- LinkedIn 集成
- 健康检查

**认证流程**:
```
注册 → SMS验证 → 语音面试 → LinkedIn验证 → 完全验证
```

### 3. 配置管理 (config/)

#### CoreConfig (core_config.py)
- 环境变量加载（正确路径：../profilerAgent/.env）
- 数据库配置（PostgreSQL + Redis）
- API 密钥管理（DeepSeek、Bright Data）
- 应用基础配置

#### ServiceConfig (service_config.py)
- Twilio 配置（SMS + 语音）
- LinkedIn OAuth 配置
- 外部服务验证和健康检查

#### Templates (templates.py)
- AI 分析提示词模板
- MBTI 分析模板
- 匹配算法参数

### 4. 核心业务逻辑 (core/)

#### UserManager (user_manager.py)
**职责**: 用户生命周期管理

**主要功能**:
- 用户创建和状态管理
- 权限控制系统
- 对话历史管理
- JWT Token 生成
- 事件系统

**用户状态流转**:
```
INACTIVE → ACTIVE (SMS验证)
验证状态: pending → sms_verified → voice_completed → linkedin_verified → fully_verified
```

#### AnalysisEngine (analysis_engine.py)
**职责**: AI 分析和画像生成

**主要功能**:
- DeepSeek API 集成
- 语音转录分析
- MBTI 性格分析
- 兴趣和职业画像生成
- 置信度评估

**分析流程**:
```
语音/文本输入 → DeepSeek分析 → MBTI维度 → 画像卡片 → 置信度评分
```

#### MatchEngine (match_engine.py)
**职责**: 智能匹配算法

**匹配权重**:
- MBTI 兼容性: 35%
- 兴趣重叠: 25%
- 职业兼容性: 20%
- 地理位置: 15%
- 性格相似度: 5%

#### DatabaseManager (database.py)
**职责**: 统一数据库访问层

**主要功能**:
- 异步数据库操作
- 用户数据 CRUD
- SMS 验证记录管理
- 消息历史存储
- 内存备用存储（MVP模式）

### 5. 外部服务集成 (services/)

#### SMSService (sms_service.py)
**职责**: SMS 验证服务

**主要功能**:
- Twilio SMS 发送
- 验证码生成和验证
- 频率限制控制
- 测试模式支持

#### VoiceService (voice_service.py)
**职责**: 语音通话服务

**主要功能**:
- Twilio 语音通话
- TwiML 响应生成
- 录音处理
- 语音转录集成

#### LinkedInService (linkedin_service.py)
**职责**: LinkedIn 集成

**主要功能**:
- OAuth 2.0 流程
- 用户资料抓取
- Bright Data 集成
- 职业信息验证

### 6. 数据模型 (utils/data_models.py)

#### 核心模型
- **User**: 用户主模型
- **SMSVerification**: SMS 验证记录
- **VoiceAnalysis**: 语音分析结果
- **LinkedInProfile**: LinkedIn 资料
- **ProfileCard**: 用户画像卡片
- **MatchResult**: 匹配结果

#### 权限系统
- **UserPermission**: 用户权限枚举
- **USER_PERMISSIONS**: 用户状态权限映射
- **VERIFICATION_PERMISSIONS**: 验证状态权限映射

## 初始化流程

### 使用工厂模式（推荐）
```python
from agent import initialize_agent_system, get_agent_interface

# 初始化整个系统
await initialize_agent_system(redis_client)

# 获取接口
agent = get_agent_interface()

# 使用
result = agent.register_user(registration_data)
```

### 直接使用组件
```python
from agent import UserManager, SMSService, AnalysisEngine

user_manager = UserManager()
sms_service = SMSService()
analysis_engine = AnalysisEngine()
```

## 配置要求

### 环境变量 (.env)
```bash
# 数据库
DATABASE_URL=postgresql://dating_app_user:dating_app_password@localhost:5432/dating_app_db
REDIS_URL=redis://localhost:6380/0

# AI 服务
DEEPSEEK_API_KEY=your_deepseek_key

# Twilio
TWILIO_MODE=test
TWILIO_TEST_ACCOUNT_SID=your_test_sid
TWILIO_TEST_AUTH_TOKEN=your_test_token
TWILIO_SMS_SERVICE_SID=your_sms_service_sid
TWILIO_VOICE_PHONE_NUMBER=your_voice_number

# LinkedIn
LINKEDIN_CLIENT_ID=your_client_id
LINKEDIN_CLIENT_SECRET="your_client_secret"
LINKEDIN_REDIRECT_URI=http://localhost:8000/auth/linkedin/callback
```

## 测试

### 运行测试
```bash
# 完整系统测试
python profilerAgent/test_agent_fixes.py

# 认证流程测试
python profilerAgent/test_auth_flow.py

# 真实环境测试
python profilerAgent/test_real_sms.py
```

### 测试覆盖
- ✅ 组件导入和初始化
- ✅ 配置加载和验证
- ✅ 数据库连接和操作
- ✅ 用户管理和权限
- ✅ SMS 服务集成
- ✅ 系统工厂模式

## 性能特点

- **异步支持**: 所有 I/O 操作支持异步
- **缓存策略**: Redis 缓存 + 内存备用
- **错误处理**: 完善的异常处理和降级机制
- **可扩展性**: 模块化设计，易于扩展新功能
- **测试友好**: 支持测试模式和模拟数据

## 下一步开发

1. **真实数据库集成** - 替换内存存储为真实 PostgreSQL 操作
2. **性能优化** - 缓存策略优化和查询优化
3. **监控集成** - 添加 metrics 和 logging
4. **安全加固** - 加强权限控制和数据验证
5. **扩展功能** - 添加更多 AI 分析维度
