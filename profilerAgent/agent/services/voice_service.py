"""
VoiceService - Twilio语音处理
处理语音通话、转录和分析
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
import json
import random
from collections import defaultdict

# Twilio SDK导入
try:
    from twilio.rest import Client as TwilioSDKClient
    TWILIO_SDK_AVAILABLE = True
except ImportError:
    TWILIO_SDK_AVAILABLE = False
    logger.warning("Twilio SDK not available. Outgoing calls will not work.")

from ..config.service_config import ServiceConfig
# Templates import removed - now using intelligent ConversationAgent instead of hardcoded templates
from ..utils import (
    VoiceAnalysis, User, UserStatus, VerificationStatus, ConversationStage,
    APIResponse, EventType
)
from .ai_service import AIService
from .conversation_agent import ConversationAgent, ConversationContext, AgentDecision

logger = logging.getLogger(__name__)

class CallSession:
    """通话会话数据"""
    def __init__(self, call_sid: str, user_id: str, from_number: str):
        self.call_sid = call_sid
        self.user_id = user_id
        self.from_number = from_number
        self.start_time = datetime.now()
        self.current_stage = "greeting"  # 简化为字符串，由Agent管理
        self.questions_asked = 0
        self.responses_received = 0
        self.conversation_transcript = []
        self.collected_info = {}
        self.last_question = ""
        self.recording_url: Optional[str] = None
        self.recording_duration: int = 0
        self.call_status: str = "initiated"
        self.timeout_count: int = 0

    def add_exchange(self, question: str, response: str = ""):
        """添加问答交换"""
        self.conversation_transcript.append({
            "timestamp": datetime.now().isoformat(),
            "question": question,
            "response": response,
            "stage": self.current_stage
        })

        if question:
            self.questions_asked += 1
            self.last_question = question

        if response:
            self.responses_received += 1

    def get_duration(self) -> int:
        """获取通话时长（秒）"""
        return int((datetime.now() - self.start_time).total_seconds())

    def get_transcript_text(self) -> str:
        """获取完整转录文本"""
        transcript = ""
        for exchange in self.conversation_transcript:
            if exchange["question"]:
                transcript += f"Assistant: {exchange['question']}\n"
            if exchange["response"]:
                transcript += f"User: {exchange['response']}\n"
        return transcript

    def to_dict(self) -> dict:
        """序列化Session为字典"""
        return {
            "call_sid": self.call_sid,
            "user_id": self.user_id,
            "from_number": self.from_number,
            "start_time": self.start_time.isoformat(),
            "current_stage": self.current_stage,
            "questions_asked": self.questions_asked,
            "responses_received": self.responses_received,
            "conversation_transcript": self.conversation_transcript,
            "collected_info": self.collected_info,
            "last_question": self.last_question,
            "recording_url": self.recording_url,
            "recording_duration": self.recording_duration,
            "call_status": self.call_status,
            "timeout_count": self.timeout_count
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'CallSession':
        """从字典反序列化Session"""
        session = cls(
            call_sid=data["call_sid"],
            user_id=data["user_id"],
            from_number=data["from_number"]
        )

        # 恢复时间戳
        if "start_time" in data:
            session.start_time = datetime.fromisoformat(data["start_time"].replace('Z', '+00:00'))

        # 恢复状态
        session.current_stage = data.get("current_stage", "greeting")
        session.questions_asked = data.get("questions_asked", 0)
        session.responses_received = data.get("responses_received", 0)
        session.conversation_transcript = data.get("conversation_transcript", [])
        session.collected_info = data.get("collected_info", {})
        session.last_question = data.get("last_question", "")
        session.recording_url = data.get("recording_url")
        session.recording_duration = data.get("recording_duration", 0)
        session.call_status = data.get("call_status", "initiated")
        session.timeout_count = data.get("timeout_count", 0)

        return session

class TwilioClient:
    """Twilio API客户端"""

    def __init__(self, config: Dict[str, Any]):
        self.account_sid = config["account_sid"]
        self.auth_token = config["auth_token"]
        self.phone_number = config["phone_number"]
        self.webhook_url = config.get("webhook_url", "")

        # 语音配置
        self.voice_language = config["voice_language"]
        self.voice_model = config["voice_model"]
        self.speech_timeout = config["speech_timeout"]
        self.confidence_threshold = config["confidence_threshold"]
        self.max_call_duration = config["max_call_duration"]

        # 初始化Twilio SDK客户端
        if TWILIO_SDK_AVAILABLE:
            self.client = TwilioSDKClient(self.account_sid, self.auth_token)
            logger.info(f"Twilio client initialized with SDK: {self.phone_number}")
        else:
            self.client = None
            logger.warning(f"Twilio client initialized without SDK: {self.phone_number}")

        logger.info(f"Twilio client initialized: {self.phone_number}")

    def generate_twiml_say(self, message: str, voice: str = "alice") -> str:
        """生成TwiML Say指令"""
        return f'<Say voice="{voice}" language="{self.voice_language}">{message}</Say>'

    def generate_twiml_gather(self, message: str, action_url: str,
                             timeout: int = 5, speech_timeout: str = "auto",
                             include_timeout_handling: bool = True) -> str:
        """生成TwiML Gather指令"""
        twiml = f'''<Gather input="speech" action="{action_url}" timeout="{timeout}"
                speechTimeout="{speech_timeout}" language="{self.voice_language}">
            <Say voice="alice" language="{self.voice_language}">{message}</Say>
        </Gather>'''

        if include_timeout_handling:
            twiml += f'''
        <Say voice="alice" language="{self.voice_language}">I didn't hear you. Let me try asking again.</Say>
        <Redirect>{self.webhook_url}/voice/webhook/timeout</Redirect>'''

        return twiml

    def generate_twiml_hangup(self, message: str = "") -> str:
        """生成TwiML Hangup指令"""
        twiml = ""
        if message:
            twiml += self.generate_twiml_say(message)
        twiml += "<Hangup/>"
        return twiml

    def create_twiml_response(self, content: str) -> str:
        """创建完整的TwiML响应"""
        return f'<?xml version="1.0" encoding="UTF-8"?><Response>{content}</Response>'

    def initiate_outbound_call(self, to_number: str, webhook_url: str) -> Optional[str]:
        """发起去电"""
        if not self.client:
            logger.error("Twilio SDK not available for outbound calls")
            return None

        try:
            # 构建status callback URL
            status_callback_url = f"{self.webhook_url}/voice/webhook/status"

            call = self.client.calls.create(
                url=webhook_url,  # outgoing call的webhook URL
                to=to_number,     # 用户号码
                from_=self.phone_number,  # 系统号码
                status_callback=status_callback_url,  # 状态回调URL
                status_callback_event=['initiated', 'ringing', 'answered', 'completed', 'busy', 'no-answer', 'failed', 'canceled'],  # 监听所有状态事件
                status_callback_method='POST'  # 使用POST方法
            )
            logger.info(f"Outbound call initiated: {call.sid} to {to_number}")
            logger.info(f"Status callback configured: {status_callback_url}")
            logger.info(f"Status events monitored: ['initiated', 'ringing', 'answered', 'completed', 'busy', 'no-answer', 'failed', 'canceled']")
            return call.sid
        except Exception as e:
            logger.error(f"Failed to initiate outbound call to {to_number}: {e}")
            return None

class VoiceService:
    """语音服务 - MVP版本"""

    def __init__(self, user_manager, analysis_engine):
        """初始化语音服务"""
        self.user_manager = user_manager
        self.analysis_engine = analysis_engine

        # 初始化Twilio客户端
        twilio_config = ServiceConfig.get_twilio_voice_config()
        self.twilio_client = TwilioClient(twilio_config)

        # 初始化AI服务
        self.ai_service = AIService()
        
        # 初始化智能对话Agent (传递数据库管理器)
        db_manager = getattr(self.user_manager, 'db_manager', None)
        self.conversation_agent = ConversationAgent(self.ai_service, db_manager)

        # 通话会话管理
        self._active_sessions: Dict[str, CallSession] = {}
        self._completed_sessions: Dict[str, CallSession] = {}
        
        # Agent上下文管理
        self._agent_contexts: Dict[str, ConversationContext] = {}

        # 面试配置
        self.min_responses_per_stage = 1
        self.max_questions_per_stage = 3
        self.max_total_duration = self.twilio_client.max_call_duration
        
        # Emergency Response System
        self.emergency_responses = [
            "I understand. Could you tell me more about that?",
            "That's interesting. What else would you like to share?",
            "Thank you for sharing. Could you elaborate on that?",
            "I see. Is there anything else important I should know?",
            "That's helpful. What other details can you share?"
        ]
        
        # Agent failure monitoring
        self.agent_failures = defaultdict(int)  # call_sid -> failure_count
        self.agent_failure_reasons = defaultdict(list)  # call_sid -> [error_messages]

        logger.info("VoiceService initialized")

    # ============ ConversationRelay Support Methods ============

    async def get_user_by_call_sid(self, call_sid: str) -> Optional[str]:
        """Get user ID by call SID for ConversationRelay"""
        try:
            # Check active sessions first
            session = self._active_sessions.get(call_sid)
            if session:
                return session.user_id

            # Try to load from database
            session = await self._load_session_from_db(call_sid)
            if session:
                # Restore to active sessions
                self._active_sessions[call_sid] = session
                return session.user_id

            logger.warning(f"No user found for call_sid: {call_sid}")
            return None

        except Exception as e:
            logger.error(f"Error finding user by call_sid {call_sid}: {e}")
            return None

    async def process_voice_conversation(self, user_id: str, user_text: str, call_sid: str) -> dict:
        """Process voice conversation for ConversationRelay"""
        try:
            logger.info(f"Processing ConversationRelay input for user {user_id}: '{user_text}'")

            # Get or create session
            session = await self._get_or_load_session(call_sid)
            if not session:
                # Create new session for ConversationRelay
                user = await self.user_manager.get_user(user_id)
                if not user:
                    return {
                        "success": False,
                        "response": "I'm sorry, I couldn't find your profile. Please try again later.",
                        "should_end": True,
                        "confidence": 0.0
                    }

                session = CallSession(call_sid, user_id, user.phone_number or "relay_user")
                self._active_sessions[call_sid] = session
                await self._save_session_to_db(session)

            # Record user response
            session.add_exchange("", user_text)

            # Get next question using AI
            next_question = await self._get_next_question_with_ai(session, user_text)

            # Update session in database
            await self._update_session_in_db(session)

            # Prepare response
            if next_question is None:
                # Conversation should end
                return {
                    "success": True,
                    "response": "Thank you so much for sharing! I have all the information I need to create your profile. You'll see the results in your dashboard shortly.",
                    "should_end": True,
                    "confidence": 0.9
                }
            else:
                # Continue conversation
                session.last_question = next_question
                return {
                    "success": True,
                    "response": next_question,
                    "should_end": False,
                    "confidence": 0.8
                }

        except Exception as e:
            logger.error(f"Error processing ConversationRelay input: {e}")
            return {
                "success": False,
                "response": "I'm sorry, I didn't quite catch that. Could you please repeat?",
                "should_end": False,
                "confidence": 0.3
            }
    
    def _session_to_context(self, session: CallSession) -> ConversationContext:
        """Convert CallSession to ConversationContext for Agent"""
        if session.call_sid in self._agent_contexts:
            context = self._agent_contexts[session.call_sid]
            # Update time remaining
            context.time_remaining = max(0, self.max_total_duration - session.get_duration())
            context.questions_asked = session.questions_asked
            context.successful_responses = session.responses_received
            context.timeout_count = session.timeout_count
            return context
        
        # Create new context
        context = ConversationContext(
            call_sid=session.call_sid,
            user_id=session.user_id,
            conversation_stage=session.current_stage,
            collected_info=session.collected_info.copy(),
            conversation_history=[],
            emotional_state={"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5},
            time_remaining=max(0, self.max_total_duration - session.get_duration()),
            questions_asked=session.questions_asked,
            successful_responses=session.responses_received,
            timeout_count=session.timeout_count
        )
        
        # Convert transcript to conversation history
        for exchange in session.conversation_transcript:
            context.conversation_history.append({
                "timestamp": exchange.get("timestamp", ""),
                "question": exchange.get("question", ""),
                "response": exchange.get("response", ""),
                "stage": exchange.get("stage", session.current_stage)
            })
        
        self._agent_contexts[session.call_sid] = context
        return context
    
    def _update_session_from_context(self, session: CallSession, context: ConversationContext):
        """Update CallSession from ConversationContext after Agent processing"""
        # Update collected info
        session.collected_info.update(context.collected_info)
        
        # Update stage if changed
        if context.conversation_stage != session.current_stage:
            session.current_stage = context.conversation_stage
        
        # Update counters
        session.questions_asked = context.questions_asked
        session.responses_received = context.successful_responses
        session.timeout_count = context.timeout_count
        
        # Sync conversation history
        if len(context.conversation_history) > len(session.conversation_transcript):
            # Add new exchanges
            for i in range(len(session.conversation_transcript), len(context.conversation_history)):
                exchange = context.conversation_history[i]
                session.conversation_transcript.append(exchange)

    async def handle_incoming_call(self, call_sid: str, from_number: str,
                           caller_name: str = None) -> str:
        """处理来电"""
        try:
            # 查找用户
            user = await self.user_manager.get_user_by_phone(from_number)
            if not user:
                # 未知用户，礼貌拒绝
                message = "Thank you for calling. Please complete your registration on our website first to begin your profile setup."
                return self.twilio_client.create_twiml_response(
                    self.twilio_client.generate_twiml_hangup(message)
                )

            # 检查用户状态 - 使用与SMS服务相同的验证逻辑
            if not user.sms_verified:
                message = "Thank you for calling! Please complete your phone verification first, or you've already completed your voice interview. Check your profile for results!"
                return self.twilio_client.create_twiml_response(
                    self.twilio_client.generate_twiml_hangup(message)
                )
                
            if user.voice_call_completed:
                message = "Thank you for answering! You have already completed your voice interview. Check your profile for results!"
                return self.twilio_client.create_twiml_response(
                    self.twilio_client.generate_twiml_hangup(message)
                )

            # 创建通话会话
            session = CallSession(call_sid, user.user_id, from_number)
            self._active_sessions[call_sid] = session

            # 立即保存Session到数据库
            await self._save_session_to_db(session)

            # 使用ConversationAgent生成开场问题
            try:
                context = self._session_to_context(session)
                decision = await self.conversation_agent.process_input("", context)
                greeting = decision.content
                logger.info(f"Agent generated greeting: {greeting}")
            except Exception as e:
                logger.warning(f"Agent greeting failed, using simple greeting: {e}")
                self._record_agent_failure(session, e)
                greeting = "Hello! I'm here to help you create your dating profile. Could you start by telling me a bit about yourself?"
            
            session.add_exchange(greeting)

            # 更新Session状态到数据库
            await self._update_session_in_db(session)

            # 生成TwiML响应
            action_url = f"{self.twilio_client.webhook_url}/voice/webhook/speech"
            twiml_content = self.twilio_client.generate_twiml_gather(
                greeting, action_url, timeout=15, speech_timeout="5"
            )

            logger.info(f"Started voice interview for user {user.user_id}, call {call_sid}")
            return self.twilio_client.create_twiml_response(twiml_content)

        except Exception as e:
            logger.error(f"Incoming call handling failed: {e}")
            error_message = "Sorry, we're experiencing technical difficulties. Please try again later."
            return self.twilio_client.create_twiml_response(
                self.twilio_client.generate_twiml_hangup(error_message)
            )

    async def handle_outgoing_call(self, call_sid: str, to_number: str, from_number: str = None) -> str:
        """处理去电 - 系统主动拨打用户"""
        try:
            # 查找用户 - 注意这里to_number是用户号码
            user = await self.user_manager.get_user_by_phone(to_number)
            if not user:
                # 未知用户，礼貌结束
                message = "Thank you for answering. However, we couldn't find your registration. Please visit our website to register first."
                return self.twilio_client.create_twiml_response(
                    self.twilio_client.generate_twiml_hangup(message)
                )

            # 检查用户状态
            if not user.sms_verified:
                message = "Thank you for answering! Please complete your phone verification first through our website."
                return self.twilio_client.create_twiml_response(
                    self.twilio_client.generate_twiml_hangup(message)
                )

            # 检查是否已完成语音面试
            if user.voice_call_completed:
                message = "Thank you for answering! You have already completed your voice interview. Check your profile for results!"
                return self.twilio_client.create_twiml_response(
                    self.twilio_client.generate_twiml_hangup(message)
                )

            # 创建通话会话
            session = CallSession(call_sid, user.user_id, to_number)
            self._active_sessions[call_sid] = session

            # 立即保存Session到数据库
            await self._save_session_to_db(session)

            # 生成欢迎消息和第一个问题 - 去电的欢迎语稍有不同
            greeting = "Hello! Thank you for answering. This is your scheduled voice interview for your dating profile. Are you ready to begin? Just speak naturally, and I'll ask you a few questions about yourself."
            session.add_exchange(greeting)

            # 更新Session状态到数据库
            await self._update_session_in_db(session)

            # 生成TwiML响应
            action_url = f"{self.twilio_client.webhook_url}/voice/webhook/speech"
            twiml_content = self.twilio_client.generate_twiml_gather(
                greeting, action_url, timeout=15, speech_timeout="5"
            )

            logger.info(f"Started outgoing voice interview for user {user.user_id}, call {call_sid}")
            return self.twilio_client.create_twiml_response(twiml_content)

        except Exception as e:
            logger.error(f"Outgoing call handling failed for {call_sid}: {e}")
            return self.twilio_client.create_twiml_response(
                self.twilio_client.generate_twiml_hangup("Sorry, there was an error. We'll try calling you back later.")
            )

    async def process_speech_input(self, call_sid: str, speech_text: str,
                           confidence: float = 1.0) -> str:
        """处理语音输入"""
        try:
            logger.info(f"Processing speech input for call: {call_sid}")
            logger.info(f"User speech: '{speech_text}' (confidence: {confidence})")

            # 获取通话会话 - 先从内存，再从数据库
            session = await self._get_or_load_session(call_sid)
            if not session:
                logger.warning(f"No active session found for call: {call_sid}")
                return self.twilio_client.create_twiml_response(
                    self.twilio_client.generate_twiml_hangup("Session expired. Please call again.")
                )

            # 检查通话时长
            if session.get_duration() > self.max_total_duration:
                return await self._end_call_with_completion(session, "time_limit")

            # 记录用户响应
            session.add_exchange("", speech_text)
            logger.info(f"Session updated - Questions: {session.questions_asked}, Responses: {session.responses_received}")

            # 处理响应并生成下一个问题（可能使用AI）
            next_question = await self._get_next_question_with_ai(session, speech_text)
            logger.info(f"Generated next question: '{next_question}'")

            # 更新Session到数据库
            await self._update_session_in_db(session)

            if next_question is None:
                # 面试完成
                return await self._end_call_with_completion(session, "completed")

            # 生成TwiML响应
            action_url = f"{self.twilio_client.webhook_url}/voice/webhook/speech"
            twiml_content = self.twilio_client.generate_twiml_gather(
                next_question, action_url, timeout=15, speech_timeout="5"
            )

            logger.info(f"Returning TwiML for next question")
            return self.twilio_client.create_twiml_response(twiml_content)

        except Exception as e:
            logger.error(f"Speech input processing failed for {call_sid}: {e}")
            return self.twilio_client.create_twiml_response(
                self.twilio_client.generate_twiml_hangup("Sorry, there was an error. Please try again.")
            )

    async def handle_speech_timeout(self, call_sid: str) -> str:
        """处理语音输入超时"""
        try:
            logger.info(f"Handling speech timeout for call: {call_sid}")

            # 获取Session
            session = await self._get_or_load_session(call_sid)
            if not session:
                logger.warning(f"No session found for timeout call: {call_sid}")
                return self.twilio_client.create_twiml_response(
                    self.twilio_client.generate_twiml_hangup("Session expired. Please call again.")
                )

            # 增加超时计数
            session.timeout_count += 1

            # 如果超时次数太多，结束通话
            if session.timeout_count >= 3:
                logger.info(f"Too many timeouts for call {call_sid}, ending call")
                return await self._end_call_with_completion(session, "timeout")

            # 重新问同一个问题，但给出提示
            last_question = session.last_question
            if not last_question:
                last_question = "Could you tell me a bit about yourself?"

            # 生成友好的重试消息
            retry_messages = [
                f"I didn't catch that. Let me ask again: {last_question}",
                f"Sorry, I didn't hear you clearly. {last_question}",
                f"Could you please repeat that? {last_question}"
            ]

            retry_message = retry_messages[min(session.timeout_count - 1, len(retry_messages) - 1)]

            # 重要：超时重试不应该增加问题计数
            # 不调用 session.add_exchange，避免重复计数
            logger.info(f"Repeating question due to timeout (not counting): {last_question}")

            # 更新Session到数据库
            await self._update_session_in_db(session)

            # 生成TwiML响应，给更长的超时时间，但不包含timeout重定向
            action_url = f"{self.twilio_client.webhook_url}/voice/webhook/speech"
            twiml_content = self.twilio_client.generate_twiml_gather(
                retry_message, action_url, timeout=20, speech_timeout="8",
                include_timeout_handling=False
            )
            # 如果这次还超时，直接挂断
            twiml_content += f'''
            <Say voice="alice" language="{self.twilio_client.voice_language}">I'm having trouble hearing you. Let me end this call for now. Please try calling again later.</Say>
            <Hangup/>'''

            logger.info(f"Returning timeout retry TwiML for call: {call_sid}")
            return self.twilio_client.create_twiml_response(twiml_content)

        except Exception as e:
            logger.error(f"Speech timeout handling failed for {call_sid}: {e}")
            return self.twilio_client.create_twiml_response(
                self.twilio_client.generate_twiml_hangup("Sorry, there was an error. Please try again.")
            )

    async def _get_next_question_with_ai(self, session: CallSession, user_response: str) -> Optional[str]:
        """获取下一个问题 - 使用ConversationAgent"""
        try:
            # 检查用户是否请求重复问题
            if self._is_repeat_request(user_response):
                logger.info(f"User requested question repeat: {user_response}")
                last_question = session.last_question
                if last_question:
                    repeat_message = f"Of course! Let me repeat that: {last_question}"
                    logger.info(f"Repeating question (not counting): {last_question}")
                    return repeat_message
                else:
                    return "Could you tell me a bit about yourself?"

            # Convert session to agent context
            context = self._session_to_context(session)
            
            # Use ConversationAgent to process input and make decision
            logger.info(f"Using ConversationAgent for call: {session.call_sid}")
            decision = await self.conversation_agent.process_input(user_response, context)
            
            logger.info(f"Agent decision: {decision.action_type} - {decision.content} (confidence: {decision.confidence})")
            
            # Update session from context
            self._update_session_from_context(session, context)
            
            # Handle different decision types
            if decision.action_type == "end_call" or decision.next_stage == "completed":
                logger.info("Agent decided to end call")
                return None
            
            elif decision.action_type in ["ask_question", "clarify", "summarize"]:
                # Update last question
                session.last_question = decision.content
                
                # Handle stage transitions
                if decision.next_stage and decision.next_stage != context.conversation_stage:
                    logger.info(f"Agent transitioning stage: {session.current_stage} -> {decision.next_stage}")
                    session.current_stage = decision.next_stage
                    context.conversation_stage = decision.next_stage
                
                return decision.content
            
            else:
                logger.warning(f"Unknown decision type: {decision.action_type}")
                return decision.content if decision.content else "Could you tell me more about that?"

        except Exception as e:
            logger.error(f"ConversationAgent failed: {e}")
            # Record agent failure for monitoring
            self._record_agent_failure(session, e)
            # Emergency fallback - simple but reliable
            return self._emergency_response(session)

    def _emergency_response(self, session: CallSession) -> str:
        """Emergency fallback response - simple and reliable"""
        try:
            # Choose random response to avoid repetition
            response = random.choice(self.emergency_responses)
            
            # Add context-based variation
            if session.questions_asked < 2:
                response = "Could you tell me a bit about yourself?"
            elif len(session.collected_info) < 2:
                response = "That's helpful. What else would you like me to know about you?"
            
            logger.info(f"Using emergency response for call {session.call_sid}: {response}")
            return response
            
        except Exception as e:
            logger.error(f"Emergency response failed: {e}")
            # Ultimate fallback - hardcoded and guaranteed to work
            return "Could you tell me more about that?"
    
    def _record_agent_failure(self, session: CallSession, error: Exception):
        """Record agent failure for monitoring"""
        try:
            self.agent_failures[session.call_sid] += 1
            self.agent_failure_reasons[session.call_sid].append(str(error))
            
            failure_count = self.agent_failures[session.call_sid]
            logger.warning(f"Agent failure #{failure_count} for call {session.call_sid}: {error}")
            
            # Alert on repeated failures
            if failure_count >= 3:
                logger.error(f"ALERT: Agent failing repeatedly for call {session.call_sid} - {failure_count} failures")
                
        except Exception as e:
            logger.error(f"Failed to record agent failure: {e}")
    
    def get_agent_failure_stats(self, call_sid: str = None) -> dict:
        """Get agent failure statistics"""
        if call_sid:
            return {
                "call_sid": call_sid,
                "failure_count": self.agent_failures.get(call_sid, 0),
                "failure_reasons": self.agent_failure_reasons.get(call_sid, [])
            }
        else:
            total_failures = sum(self.agent_failures.values())
            total_calls = len(self.agent_failures)
            return {
                "total_failures": total_failures,
                "total_calls_with_failures": total_calls,
                "average_failures_per_call": total_failures / max(1, total_calls),
                "calls_with_failures": dict(self.agent_failures)
            }

    async def handle_call_completed(self, call_sid: str, call_status: str,
                            call_duration: str = "0") -> None:
        """处理通话完成"""
        try:
            logger.info(f"Handling call completion: {call_sid}, status: {call_status}, duration: {call_duration}")

            # 获取Session - 先从内存，再从数据库
            session = await self._get_or_load_session(call_sid)
            if not session:
                logger.warning(f"No session found for completed call: {call_sid}")
                return

            # 移动到已完成会话
            self._completed_sessions[call_sid] = session
            if call_sid in self._active_sessions:
                del self._active_sessions[call_sid]

            # 创建语音分析数据
            voice_analysis = VoiceAnalysis(
                user_id=session.user_id,
                call_sid=call_sid,
                call_duration=session.get_duration(),
                transcript=session.get_transcript_text(),
                analysis_data={
                    "collected_info": session.collected_info,
                    "stage_progression": [exchange["stage"] for exchange in session.conversation_transcript],
                    "call_status": call_status
                },
                quality_score=self._calculate_call_quality(session),
                questions_asked=session.questions_asked,
                responses_given=session.responses_received
            )

            # 触发分析
            if session.responses_received >= 3:  # 最少3个回答才进行分析
                analysis_result = self.analysis_engine.analyze_voice_transcript(
                    session.user_id, voice_analysis
                )

                # 标记语音通话完成
                await self.user_manager.mark_voice_completed(
                    session.user_id,
                    {
                        "call_sid": call_sid,
                        "duration": voice_analysis.call_duration,
                        "quality": voice_analysis.quality_score,
                        "analysis_confidence": analysis_result.get_overall_confidence()
                    }
                )

                # 保存语音会话记录到数据库
                analysis_data = {
                    "transcript": voice_analysis.transcript,
                    "collected_info": session.collected_info,
                    "stage_progression": [exchange["stage"] for exchange in session.conversation_transcript],
                    "conversation_transcript": session.conversation_transcript,
                    "quality_score": voice_analysis.quality_score,
                    "questions_asked": session.questions_asked,
                    "responses_received": session.responses_received,
                    "call_status": call_status
                }

                session_saved = await self._complete_session_in_db(
                    session, voice_analysis.call_duration, analysis_data
                )
                if session_saved:
                    logger.info(f"Voice session completed in database: {call_sid}")
                else:
                    logger.warning(f"Failed to complete voice session in database: {call_sid}")

                # 更新用户状态
                user = await self.user_manager.get_user(session.user_id)
                if user:
                    user.complete_voice_call()
                    await self.user_manager.update_user(user)
                    logger.info(f"Updated user status to REGISTERED for user: {session.user_id}")

                logger.info(f"Voice interview completed and analyzed for user: {session.user_id}")
            else:
                logger.warning(f"Insufficient responses for analysis: {session.user_id}")
                
                # 即使响应不足，也保存部分会话记录
                session_saved = await self.user_manager.db_manager.save_voice_session(
                    user_id=session.user_id,
                    call_sid=call_sid,
                    call_duration=session.get_duration(),
                    analysis_data={
                        "transcript": session.get_transcript_text(),
                        "collected_info": session.collected_info,
                        "stage_progression": [exchange["stage"] for exchange in session.conversation_transcript],
                        "conversation_transcript": session.conversation_transcript,
                        "quality_score": self._calculate_call_quality(session),
                        "questions_asked": session.questions_asked,
                        "responses_received": session.responses_received,
                        "call_status": call_status,
                        "completion_status": "insufficient_responses"
                    }
                )
                if session_saved:
                    logger.info(f"Partial voice session saved to database: {call_sid}")
                else:
                    logger.warning(f"Failed to save partial voice session: {call_sid}")

        except Exception as e:
            logger.error(f"Call completion handling failed for {call_sid}: {e}")

    def generate_error_response(self, error_message: str) -> str:
        """生成错误响应的TwiML"""
        return self.twilio_client.create_twiml_response(
            self.twilio_client.generate_twiml_hangup(error_message)
        )

    async def get_call_status(self, user_id: str) -> Dict[str, Any]:
        """获取用户通话状态"""
        try:
            # 查找活跃会话
            active_session = None
            for session in self._active_sessions.values():
                if session.user_id == user_id:
                    active_session = session
                    break

            if active_session:
                return {
                    "status": "active",
                    "call_sid": active_session.call_sid,
                    "duration": active_session.get_duration(),
                    "current_stage": active_session.current_stage,
                    "questions_asked": active_session.questions_asked,
                    "responses_received": active_session.responses_received
                }

            # 查找已完成会话
            completed_session = None
            for session in self._completed_sessions.values():
                if session.user_id == user_id:
                    completed_session = session
                    break

            if completed_session:
                return {
                    "status": "completed",
                    "call_sid": completed_session.call_sid,
                    "duration": completed_session.get_duration(),
                    "questions_asked": completed_session.questions_asked,
                    "responses_received": completed_session.responses_received,
                    "completed_at": completed_session.start_time.isoformat() if completed_session.start_time else None
                }

            return {
                "status": "not_started",
                "message": "No voice call session found for this user"
            }

        except Exception as e:
            logger.error(f"Failed to get call status for user {user_id}: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    # REMOVED: _process_user_response and _extract_information methods
    # These are now handled by ConversationAgent's intelligent processing
    # - Information extraction is done by AI in ConversationAgent
    # - Stage transitions are managed by AI decision making
    # - Question generation is context-aware and personalized

    # ✅ CONVERSATION AGENT INTEGRATION COMPLETE
    # All old template-based methods have been replaced with intelligent AI processing
    # - Stage transitions: AI-driven based on conversation flow
    # - Question generation: Context-aware and personalized
    # - Information extraction: AI-powered analysis
    # - Emotional analysis: Real-time sentiment tracking
    # - Edge case handling: Intelligent response to user frustration, hangup requests, etc.

    async def _end_call_with_completion(self, session: CallSession, reason: str) -> str:
        """结束通话并完成面试"""
        # 生成结束消息
        if reason == "completed":
            message = "Thank you so much for sharing! I have all the information I need to create your profile. You'll see the results in your dashboard shortly. Have a wonderful day!"
        elif reason == "time_limit":
            message = "Thank you for your time! I have enough information to create your profile. You'll see the results in your profile dashboard shortly."
        else:
            message = "Thank you for the conversation! I'll analyze what we discussed and send you the results."

        # 标记会话完成
        session.current_stage = "completed"

        # 立即处理面试完成逻辑，不等待webhook
        try:
            logger.info(f"Processing interview completion for session: {session.call_sid}")

            # 计算通话时长
            call_duration = int(session.get_duration())

            # 创建语音分析对象
            voice_analysis = VoiceAnalysis(
                user_id=session.user_id,
                call_sid=session.call_sid,
                transcript=session.get_transcript_text(),
                call_duration=call_duration,
                quality_score=self._calculate_call_quality(session),
                analysis_data={
                    "collected_info": session.collected_info,
                    "stage_progression": [exchange.get("stage", "unknown") for exchange in session.conversation_transcript],
                    "completion_reason": "interview_completed"
                },
                questions_asked=session.questions_asked,
                responses_given=session.responses_received
            )

            # 进行AI分析
            analysis_result = self.analysis_engine.analyze_voice_transcript(
                session.user_id, voice_analysis
            )

            # 标记语音通话完成
            await self.user_manager.mark_voice_completed(
                session.user_id,
                {
                    "call_sid": session.call_sid,
                    "duration": voice_analysis.call_duration,
                    "quality": voice_analysis.quality_score,
                    "analysis_confidence": analysis_result.get_overall_confidence()
                }
            )

            # 保存完整的语音会话记录
            analysis_data = {
                "transcript": voice_analysis.transcript,
                "collected_info": session.collected_info,
                "stage_progression": [exchange["stage"] for exchange in session.conversation_transcript],
                "conversation_transcript": session.conversation_transcript,
                "quality_score": voice_analysis.quality_score,
                "questions_asked": session.questions_asked,
                "responses_received": session.responses_received,
                "call_status": "completed"
            }

            await self._complete_session_in_db(session, voice_analysis.call_duration, analysis_data)

            # 更新用户状态
            user = await self.user_manager.get_user(session.user_id)
            if user:
                user.complete_voice_call()
                await self.user_manager.update_user(user)
                logger.info(f"Updated user status to voice_call_completed for user: {session.user_id}")

            logger.info(f"Voice interview completed and analyzed for user: {session.user_id}")

        except Exception as e:
            logger.error(f"Failed to process interview completion: {e}")

        return self.twilio_client.create_twiml_response(
            self.twilio_client.generate_twiml_hangup(message)
        )

    def _calculate_call_quality(self, session: CallSession) -> float:
        """计算通话质量分数"""
        quality_factors = []

        # 1. 响应率 (40%)
        if session.questions_asked > 0:
            response_rate = session.responses_received / session.questions_asked
            quality_factors.append(response_rate * 0.4)

        # 2. 通话时长 (30%)
        duration = session.get_duration()
        ideal_duration = 300  # 5分钟
        if duration >= ideal_duration:
            duration_score = 1.0
        else:
            duration_score = duration / ideal_duration
        quality_factors.append(duration_score * 0.3)

        # 3. 信息收集完整性 (20%)
        info_completeness = len(session.collected_info) / 8  # 假设8个关键信息点
        quality_factors.append(min(1.0, info_completeness) * 0.2)

        # 4. 阶段完成度 (10%)
        completed_stages = len(set(exchange["stage"] for exchange in session.conversation_transcript))
        stage_completeness = completed_stages / 6  # 6个主要阶段
        quality_factors.append(min(1.0, stage_completeness) * 0.1)

        return sum(quality_factors)

    # ============ Session持久化方法 ============

    async def _save_session_to_db(self, session: CallSession) -> bool:
        """保存Session到数据库"""
        try:
            session_data = session.to_dict()
            success = await self.user_manager.db_manager.save_active_session(
                session.call_sid, session.user_id, session_data
            )
            if success:
                logger.info(f"Session saved to database: {session.call_sid}")
            else:
                logger.warning(f"Failed to save session to database: {session.call_sid}")
            return success
        except Exception as e:
            logger.error(f"Error saving session to database: {e}")
            return False

    async def _update_session_in_db(self, session: CallSession) -> bool:
        """更新数据库中的Session"""
        try:
            session_data = session.to_dict()
            success = await self.user_manager.db_manager.update_session_activity(
                session.call_sid, session_data
            )
            if success:
                logger.debug(f"Session updated in database: {session.call_sid}")
            return success
        except Exception as e:
            logger.error(f"Error updating session in database: {e}")
            return False

    async def _load_session_from_db(self, call_sid: str) -> Optional[CallSession]:
        """从数据库加载Session"""
        try:
            session_data = await self.user_manager.db_manager.load_active_session(call_sid)
            if session_data:
                session = CallSession.from_dict(session_data)
                logger.info(f"Session loaded from database: {call_sid}")
                return session
            return None
        except Exception as e:
            logger.error(f"Error loading session from database: {e}")
            return None

    async def _get_or_load_session(self, call_sid: str) -> Optional[CallSession]:
        """获取Session - 先从内存，再从数据库"""
        # 1. 先从内存缓存查找
        session = self._active_sessions.get(call_sid)
        if session:
            logger.debug(f"Session found in memory: {call_sid}")
            return session

        # 2. 从数据库加载
        logger.info(f"Session not in memory, loading from database: {call_sid}")
        session = await self._load_session_from_db(call_sid)
        if session:
            # 加载成功，添加到内存缓存
            self._active_sessions[call_sid] = session
            logger.info(f"Session restored from database: {call_sid}")
            return session

        logger.warning(f"Session not found in memory or database: {call_sid}")
        return None

    async def _complete_session_in_db(self, session: CallSession, call_duration: int, analysis_data: dict) -> bool:
        """标记Session在数据库中完成"""
        try:
            success = await self.user_manager.db_manager.complete_voice_session(
                session.call_sid, call_duration, analysis_data
            )
            if success:
                logger.info(f"Session marked as completed in database: {session.call_sid}")
            return success
        except Exception as e:
            logger.error(f"Error completing session in database: {e}")
            return False

    # ============ 管理和统计方法 ============

    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """获取活跃会话信息"""
        return {
            call_sid: {
                "user_id": session.user_id,
                "from_number": session.from_number,
                "duration": session.get_duration(),
                "current_stage": session.current_stage,
                "questions_asked": session.questions_asked,
                "responses_received": session.responses_received
            }
            for call_sid, session in self._active_sessions.items()
        }

    def get_session_details(self, call_sid: str) -> Optional[Dict[str, Any]]:
        """获取会话详细信息"""
        session = self._active_sessions.get(call_sid) or self._completed_sessions.get(call_sid)
        if not session:
            return None

        return {
            "call_sid": session.call_sid,
            "user_id": session.user_id,
            "from_number": session.from_number,
            "start_time": session.start_time.isoformat(),
            "duration": session.get_duration(),
            "current_stage": session.current_stage,
            "questions_asked": session.questions_asked,
            "responses_received": session.responses_received,
            "collected_info": session.collected_info,
            "conversation_transcript": session.conversation_transcript,
            "quality_score": self._calculate_call_quality(session)
        }

    def cleanup_old_sessions(self, hours: int = 24) -> int:
        """清理旧会话"""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        # 清理已完成的会话
        old_sessions = [
            call_sid for call_sid, session in self._completed_sessions.items()
            if session.start_time < cutoff_time
        ]

        for call_sid in old_sessions:
            del self._completed_sessions[call_sid]

        logger.info(f"Cleaned up {len(old_sessions)} old voice sessions")
        return len(old_sessions)

    def get_voice_service_stats(self) -> Dict[str, Any]:
        """获取语音服务统计信息"""
        # 计算平均通话时长
        all_sessions = list(self._active_sessions.values()) + list(self._completed_sessions.values())
        avg_duration = sum(session.get_duration() for session in all_sessions) / len(all_sessions) if all_sessions else 0

        # 计算平均质量分数
        avg_quality = sum(self._calculate_call_quality(session) for session in all_sessions) / len(all_sessions) if all_sessions else 0

        return {
            "active_sessions": len(self._active_sessions),
            "completed_sessions": len(self._completed_sessions),
            "total_sessions": len(all_sessions),
            "average_duration_seconds": round(avg_duration, 1),
            "average_quality_score": round(avg_quality, 3),
            "twilio_phone_number": self.twilio_client.phone_number,
            "max_call_duration": self.max_total_duration,
            "supported_languages": [self.twilio_client.voice_language]
        }

    # ============ Webhook处理方法 ============

    async def handle_twilio_webhook(self, webhook_data: Dict[str, Any]) -> str:
        """处理Twilio Webhook"""
        try:
            call_sid = webhook_data.get("CallSid")
            call_status = webhook_data.get("CallStatus")

            if call_status == "ringing":
                # 来电
                from_number = webhook_data.get("From", "").replace("+", "")
                caller_name = webhook_data.get("CallerName")
                return await self.handle_incoming_call(call_sid, from_number, caller_name)

            elif call_status in ["completed", "busy", "no-answer", "failed"]:
                # 通话结束
                call_duration = webhook_data.get("CallDuration", "0")
                await self.handle_call_completed(call_sid, call_status, call_duration)
                return ""

            else:
                # 语音输入
                speech_result = webhook_data.get("SpeechResult", "")
                confidence = float(webhook_data.get("Confidence", "1.0"))
                return await self.process_speech_input(call_sid, speech_result, confidence)

        except Exception as e:
            logger.error(f"Twilio webhook handling failed: {e}")
            return self.twilio_client.create_twiml_response(
                self.twilio_client.generate_twiml_hangup("Sorry, there was an error.")
            )

    # ============ API调用的缺失方法 ============

    async def initiate_call(self, phone_number: str, user_id: str) -> APIResponse:
        """发起语音通话 - API调用的方法"""
        try:
            # 检查用户是否存在
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            # 检查用户状态 - 使用与SMS服务相同的验证逻辑
            if not user.sms_verified:
                return APIResponse.error_response(
                    "SMS verification required before voice interview",
                    "SMS_NOT_VERIFIED"
                )

            # 检查是否已完成语音通话
            if user.voice_call_completed:
                return APIResponse.error_response(
                    "Voice interview already completed",
                    "VOICE_ALREADY_COMPLETED"
                )

            # 实际发起Twilio outgoing call
            outgoing_webhook_url = f"{self.twilio_client.webhook_url}/voice/webhook/outgoing"
            call_sid = self.twilio_client.initiate_outbound_call(phone_number, outgoing_webhook_url)

            if call_sid:
                call_data = {
                    "call_sid": call_sid,
                    "user_id": user_id,
                    "phone_number": phone_number,
                    "twilio_number": self.twilio_client.phone_number,
                    "call_type": "outgoing",
                    "status": "initiated",
                    "estimated_duration": "5-10 minutes"
                }
                return APIResponse.success_response(call_data)
            else:
                return APIResponse.error_response(
                    "Failed to initiate outbound call",
                    "OUTBOUND_CALL_FAILED"
                )

        except Exception as e:
            logger.error(f"Call initiation failed: {e}")
            return APIResponse.error_response(str(e), "CALL_INITIATION_FAILED")

    async def process_recording(self, call_sid: str, recording_url: str, recording_duration: str) -> APIResponse:
        """处理录音完成 - API调用的方法"""
        try:
            # 查找会话
            session = self._active_sessions.get(call_sid) or self._completed_sessions.get(call_sid)
            if not session:
                logger.warning(f"No session found for recording: {call_sid}")
                return APIResponse.error_response("Session not found", "SESSION_NOT_FOUND")

            # 更新会话录音信息
            session.recording_url = recording_url
            session.recording_duration = int(recording_duration) if recording_duration.isdigit() else 0

            # 如果会话还在进行中，标记录音已收到
            if call_sid in self._active_sessions:
                logger.info(f"Recording received for active call {call_sid}")
            else:
                # 会话已完成，可以进行最终分析
                logger.info(f"Recording received for completed call {call_sid}")
                # 这里可以触发更深入的录音分析

            return APIResponse.success_response({
                "call_sid": call_sid,
                "recording_processed": True,
                "recording_url": recording_url,
                "duration": recording_duration
            })

        except Exception as e:
            logger.error(f"Recording processing failed: {e}")
            return APIResponse.error_response(str(e), "RECORDING_PROCESSING_FAILED")

    async def handle_call_status_update(self, call_sid: str, call_status: str, call_duration: str) -> APIResponse:
        """处理通话状态更新 - API调用的方法"""
        try:
            logger.info(f"Call status update: {call_sid} -> {call_status} (duration: {call_duration})")

            # 查找会话 - 先从内存，再从数据库
            session = await self._get_or_load_session(call_sid)
            if not session:
                logger.warning(f"No session found for status update: {call_sid}")
                return APIResponse.success_response({
                    "call_sid": call_sid,
                    "status_updated": False,
                    "reason": "No session found"
                })

            # 根据状态进行处理
            if call_status in ["completed", "busy", "no-answer", "failed", "canceled"]:
                # 通话结束，调用现有的handle_call_completed方法
                logger.info(f"Processing call completion via status webhook: {call_sid} -> {call_status}")
                await self.handle_call_completed(call_sid, call_status, call_duration)
                
                return APIResponse.success_response({
                    "call_sid": call_sid,
                    "status_updated": True,
                    "final_status": call_status,
                    "duration": call_duration,
                    "session_completed": True
                })
            else:
                # 其他状态更新（如 in-progress, ringing 等）
                session.call_status = call_status
                
                return APIResponse.success_response({
                    "call_sid": call_sid,
                    "status_updated": True,
                    "current_status": call_status,
                    "session_active": True
                })

        except Exception as e:
            logger.error(f"Call status update failed: {e}")
            return APIResponse.error_response(str(e), "STATUS_UPDATE_FAILED")

    async def get_analysis_results(self, user_id: str) -> APIResponse:
        """获取语音分析结果 - API调用的方法"""
        try:
            # 检查用户是否存在
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            # 检查是否已完成语音通话
            if not user.voice_call_completed:
                return APIResponse.error_response(
                    "Voice interview not completed yet", 
                    "VOICE_NOT_COMPLETED"
                )

            # 查找用户的已完成会话
            user_session = None
            for session in self._completed_sessions.values():
                if session.user_id == user_id:
                    user_session = session
                    break

            if not user_session:
                # 从分析引擎获取结果（如果会话已被清理）
                try:
                    analysis_result = self.analysis_engine.get_voice_analysis(user_id)
                    if analysis_result:
                        return APIResponse.success_response({
                            "user_id": user_id,
                            "analysis_completed": True,
                            "analysis_data": analysis_result,
                            "source": "analysis_engine"
                        })
                except Exception as e:
                    logger.warning(f"Failed to get analysis from engine: {e}")

                return APIResponse.error_response(
                    "Voice analysis not found", 
                    "ANALYSIS_NOT_FOUND"
                )

            # 计算分析数据
            analysis_summary = self._generate_analysis_summary(user_session)
            analysis_data = {
                "call_sid": user_session.call_sid,
                "call_duration": user_session.get_duration(),
                "questions_asked": user_session.questions_asked,
                "responses_received": user_session.responses_received,
                "quality_score": self._calculate_call_quality(user_session),
                **analysis_summary  # 展开分析摘要
            }

            return APIResponse.success_response({
                "user_id": user_id,
                "analysis_completed": True,
                "analysis_data": analysis_data,
                "source": "voice_session"
            })

        except Exception as e:
            logger.error(f"Analysis results retrieval failed: {e}")
            return APIResponse.error_response(str(e), "ANALYSIS_RETRIEVAL_FAILED")

    async def reset_voice_status(self, user_id: str) -> APIResponse:
        """重置语音面试状态 - API调用的方法"""
        try:
            # 检查用户是否存在
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            # 检查用户SMS验证状态 - 使用与SMS服务相同的验证逻辑
            if not user.sms_verified:
                return APIResponse.error_response(
                    "SMS verification required",
                    "SMS_NOT_VERIFIED"
                )

            # 清理用户相关的会话
            sessions_to_remove = []
            
            # 查找并移除活跃会话
            for call_sid, session in self._active_sessions.items():
                if session.user_id == user_id:
                    sessions_to_remove.append(("active", call_sid))

            # 查找并移除已完成会话
            for call_sid, session in self._completed_sessions.items():
                if session.user_id == user_id:
                    sessions_to_remove.append(("completed", call_sid))

            # 执行清理
            removed_count = 0
            for session_type, call_sid in sessions_to_remove:
                if session_type == "active":
                    del self._active_sessions[call_sid]
                else:
                    del self._completed_sessions[call_sid]
                removed_count += 1

            # 重置用户语音状态
            user.reset_voice_call_status()
            self.user_manager.update_user(user)

            logger.info(f"Reset voice status for user {user_id}, removed {removed_count} sessions")

            return APIResponse.success_response({
                "user_id": user_id,
                "status_reset": True,
                "sessions_removed": removed_count,
                "can_retry": True,
                "message": "Voice interview status has been reset. You can now start a new interview."
            })

        except Exception as e:
            logger.error(f"Voice status reset failed: {e}")
            return APIResponse.error_response(str(e), "STATUS_RESET_FAILED")

    # ============ 辅助分析方法 ============

    def _generate_analysis_summary(self, session: CallSession) -> Dict[str, Any]:
        """生成分析摘要 - 整合transcript和personality分析"""
        try:
            # 基础统计
            total_exchanges = len(session.conversation_transcript)
            stages_covered = list(set(exchange["stage"] for exchange in session.conversation_transcript))
            response_rate = session.responses_received / session.questions_asked if session.questions_asked > 0 else 0
            
            # 个性洞察
            insights = {
                "communication_style": "conversational",
                "engagement_level": "high" if response_rate > 0.8 else "low" if response_rate < 0.5 else "medium",
                "personality_traits": {}
            }

            # 基于收集的信息分析个性特征
            if session.collected_info.get("organized_traits"):
                insights["personality_traits"]["organized"] = 0.8
            if session.collected_info.get("social_traits"):
                insights["personality_traits"]["social"] = 0.7
            if session.collected_info.get("relationship_values"):
                insights["personality_traits"]["values_driven"] = 0.6

            return {
                "total_exchanges": total_exchanges,
                "stages_covered": stages_covered,
                "response_rate": response_rate,
                "conversation_flow": [ex["stage"] for ex in session.conversation_transcript if ex["question"]],
                "personality_insights": insights,
                "collected_information": session.collected_info
            }

        except Exception as e:
            logger.error(f"Analysis summary generation failed: {e}")
            return {"error": "Failed to generate analysis summary"}

    def _is_repeat_request(self, user_response: str) -> bool:
        """检测用户是否请求重复问题"""
        if not user_response:
            return False
            
        user_response_lower = user_response.lower().strip()
        
        # 常见的重复请求短语
        repeat_phrases = [
            "what", "pardon", "sorry", "excuse me", "repeat", "again",
            "didn't hear", "didn't catch", "can you say", "say that again",
            "one more time", "come again", "huh", "eh", "什么", "再说一遍",
            "没听清", "重复", "再来一次"
        ]
        
        # 检查是否包含重复请求短语
        for phrase in repeat_phrases:
            if phrase in user_response_lower:
                return True
        
        # 检查是否只是简单的疑问词
        simple_questions = ["what?", "huh?", "sorry?", "pardon?", "eh?", "什么？"]
        if user_response_lower in simple_questions:
            return True
            
        return False
