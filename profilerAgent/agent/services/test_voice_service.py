"""
TestVoiceService - 语音面试测试服务
用命令行交互替代Twilio，测试语音面试逻辑
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
import json
import uuid
from enum import Enum

from ..config.service_config import ServiceConfig
from ..config.templates import Templates
from ..utils import (
    VoiceAnalysis, User, UserStatus, VerificationStatus, ConversationStage,
    APIResponse, EventType
)

logger = logging.getLogger(__name__)

class VoiceInterviewStage(Enum):
    """语音面试阶段"""
    GREETING = "greeting"
    PROFESSIONAL = "professional"
    PERSONALITY = "personality"
    INTERESTS = "interests"
    RELATIONSHIPS = "relationships"
    CLOSING = "closing"
    COMPLETED = "completed"

class TestCallSession:
    """测试通话会话数据"""
    def __init__(self, session_id: str, user_id: str, phone_number: str):
        self.session_id = session_id  # 替代 call_sid
        self.user_id = user_id
        self.phone_number = phone_number
        self.start_time = datetime.now()
        self.current_stage = VoiceInterviewStage.GREETING
        self.questions_asked = 0
        self.responses_received = 0
        self.conversation_transcript = []
        self.collected_info = {}
        self.stage_attempts = {}
        self.last_question = ""
        self.call_status: str = "test_initiated"

    def add_exchange(self, question: str, response: str = ""):
        """添加问答交换"""
        self.conversation_transcript.append({
            "timestamp": datetime.now().isoformat(),
            "question": question,
            "response": response,
            "stage": self.current_stage.value
        })

        if question:
            self.questions_asked += 1
            self.last_question = question

        if response:
            self.responses_received += 1

    def get_duration(self) -> int:
        """获取通话时长（秒）"""
        return int((datetime.now() - self.start_time).total_seconds())

    def get_transcript_text(self) -> str:
        """获取完整转录文本"""
        transcript = ""
        for exchange in self.conversation_transcript:
            if exchange["question"]:
                transcript += f"Assistant: {exchange['question']}\n"
            if exchange["response"]:
                transcript += f"User: {exchange['response']}\n"
        return transcript

class TestVoiceService:
    """测试语音服务 - 命令行版本"""

    def __init__(self, user_manager, analysis_engine):
        """初始化测试语音服务"""
        self.user_manager = user_manager
        self.analysis_engine = analysis_engine

        # 测试会话管理
        self._test_sessions: Dict[str, TestCallSession] = {}
        self._completed_sessions: Dict[str, TestCallSession] = {}

        # 面试配置
        self.min_responses_per_stage = 1
        self.max_questions_per_stage = 3
        self.max_total_duration = 600  # 10分钟

        logger.info("TestVoiceService initialized")

    async def start_test_interview(self, phone_number: str) -> Dict[str, Any]:
        """开始测试面试"""
        try:
            print(f"\n=== Starting Test Interview ===")
            print(f"Phone: {phone_number}")
            
            # 查找用户
            user = await self.user_manager.get_user_by_phone(phone_number)
            if not user:
                return {
                    "success": False,
                    "error": "User not found",
                    "message": "❌ No user found with this phone number"
                }

            print(f"✓ Found user: {user.first_name} {user.last_name} (ID: {user.user_id})")

            # 检查用户状态
            if not user.sms_verified:
                return {
                    "success": False,
                    "error": "SMS not verified",
                    "message": "❌ SMS verification required before voice interview"
                }

            print(f"✓ SMS verified: {user.sms_verified}")

            if user.voice_call_completed:
                return {
                    "success": False,
                    "error": "Voice already completed",
                    "message": "⚠️ Voice interview already completed"
                }

            print(f"✓ Voice status: Ready for interview")

            # 创建测试会话
            session_id = str(uuid.uuid4())[:8]
            session = TestCallSession(session_id, user.user_id, phone_number)
            self._test_sessions[session_id] = session

            # 生成欢迎消息和第一个问题
            greeting = Templates.get_voice_question("opening")
            session.add_exchange(greeting)

            print(f"\n=== Interview Started (Session: {session_id}) ===")
            
            return {
                "success": True,
                "session_id": session_id,
                "user_id": user.user_id,
                "first_question": greeting,
                "message": "✓ Test interview session created"
            }

        except Exception as e:
            logger.error(f"Test interview start failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"❌ Failed to start interview: {e}"
            }

    async def process_text_input(self, session_id: str, text_input: str) -> Dict[str, Any]:
        """处理文字输入（替代语音输入）"""
        try:
            # 获取测试会话
            session = self._test_sessions.get(session_id)
            if not session:
                return {
                    "success": False,
                    "error": "Session not found",
                    "message": "❌ Test session not found or expired"
                }

            # 检查会话时长
            if session.get_duration() > self.max_total_duration:
                return await self._end_test_interview(session, "time_limit")

            # 记录用户响应
            session.add_exchange("", text_input)
            print(f"✓ Recorded response: {text_input[:50]}...")

            # 处理响应并生成下一个问题
            next_question = await self._process_user_response(session, text_input)

            if next_question is None:
                # 面试完成
                return await self._end_test_interview(session, "completed")

            # 返回下一个问题
            return {
                "success": True,
                "session_id": session_id,
                "current_stage": session.current_stage.value,
                "next_question": next_question,
                "questions_asked": session.questions_asked,
                "responses_received": session.responses_received,
                "duration": session.get_duration()
            }

        except Exception as e:
            logger.error(f"Text input processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"❌ Failed to process input: {e}"
            }

    async def _process_user_response(self, session: TestCallSession, response: str) -> Optional[str]:
        """处理用户响应并生成下一个问题"""
        # 提取信息
        self._extract_information(session, response)

        # 检查是否需要进入下一阶段
        if self._should_advance_stage(session):
            old_stage = session.current_stage.value
            session.current_stage = self._get_next_stage(session.current_stage)
            new_stage = session.current_stage.value
            
            if old_stage != new_stage:
                print(f"[{old_stage.upper()} → {new_stage.upper()}]")

            # 检查是否完成所有阶段
            if session.current_stage == VoiceInterviewStage.COMPLETED:
                return None

        # 生成下一个问题
        next_question = self._generate_next_question(session)
        session.add_exchange(next_question)

        return next_question

    def _extract_information(self, session: TestCallSession, response: str) -> None:
        """从用户响应中提取信息"""
        response_lower = response.lower()

        # 根据当前阶段提取相关信息
        if session.current_stage == VoiceInterviewStage.GREETING:
            # 提取姓名和位置
            if "name" in response_lower or "i'm" in response_lower or "my name" in response_lower:
                session.collected_info["name_mentioned"] = True
            if any(city in response_lower for city in ["san francisco", "new york", "los angeles", "chicago", "seattle", "boston", "austin", "denver"]):
                session.collected_info["location_mentioned"] = True

        elif session.current_stage == VoiceInterviewStage.PROFESSIONAL:
            # 提取职业信息
            if any(word in response_lower for word in ["engineer", "manager", "developer", "designer", "analyst", "consultant", "director"]):
                session.collected_info["job_title_mentioned"] = True
            if any(word in response_lower for word in ["company", "work", "job", "career", "startup", "tech"]):
                session.collected_info["work_context_mentioned"] = True

        elif session.current_stage == VoiceInterviewStage.PERSONALITY:
            # 提取性格特征
            if any(word in response_lower for word in ["organized", "plan", "structure", "schedule", "detail"]):
                session.collected_info["organized_traits"] = True
            if any(word in response_lower for word in ["people", "social", "friends", "team", "group"]):
                session.collected_info["social_traits"] = True

        elif session.current_stage == VoiceInterviewStage.INTERESTS:
            # 提取兴趣爱好
            if any(word in response_lower for word in ["music", "travel", "sports", "reading", "cooking", "hiking", "art"]):
                session.collected_info["interests_mentioned"] = True

        elif session.current_stage == VoiceInterviewStage.RELATIONSHIPS:
            # 提取关系偏好
            if any(word in response_lower for word in ["honest", "trust", "communication", "respect", "loyal"]):
                session.collected_info["relationship_values"] = True

    def _should_advance_stage(self, session: TestCallSession) -> bool:
        """判断是否应该进入下一阶段"""
        current_stage = session.current_stage

        # 获取当前阶段的问题数量
        stage_questions = sum(1 for exchange in session.conversation_transcript
                            if exchange["stage"] == current_stage.value and exchange["question"])

        # 获取当前阶段的回答数量
        stage_responses = sum(1 for exchange in session.conversation_transcript
                            if exchange["stage"] == current_stage.value and exchange["response"])

        # 基本条件：至少一个回答
        if stage_responses < self.min_responses_per_stage:
            return False

        # 如果已经问了最大问题数，强制进入下一阶段
        if stage_questions >= self.max_questions_per_stage:
            return True

        # 简化的阶段推进条件 - 每个阶段只需要1个回答
        if current_stage == VoiceInterviewStage.GREETING:
            return stage_responses >= 1

        elif current_stage == VoiceInterviewStage.PROFESSIONAL:
            return stage_responses >= 1

        elif current_stage == VoiceInterviewStage.PERSONALITY:
            return stage_responses >= 1

        elif current_stage == VoiceInterviewStage.INTERESTS:
            return stage_responses >= 1

        elif current_stage == VoiceInterviewStage.RELATIONSHIPS:
            return stage_responses >= 1

        elif current_stage == VoiceInterviewStage.CLOSING:
            return stage_responses >= 1  # CLOSING阶段收到回答后就结束

        return False

    def _get_next_stage(self, current_stage: VoiceInterviewStage) -> VoiceInterviewStage:
        """获取下一个面试阶段"""
        stage_order = [
            VoiceInterviewStage.GREETING,
            VoiceInterviewStage.PROFESSIONAL,
            VoiceInterviewStage.PERSONALITY,
            VoiceInterviewStage.INTERESTS,
            VoiceInterviewStage.RELATIONSHIPS,
            VoiceInterviewStage.CLOSING,
            VoiceInterviewStage.COMPLETED
        ]

        try:
            current_index = stage_order.index(current_stage)
            if current_index < len(stage_order) - 1:
                return stage_order[current_index + 1]
            else:
                return VoiceInterviewStage.COMPLETED
        except ValueError:
            return VoiceInterviewStage.COMPLETED

    def _generate_next_question(self, session: TestCallSession) -> str:
        """生成下一个问题"""
        current_stage = session.current_stage

        # 获取已使用的问题
        used_questions = [exchange["question"] for exchange in session.conversation_transcript
                         if exchange["question"]]

        # 根据阶段获取问题
        if current_stage == VoiceInterviewStage.PROFESSIONAL:
            return Templates.get_voice_question("professional", used_questions)

        elif current_stage == VoiceInterviewStage.PERSONALITY:
            return Templates.get_voice_question("personality", used_questions)

        elif current_stage == VoiceInterviewStage.INTERESTS:
            return Templates.get_voice_question("interests", used_questions)

        elif current_stage == VoiceInterviewStage.RELATIONSHIPS:
            return Templates.get_voice_question("relationships", used_questions)

        elif current_stage == VoiceInterviewStage.CLOSING:
            return Templates.get_voice_question("closing", used_questions)

        else:
            return "Could you tell me more about that?"

    async def _end_test_interview(self, session: TestCallSession, reason: str) -> Dict[str, Any]:
        """结束测试面试并进行分析"""
        try:
            print(f"\n=== Interview Ending ===")
            print(f"Reason: {reason}")
            print(f"Duration: {session.get_duration()} seconds")
            print(f"Questions asked: {session.questions_asked}")
            print(f"Responses received: {session.responses_received}")

            # 移动到已完成会话
            self._completed_sessions[session.session_id] = session
            del self._test_sessions[session.session_id]

            # 标记会话完成
            session.current_stage = VoiceInterviewStage.COMPLETED

            # 创建语音分析数据
            voice_analysis = VoiceAnalysis(
                user_id=session.user_id,
                call_sid=session.session_id,
                call_duration=session.get_duration(),
                transcript=session.get_transcript_text(),
                analysis_data={
                    "collected_info": session.collected_info,
                    "stage_progression": [exchange["stage"] for exchange in session.conversation_transcript],
                    "call_status": reason
                },
                quality_score=self._calculate_call_quality(session),
                questions_asked=session.questions_asked,
                responses_given=session.responses_received
            )

            print(f"\n=== Starting AI Analysis ===")
            print(f"Transcript length: {len(voice_analysis.transcript)} characters")
            print(f"Quality score: {voice_analysis.quality_score:.2f}")

            # 触发分析（如果有足够回答）
            if session.responses_received >= 3:
                print("✓ Sufficient responses for analysis")
                print("✓ Calling DeepSeek API...")
                
                # 使用异步方式调用分析引擎
                try:
                    # 直接调用DeepSeek的异步方法，避免asyncio.run()冲突
                    analysis_result = await self._analyze_voice_async(session.user_id, voice_analysis)
                except Exception as e:
                    print(f"⚠️ Analysis failed: {e}")
                    # 创建一个基本的分析结果
                    from ..utils.data_models import AnalysisResult
                    analysis_result = AnalysisResult(
                        user_id=session.user_id,
                        mbti_type="UNKNOWN",
                        personality_traits=[],
                        professional_info={},
                        interests=[],
                        raw_analysis={"error": str(e)}
                    )

                print(f"✓ Analysis completed!")
                
                # 1. 保存语音会话记录到 voice_sessions 表
                print("✓ Saving voice session to database...")
                session_saved = await self.user_manager.db_manager.save_voice_session(
                    user_id=session.user_id,
                    call_sid=session.session_id,
                    call_duration=voice_analysis.call_duration,
                    analysis_data={
                        "transcript": voice_analysis.transcript,
                        "collected_info": session.collected_info,
                        "stage_progression": [exchange["stage"] for exchange in session.conversation_transcript],
                        "conversation_transcript": session.conversation_transcript,
                        "quality_score": voice_analysis.quality_score,
                        "questions_asked": session.questions_asked,
                        "responses_received": session.responses_received
                    }
                )
                if session_saved:
                    print("  ✓ Voice session saved to voice_sessions table")
                else:
                    print("  ⚠️ Failed to save voice session")
                
                # 2. 保存AI分析结果到 user_profiles 表
                print("✓ Saving user profile analysis...")
                profile_data = {
                    "mbti_analysis": {
                        "type": analysis_result.mbti_type,
                        "dimensions": getattr(analysis_result, 'mbti_dimensions', {}),
                        "confidence": analysis_result.get_overall_confidence()
                    },
                    "personality_traits": analysis_result.personality_traits,
                    "professional_info": analysis_result.professional_info,
                    "interests": analysis_result.interests,
                    "analysis_timestamp": datetime.now().isoformat(),
                    "source": "voice_interview"
                }
                
                profile_saved = await self.user_manager.db_manager.save_user_profile(
                    user_id=session.user_id,
                    voice_analysis=analysis_result.raw_analysis,
                    final_profile=profile_data,
                    confidence_scores=getattr(analysis_result, 'confidence_scores', {}),
                    mbti_type=analysis_result.mbti_type,
                    verification_level="medium"
                )
                if profile_saved:
                    print("  ✓ User profile saved to user_profiles table")
                else:
                    print("  ⚠️ Failed to save user profile")
                
                # 3. 生成并保存画像卡片到 profile_cards 表
                print("✓ Generating profile cards...")
                cards_created = await self._create_profile_cards(session.user_id, analysis_result, session)
                print(f"  ✓ Created {cards_created} profile cards")
                
                # 4. 标记语音通话完成
                try:
                    await self.user_manager.mark_voice_completed(
                        session.user_id,
                        {
                            "call_sid": session.session_id,
                            "duration": voice_analysis.call_duration,
                            "quality": voice_analysis.quality_score,
                            "analysis_confidence": analysis_result.get_overall_confidence()
                        }
                    )
                    print("✓ Voice completion marked successfully")
                except Exception as e:
                    print(f"⚠️ Failed to mark voice completion: {e}")

                # 5. 更新用户状态
                user = await self.user_manager.get_user(session.user_id)
                if user:
                    user.complete_voice_call()
                    # 使用异步更新方法
                    await self.user_manager._save_user_to_db(user)
                    print(f"✓ User status updated: voice_call_completed = True")

                return {
                    "success": True,
                    "completed": True,
                    "session_id": session.session_id,
                    "analysis_result": {
                        "mbti_type": getattr(analysis_result, 'mbti_type', 'Unknown'),
                        "confidence": analysis_result.get_overall_confidence() if hasattr(analysis_result, 'get_overall_confidence') else 0.0,
                        "personality_traits": getattr(analysis_result, 'personality_traits', []),
                        "professional_info": getattr(analysis_result, 'professional_info', {})
                    },
                    "transcript": voice_analysis.transcript,
                    "message": "✓ Interview completed and analyzed successfully!"
                }
            else:
                print(f"⚠️ Insufficient responses ({session.responses_received}) for analysis")
                return {
                    "success": True,
                    "completed": True,
                    "session_id": session.session_id,
                    "analysis_result": None,
                    "message": "⚠️ Interview completed but insufficient data for analysis"
                }

        except Exception as e:
            logger.error(f"Test interview completion failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"❌ Failed to complete interview: {e}"
            }

    def _calculate_call_quality(self, session: TestCallSession) -> float:
        """计算通话质量分数"""
        quality_factors = []

        # 1. 响应率 (40%)
        if session.questions_asked > 0:
            response_rate = session.responses_received / session.questions_asked
            quality_factors.append(response_rate * 0.4)

        # 2. 通话时长 (30%)
        duration = session.get_duration()
        ideal_duration = 300  # 5分钟
        if duration >= ideal_duration:
            duration_score = 1.0
        else:
            duration_score = duration / ideal_duration
        quality_factors.append(duration_score * 0.3)

        # 3. 信息收集完整性 (20%)
        info_completeness = len(session.collected_info) / 8  # 假设8个关键信息点
        quality_factors.append(min(1.0, info_completeness) * 0.2)

        # 4. 阶段完成度 (10%)
        completed_stages = len(set(exchange["stage"] for exchange in session.conversation_transcript))
        stage_completeness = completed_stages / 6  # 6个主要阶段
        quality_factors.append(min(1.0, stage_completeness) * 0.1)

        return sum(quality_factors)

    def get_test_session_status(self, session_id: str) -> Dict[str, Any]:
        """获取测试会话状态"""
        session = self._test_sessions.get(session_id) or self._completed_sessions.get(session_id)
        if not session:
            return {"error": "Session not found"}

        return {
            "session_id": session.session_id,
            "user_id": session.user_id,
            "phone_number": session.phone_number,
            "current_stage": session.current_stage.value,
            "duration": session.get_duration(),
            "questions_asked": session.questions_asked,
            "responses_received": session.responses_received,
            "collected_info": session.collected_info,
            "is_active": session_id in self._test_sessions
        }

    def get_session_transcript(self, session_id: str) -> str:
        """获取会话转录"""
        session = self._test_sessions.get(session_id) or self._completed_sessions.get(session_id)
        if not session:
            return "Session not found"
        
        return session.get_transcript_text()

    async def _analyze_voice_async(self, user_id: str, voice_data) -> Any:
        """异步语音分析方法，避免asyncio.run()冲突"""
        try:
            # 构建语音分析提示
            from ..config.templates import Templates
            system_prompt = Templates.AI_PROMPTS["voice_analysis"]["system_prompt"]
            
            # 准备语音转录文本
            transcript_text = voice_data.transcript
            
            user_prompt = Templates.AI_PROMPTS["voice_analysis"]["analysis_prompt"].format(
                conversation_text=transcript_text
            )
            
            # 直接调用DeepSeek的异步方法
            analysis_response = await self.analysis_engine.deepseek_client.analyze_async(
                system_prompt, user_prompt, "json"
            )
            
            if "error" in analysis_response:
                raise Exception(f"DeepSeek analysis failed: {analysis_response['error']}")
            
            # 构建分析结果
            from ..utils.data_models import AnalysisResult
            result = AnalysisResult(
                user_id=user_id,
                analysis_type="voice",
                mbti_type=analysis_response.get("mbti_type"),
                mbti_dimensions=analysis_response.get("mbti_dimensions", {}),
                personality_traits=analysis_response.get("personality_traits", {}),
                interests=analysis_response.get("interests", []),
                professional_info=analysis_response.get("professional_info", {}),
                confidence_scores=analysis_response.get("confidence_scores", {}),
                raw_analysis=analysis_response
            )
            
            # 添加语音特定信息
            result.raw_analysis["voice_quality"] = voice_data.quality_score
            result.raw_analysis["call_duration"] = voice_data.call_duration
            result.raw_analysis["engagement_score"] = voice_data.calculate_engagement_score()
            
            # 缓存结果
            self.analysis_engine._analysis_cache[user_id] = result
            
            logger.info(f"Voice analysis completed for user: {user_id}")
            return result
            
        except Exception as e:
            logger.error(f"Voice analysis failed for user {user_id}: {e}")
            # 创建失败时的备用分析结果
            from ..utils.data_models import AnalysisResult
            return AnalysisResult(
                user_id=user_id,
                analysis_type="voice",
                mbti_type="UNKNOWN",
                mbti_dimensions={},
                personality_traits={},
                interests=[],
                professional_info={},
                confidence_scores={"error": 0.0},
                raw_analysis={"error": str(e), "fallback": True}
            )

    async def _create_profile_cards(self, user_id: str, analysis_result, session: TestCallSession) -> int:
        """生成并保存用户画像卡片"""
        cards_created = 0
        
        try:
            # 从对话中提取证据
            evidence_quotes = []
            for exchange in session.conversation_transcript:
                if exchange.get("response") and len(exchange["response"]) > 10:
                    evidence_quotes.append(f"User: {exchange['response']}")
            
            # 1. 性格特征卡片 (personality_mbti)
            if analysis_result.personality_traits:
                personality_traits = []
                if isinstance(analysis_result.personality_traits, dict):
                    personality_traits = list(analysis_result.personality_traits.keys())
                elif isinstance(analysis_result.personality_traits, list):
                    personality_traits = analysis_result.personality_traits
                
                if personality_traits:
                    card_id = await self.user_manager.db_manager.save_profile_card(
                        user_id=user_id,
                        card_type="personality_mbti",
                        title=f"MBTI: {analysis_result.mbti_type}",
                        description=f"Key personality traits: {', '.join(personality_traits[:5])}",
                        confidence=analysis_result.get_overall_confidence(),
                        tags=personality_traits,
                        evidence=evidence_quotes[:3]
                    )
                    if card_id:
                        cards_created += 1
            
            # 2. 兴趣爱好卡片 (interests)
            if analysis_result.interests:
                interests_list = analysis_result.interests
                if isinstance(interests_list, dict):
                    interests_list = list(interests_list.keys())
                
                if interests_list:
                    card_id = await self.user_manager.db_manager.save_profile_card(
                        user_id=user_id,
                        card_type="interests",
                        title="Interests & Hobbies",
                        description=f"Main interests: {', '.join(interests_list[:3])}",
                        confidence=0.8,
                        tags=interests_list,
                        evidence=[q for q in evidence_quotes if any(interest.lower() in q.lower() for interest in interests_list)][:2]
                    )
                    if card_id:
                        cards_created += 1
            
            # 3. 职业信息卡片 (lifestyle)
            if analysis_result.professional_info:
                prof_info = analysis_result.professional_info
                title = "Professional Background"
                description = ""
                tags = []
                
                if isinstance(prof_info, dict):
                    if "job_title" in prof_info:
                        title = f"Job: {prof_info['job_title']}"
                        tags.append(prof_info['job_title'])
                    if "industry" in prof_info:
                        description = f"Industry: {prof_info['industry']}"
                        tags.append(prof_info['industry'])
                    if "skills" in prof_info:
                        skills = prof_info['skills'] if isinstance(prof_info['skills'], list) else [prof_info['skills']]
                        description += f", Skills: {', '.join(skills[:3])}"
                        tags.extend(skills)
                
                if tags:
                    card_id = await self.user_manager.db_manager.save_profile_card(
                        user_id=user_id,
                        card_type="lifestyle",
                        title=title,
                        description=description,
                        confidence=0.75,
                        tags=tags,
                        evidence=[q for q in evidence_quotes if any(tag.lower() in q.lower() for tag in tags)][:2]
                    )
                    if card_id:
                        cards_created += 1
            
            # 4. 社交特征卡片 (social_style)
            mbti_type = analysis_result.mbti_type
            if mbti_type:
                social_traits = []
                social_desc = ""
                
                # 基于MBTI推断社交特征
                if mbti_type.startswith('E'):
                    social_traits = ["Extroverted", "Social", "Outgoing"]
                    social_desc = "Enjoys social interactions and group activities"
                elif mbti_type.startswith('I'):
                    social_traits = ["Introverted", "Reflective", "Deep thinker"]
                    social_desc = "Prefers meaningful one-on-one conversations"
                
                if social_traits:
                    card_id = await self.user_manager.db_manager.save_profile_card(
                        user_id=user_id,
                        card_type="social_style",
                        title="Social Style",
                        description=social_desc,
                        confidence=0.7,
                        tags=social_traits,
                        evidence=evidence_quotes[:2]
                    )
                    if card_id:
                        cards_created += 1
            
            # 5. 恋爱观卡片 (relationship_goals)
            relationship_evidence = []
            for exchange in session.conversation_transcript:
                if exchange.get("stage") == "relationships" and exchange.get("response"):
                    relationship_evidence.append(f"User: {exchange['response']}")
            
            if relationship_evidence:
                card_id = await self.user_manager.db_manager.save_profile_card(
                    user_id=user_id,
                    card_type="relationship_goals",
                    title="Relationship Values",
                    description="Values and expectations in relationships",
                    confidence=0.8,
                    tags=["relationships", "values", "dating"],
                    evidence=relationship_evidence[:2]
                )
                if card_id:
                    cards_created += 1
            
            return cards_created
            
        except Exception as e:
            logger.error(f"Failed to create profile cards: {e}")
            return cards_created 