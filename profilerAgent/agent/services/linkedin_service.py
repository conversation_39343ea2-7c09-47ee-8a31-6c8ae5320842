"""
LinkedInService - LinkedIn数据抓取和验证
使用Bright Data进行LinkedIn个人资料抓取和验证
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
import json
import re
import random
from urllib.parse import urlparse

from ..config.service_config import ServiceConfig
from ..config.templates import Templates
from ..utils import (
    LinkedInProfile, VerificationResult, User, UserStatus, VerificationStatus,
    APIResponse, EventType
)

logger = logging.getLogger(__name__)

class BrightDataClient:
    """Bright Data API客户端"""

    def __init__(self, config: Dict[str, Any]):
        self.api_key = config["api_key"]
        self.zone = config["zone"]
        self.endpoint = config["endpoint"]
        self.timeout = config["timeout"]
        self.max_retries = config["max_retries"]

        # 抓取配置
        self.rate_limit_delay = config["rate_limit_delay"]
        self.user_agent = config["user_agent"]

        logger.info(f"Bright Data client initialized: {self.zone}")

    def scrape_linkedin_profile(self, linkedin_url: str) -> Dict[str, Any]:
        """抓取LinkedIn个人资料"""
        try:
            # 验证LinkedIn URL格式
            if not self._is_valid_linkedin_url(linkedin_url):
                raise ValueError(f"Invalid LinkedIn URL: {linkedin_url}")

            # 构建抓取请求
            scrape_request = {
                "url": linkedin_url,
                "format": "json",
                "country": "US",
                "session_id": f"linkedin_{datetime.now().timestamp()}",
                "render": True,
                "wait": 3000  # 等待页面加载
            }

            # MVP版本：模拟Bright Data API调用
            profile_data = self._mock_bright_data_scrape(linkedin_url)

            logger.info(f"LinkedIn profile scraped: {linkedin_url}")
            return profile_data

        except Exception as e:
            logger.error(f"LinkedIn scraping failed for {linkedin_url}: {e}")
            raise

    def _is_valid_linkedin_url(self, url: str) -> bool:
        """验证LinkedIn URL格式"""
        linkedin_pattern = r'^https?://(www\.)?linkedin\.com/in/[\w\-]+/?$'
        return bool(re.match(linkedin_pattern, url, re.IGNORECASE))

    def _mock_bright_data_scrape(self, linkedin_url: str) -> Dict[str, Any]:
        """模拟Bright Data抓取结果 - MVP测试用"""
        # 从URL提取用户名
        username = linkedin_url.split('/in/')[-1].rstrip('/')

        # 生成模拟的LinkedIn数据
        mock_profiles = {
            "sarah-smith": {
                "basic_info": {
                    "name": "Sarah Smith",
                    "headline": "Senior Product Manager at TechCorp",
                    "location": "San Francisco, California",
                    "industry": "Technology",
                    "summary": "Experienced product manager with 8+ years in tech, passionate about building user-centric products."
                },
                "experience": [
                    {
                        "title": "Senior Product Manager",
                        "company": "TechCorp",
                        "duration": "2021 - Present",
                        "location": "San Francisco, CA",
                        "description": "Leading product strategy for mobile applications with 2M+ users."
                    },
                    {
                        "title": "Product Manager",
                        "company": "StartupXYZ",
                        "duration": "2019 - 2021",
                        "location": "San Francisco, CA",
                        "description": "Managed product roadmap and cross-functional teams."
                    }
                ],
                "education": [
                    {
                        "school": "Stanford University",
                        "degree": "MBA",
                        "field": "Business Administration",
                        "years": "2017 - 2019"
                    },
                    {
                        "school": "UC Berkeley",
                        "degree": "Bachelor's",
                        "field": "Computer Science",
                        "years": "2013 - 2017"
                    }
                ],
                "skills": ["Product Management", "Strategy", "Leadership", "Analytics", "User Experience"],
                "connections": "500+",
                "profile_views": "1,234 profile views in the past 90 days"
            }
        }

        # 返回匹配的模拟数据或生成通用数据
        if username in mock_profiles:
            return mock_profiles[username]
        else:
            return self._generate_generic_profile(username)

    def _generate_generic_profile(self, username: str) -> Dict[str, Any]:
        """生成通用的模拟LinkedIn数据"""
        names = ["Alex Johnson", "Jordan Lee", "Taylor Brown", "Casey Wilson", "Morgan Davis"]
        titles = ["Software Engineer", "Data Scientist", "Marketing Manager", "Sales Director", "Designer"]
        companies = ["Google", "Microsoft", "Apple", "Meta", "Amazon", "Netflix", "Uber"]
        locations = ["San Francisco, CA", "New York, NY", "Seattle, WA", "Austin, TX", "Boston, MA"]

        return {
            "basic_info": {
                "name": random.choice(names),
                "headline": f"{random.choice(titles)} at {random.choice(companies)}",
                "location": random.choice(locations),
                "industry": "Technology",
                "summary": "Passionate professional with expertise in technology and innovation."
            },
            "experience": [
                {
                    "title": random.choice(titles),
                    "company": random.choice(companies),
                    "duration": "2020 - Present",
                    "location": random.choice(locations),
                    "description": "Leading innovative projects and driving business growth."
                }
            ],
            "education": [
                {
                    "school": "University of California",
                    "degree": "Bachelor's",
                    "field": "Computer Science",
                    "years": "2016 - 2020"
                }
            ],
            "skills": ["Leadership", "Technology", "Innovation", "Strategy"],
            "connections": "500+",
            "profile_views": "500+ profile views in the past 90 days"
        }

class LinkedInService:
    """LinkedIn服务 - MVP版本"""

    def __init__(self, user_manager, analysis_engine):
        """初始化LinkedIn服务"""
        self.user_manager = user_manager
        self.analysis_engine = analysis_engine

        # 初始化Bright Data客户端
        bright_data_config = ServiceConfig.get_bright_data_config()
        self.bright_data_client = BrightDataClient(bright_data_config)

        # 验证配置
        self.consistency_threshold = ServiceConfig.LINKEDIN_CONSISTENCY_THRESHOLD
        self.verification_weights = {
            "basic_info": 0.3,
            "professional": 0.4,
            "education": 0.2,
            "skills": 0.1
        }

        # 缓存LinkedIn数据
        self._profile_cache: Dict[str, LinkedInProfile] = {}
        self._verification_cache: Dict[str, VerificationResult] = {}

        logger.info("LinkedInService initialized")

    def process_linkedin_verification(self, user_id: str, linkedin_url: str) -> APIResponse:
        """处理LinkedIn验证流程"""
        try:
            # 1. 抓取LinkedIn数据
            linkedin_data = self.scrape_linkedin_profile(linkedin_url)

            # 2. 创建LinkedIn个人资料对象
            linkedin_profile = self._create_linkedin_profile(user_id, linkedin_url, linkedin_data)

            # 3. 验证个人资料一致性
            verification_result = self.verify_profile_consistency(user_id, linkedin_profile)

            # 4. 缓存结果
            self._profile_cache[user_id] = linkedin_profile
            self._verification_cache[user_id] = verification_result

            # 5. 更新用户状态
            if verification_result.is_verified:
                self.user_manager.mark_linkedin_verified(
                    user_id,
                    {
                        "url": linkedin_url,
                        "verification_score": verification_result.overall_score,
                        "verified_at": datetime.now().isoformat()
                    }
                )

            return APIResponse.success_response({
                "user_id": user_id,
                "linkedin_url": linkedin_url,
                "verification_result": verification_result.to_dict(),
                "profile_data": linkedin_profile.to_dict()
            })

        except Exception as e:
            logger.error(f"LinkedIn verification failed for user {user_id}: {e}")
            return APIResponse.error_response(str(e), "LINKEDIN_VERIFICATION_ERROR")

    def scrape_linkedin_profile(self, linkedin_url: str) -> Dict[str, Any]:
        """抓取LinkedIn个人资料"""
        return self.bright_data_client.scrape_linkedin_profile(linkedin_url)

    def verify_profile_consistency(self, user_id: str, linkedin_profile: LinkedInProfile) -> VerificationResult:
        """验证个人资料一致性"""
        try:
            # 获取用户的语音分析结果
            voice_analysis = self.analysis_engine.get_analysis_result(user_id)
            if not voice_analysis:
                logger.warning(f"No voice analysis found for user {user_id}")
                return self._create_fallback_verification_result(user_id, "no_voice_analysis")

            # 计算各维度的一致性分数
            consistency_scores = {}

            # 1. 基本信息一致性
            consistency_scores["basic_info"] = self._verify_basic_info_consistency(
                linkedin_profile, voice_analysis
            )

            # 2. 职业信息一致性
            consistency_scores["professional"] = self._verify_professional_consistency(
                linkedin_profile, voice_analysis
            )

            # 3. 教育信息一致性
            consistency_scores["education"] = self._verify_education_consistency(
                linkedin_profile, voice_analysis
            )

            # 4. 技能一致性
            consistency_scores["skills"] = self._verify_skills_consistency(
                linkedin_profile, voice_analysis
            )

            # 计算总体验证分数
            overall_score = sum(
                consistency_scores[dimension] * self.verification_weights[dimension]
                for dimension in consistency_scores.keys()
            )

            # 判断是否通过验证
            is_verified = overall_score >= self.consistency_threshold

            # 生成验证结果
            verification_result = VerificationResult(
                user_id=user_id,
                linkedin_url=linkedin_profile.profile_url,
                overall_score=overall_score,
                is_verified=is_verified,
                consistency_breakdown=consistency_scores,
                verification_details=self._generate_verification_details(consistency_scores),
                verified_at=datetime.now()
            )

            logger.info(f"LinkedIn verification completed for user {user_id}: {overall_score:.3f}")
            return verification_result

        except Exception as e:
            logger.error(f"Profile consistency verification failed for user {user_id}: {e}")
            return self._create_fallback_verification_result(user_id, str(e))

    def extract_professional_info(self, linkedin_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取职业信息"""
        professional_info = {}

        # 基本职业信息
        basic_info = linkedin_data.get("basic_info", {})
        professional_info["current_title"] = basic_info.get("headline", "").split(" at ")[0]
        professional_info["current_company"] = basic_info.get("headline", "").split(" at ")[-1] if " at " in basic_info.get("headline", "") else ""
        professional_info["industry"] = basic_info.get("industry", "")
        professional_info["location"] = basic_info.get("location", "")

        # 工作经历
        experience = linkedin_data.get("experience", [])
        if experience:
            current_job = experience[0]  # 假设第一个是当前工作
            professional_info["current_role"] = current_job.get("title", "")
            professional_info["current_employer"] = current_job.get("company", "")
            professional_info["work_location"] = current_job.get("location", "")

            # 计算工作经验年数
            total_experience = self._calculate_total_experience(experience)
            professional_info["years_of_experience"] = total_experience

        # 技能
        skills = linkedin_data.get("skills", [])
        professional_info["skills"] = skills[:10]  # 取前10个技能

        return professional_info

    def extract_education_info(self, linkedin_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取教育信息"""
        education_info = {}

        education = linkedin_data.get("education", [])
        if education:
            # 最高学历
            highest_education = education[0]  # 假设按时间倒序排列
            education_info["highest_degree"] = highest_education.get("degree", "")
            education_info["field_of_study"] = highest_education.get("field", "")
            education_info["school"] = highest_education.get("school", "")
            education_info["graduation_year"] = self._extract_graduation_year(highest_education.get("years", ""))

            # 所有学历
            education_info["all_degrees"] = [
                {
                    "degree": edu.get("degree", ""),
                    "field": edu.get("field", ""),
                    "school": edu.get("school", ""),
                    "years": edu.get("years", "")
                }
                for edu in education
            ]

        return education_info

    def calculate_consistency_score(self, voice_info: Dict[str, Any], linkedin_info: Dict[str, Any]) -> float:
        """计算一致性分数"""
        consistency_factors = []

        # 职位一致性
        voice_job = voice_info.get("professional_info", {}).get("career_focus", "").lower()
        linkedin_job = linkedin_info.get("current_title", "").lower()

        if voice_job and linkedin_job:
            job_similarity = self._calculate_text_similarity(voice_job, linkedin_job)
            consistency_factors.append(job_similarity * 0.4)

        # 行业一致性
        voice_industry = voice_info.get("professional_info", {}).get("industry", "").lower()
        linkedin_industry = linkedin_info.get("industry", "").lower()

        if voice_industry and linkedin_industry:
            industry_similarity = self._calculate_text_similarity(voice_industry, linkedin_industry)
            consistency_factors.append(industry_similarity * 0.3)

        # 技能一致性
        voice_interests = [interest.lower() for interest in voice_info.get("interests", [])]
        linkedin_skills = [skill.lower() for skill in linkedin_info.get("skills", [])]

        if voice_interests and linkedin_skills:
            skills_overlap = len(set(voice_interests) & set(linkedin_skills)) / len(set(voice_interests) | set(linkedin_skills))
            consistency_factors.append(skills_overlap * 0.3)

        return sum(consistency_factors) if consistency_factors else 0.5

    # ============ 内部验证方法 ============

    def _create_linkedin_profile(self, user_id: str, linkedin_url: str, linkedin_data: Dict[str, Any]) -> LinkedInProfile:
        """创建LinkedIn个人资料对象"""
        basic_info = linkedin_data.get("basic_info", {})

        return LinkedInProfile(
            user_id=user_id,
            profile_url=linkedin_url,
            name=basic_info.get("name", ""),
            headline=basic_info.get("headline", ""),
            location=basic_info.get("location", ""),
            industry=basic_info.get("industry", ""),
            summary=basic_info.get("summary", ""),
            experience=linkedin_data.get("experience", []),
            education=linkedin_data.get("education", []),
            skills=linkedin_data.get("skills", []),
            connections=linkedin_data.get("connections", ""),
            scraped_at=datetime.now()
        )

    def _verify_basic_info_consistency(self, linkedin_profile: LinkedInProfile, voice_analysis) -> float:
        """验证基本信息一致性"""
        consistency_score = 0.0
        factors = 0

        # 位置一致性
        linkedin_location = linkedin_profile.location.lower()
        voice_professional = voice_analysis.professional_info

        if voice_professional and "location" in voice_professional:
            voice_location = voice_professional["location"].lower()
            if linkedin_location and voice_location:
                location_match = self._calculate_location_similarity(linkedin_location, voice_location)
                consistency_score += location_match
                factors += 1

        # 行业一致性
        linkedin_industry = linkedin_profile.industry.lower()
        if voice_professional and "industry" in voice_professional:
            voice_industry = voice_professional["industry"].lower()
            if linkedin_industry and voice_industry:
                industry_match = self._calculate_text_similarity(linkedin_industry, voice_industry)
                consistency_score += industry_match
                factors += 1

        return consistency_score / factors if factors > 0 else 0.7  # 默认较高分数

    def _verify_professional_consistency(self, linkedin_profile: LinkedInProfile, voice_analysis) -> float:
        """验证职业信息一致性"""
        consistency_score = 0.0
        factors = 0

        voice_professional = voice_analysis.professional_info
        if not voice_professional:
            return 0.6  # 默认分数

        # 职位标题一致性
        linkedin_title = linkedin_profile.headline.split(" at ")[0].lower()
        voice_career_focus = voice_professional.get("career_focus", "").lower()

        if linkedin_title and voice_career_focus:
            title_similarity = self._calculate_text_similarity(linkedin_title, voice_career_focus)
            consistency_score += title_similarity
            factors += 1

        # 经验级别一致性
        if linkedin_profile.experience:
            linkedin_experience_years = self._calculate_total_experience(linkedin_profile.experience)
            voice_experience_level = voice_professional.get("experience_level", "").lower()

            experience_consistency = self._verify_experience_level_consistency(
                linkedin_experience_years, voice_experience_level
            )
            consistency_score += experience_consistency
            factors += 1

        return consistency_score / factors if factors > 0 else 0.6

    def _verify_education_consistency(self, linkedin_profile: LinkedInProfile, voice_analysis) -> float:
        """验证教育信息一致性"""
        # 教育信息通常在语音中不会详细提及，给予较高的默认分数
        if linkedin_profile.education:
            return 0.8  # 有教育信息就给高分
        else:
            return 0.6  # 没有教育信息给中等分数

    def _verify_skills_consistency(self, linkedin_profile: LinkedInProfile, voice_analysis) -> float:
        """验证技能一致性"""
        voice_interests = [interest.lower() for interest in voice_analysis.interests]
        linkedin_skills = [skill.lower() for skill in linkedin_profile.skills]

        if not voice_interests or not linkedin_skills:
            return 0.6  # 默认分数

        # 计算技能和兴趣的重叠度
        overlap = len(set(voice_interests) & set(linkedin_skills))
        union = len(set(voice_interests) | set(linkedin_skills))

        if union == 0:
            return 0.6

        jaccard_similarity = overlap / union

        # 技能匹配的权重较低，因为语音中的兴趣可能与LinkedIn技能不完全对应
        return min(1.0, jaccard_similarity + 0.4)

    def _verify_experience_level_consistency(self, linkedin_years: int, voice_level: str) -> float:
        """验证经验级别一致性"""
        level_mapping = {
            "entry": (0, 2),
            "junior": (1, 3),
            "mid": (3, 7),
            "senior": (5, 15),
            "executive": (10, 50)
        }

        if voice_level not in level_mapping:
            return 0.7  # 默认分数

        min_years, max_years = level_mapping[voice_level]

        if min_years <= linkedin_years <= max_years:
            return 1.0  # 完全匹配
        elif abs(linkedin_years - min_years) <= 2 or abs(linkedin_years - max_years) <= 2:
            return 0.8  # 接近匹配
        else:
            return 0.4  # 不匹配

    # ============ 工具方法 ============

    def _calculate_total_experience(self, experience_list: List[Dict[str, Any]]) -> int:
        """计算总工作经验年数"""
        total_years = 0

        for exp in experience_list:
            duration = exp.get("duration", "")
            years = self._extract_years_from_duration(duration)
            total_years += years

        return total_years

    def _extract_years_from_duration(self, duration: str) -> int:
        """从工作时长字符串中提取年数"""
        if not duration:
            return 0

        # 简单的年数提取逻辑
        if "Present" in duration or "present" in duration:
            # 计算到现在的年数
            start_year = self._extract_start_year(duration)
            if start_year:
                return datetime.now().year - start_year

        # 提取年份范围
        years = re.findall(r'\d{4}', duration)
        if len(years) >= 2:
            return int(years[-1]) - int(years[0])

        return 1  # 默认1年

    def _extract_start_year(self, duration: str) -> Optional[int]:
        """提取开始年份"""
        years = re.findall(r'\d{4}', duration)
        return int(years[0]) if years else None

    def _extract_graduation_year(self, years_str: str) -> Optional[int]:
        """提取毕业年份"""
        years = re.findall(r'\d{4}', years_str)
        return int(years[-1]) if years else None

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        if not text1 or not text2:
            return 0.0

        # 简单的关键词匹配
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        intersection = len(words1 & words2)
        union = len(words1 | words2)

        if union == 0:
            return 0.0

        return intersection / union

    def _calculate_location_similarity(self, location1: str, location2: str) -> float:
        """计算位置相似度"""
        # 提取城市名
        city1 = location1.split(",")[0].strip().lower()
        city2 = location2.split(",")[0].strip().lower()

        if city1 == city2:
            return 1.0

        # 检查是否包含相同的城市名
        if city1 in city2 or city2 in city1:
            return 0.8

        return 0.3  # 不同城市给低分

    def _generate_verification_details(self, consistency_scores: Dict[str, float]) -> Dict[str, Any]:
        """生成验证详情"""
        details = {
            "verification_summary": "Profile verification completed",
            "strengths": [],
            "concerns": [],
            "recommendations": []
        }

        for dimension, score in consistency_scores.items():
            if score >= 0.8:
                details["strengths"].append(f"Strong {dimension} consistency")
            elif score < 0.5:
                details["concerns"].append(f"Low {dimension} consistency")

        if not details["concerns"]:
            details["recommendations"].append("Profile appears highly consistent")
        else:
            details["recommendations"].append("Consider reviewing profile information for accuracy")

        return details

    def _create_fallback_verification_result(self, user_id: str, reason: str) -> VerificationResult:
        """创建备用验证结果"""
        return VerificationResult(
            user_id=user_id,
            linkedin_url="",
            overall_score=0.5,
            is_verified=False,
            consistency_breakdown={"error": 0.0},
            verification_details={"error": reason, "fallback": True},
            verified_at=datetime.now()
        )

    # ============ 管理和统计方法 ============

    def get_linkedin_profile(self, user_id: str) -> Optional[LinkedInProfile]:
        """获取用户的LinkedIn个人资料"""
        return self._profile_cache.get(user_id)

    def get_verification_result(self, user_id: str) -> Optional[VerificationResult]:
        """获取用户的验证结果"""
        return self._verification_cache.get(user_id)

    def clear_user_cache(self, user_id: str) -> None:
        """清除用户的LinkedIn缓存"""
        self._profile_cache.pop(user_id, None)
        self._verification_cache.pop(user_id, None)
        logger.info(f"Cleared LinkedIn cache for user: {user_id}")

    def get_linkedin_service_stats(self) -> Dict[str, Any]:
        """获取LinkedIn服务统计信息"""
        # 计算平均验证分数
        verification_scores = [
            result.overall_score for result in self._verification_cache.values()
            if result.overall_score > 0
        ]
        avg_verification_score = sum(verification_scores) / len(verification_scores) if verification_scores else 0

        # 计算验证通过率
        verified_count = sum(1 for result in self._verification_cache.values() if result.is_verified)
        total_verifications = len(self._verification_cache)
        verification_rate = verified_count / total_verifications if total_verifications > 0 else 0

        return {
            "cached_profiles": len(self._profile_cache),
            "cached_verifications": len(self._verification_cache),
            "average_verification_score": round(avg_verification_score, 3),
            "verification_pass_rate": round(verification_rate, 3),
            "consistency_threshold": self.consistency_threshold,
            "verification_weights": self.verification_weights,
            "bright_data_zone": self.bright_data_client.zone
        }

    def handle_verification_completed(self, user_id: str, verification_result: VerificationResult):
        """处理验证完成"""
        try:
            # 触发验证完成事件
            if verification_result.is_verified:
                logger.info(f"LinkedIn verification successful for user: {user_id}")
            else:
                logger.warning(f"LinkedIn verification failed for user: {user_id}")

            # 可以在这里添加额外的处理逻辑，如发送通知等

        except Exception as e:
            logger.error(f"Verification completion handling failed for user {user_id}: {e}")
