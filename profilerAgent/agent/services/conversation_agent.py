"""
ConversationAgent - Intelligent Conversation Engine
Replaces hardcoded voice interview logic with AI-driven conversation
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass
from functools import wraps

from ..config.core_config import CoreConfig
from ..utils import APIResponse
from .ai_service import AIService
from ..core.database import DatabaseManager

logger = logging.getLogger(__name__)

# Time logging decorator
def time_logger(func_name: str = None):
    """Decorator to log execution time of functions"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            name = func_name or f"{func.__name__}"

            try:
                result = await func(*args, **kwargs)
                end_time = time.perf_counter()
                execution_time = end_time - start_time
                logger.info(f"⏱️  {name} completed in {execution_time:.3f}s")
                return result
            except Exception as e:
                end_time = time.perf_counter()
                execution_time = end_time - start_time
                logger.error(f"⏱️  {name} failed after {execution_time:.3f}s: {e}")
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            name = func_name or f"{func.__name__}"

            try:
                result = func(*args, **kwargs)
                end_time = time.perf_counter()
                execution_time = end_time - start_time
                logger.info(f"⏱️  {name} completed in {execution_time:.3f}s")
                return result
            except Exception as e:
                end_time = time.perf_counter()
                execution_time = end_time - start_time
                logger.error(f"⏱️  {name} failed after {execution_time:.3f}s: {e}")
                raise

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator

@dataclass
class ConversationContext:
    """Enhanced conversation context for AI agent"""
    call_sid: str
    user_id: str
    conversation_stage: str
    collected_info: Dict[str, Any]
    conversation_history: List[Dict[str, str]]
    emotional_state: Dict[str, float]
    time_remaining: int
    questions_asked: int
    successful_responses: int
    timeout_count: int
    # Add tracking for asked questions to prevent repetition
    asked_questions: set = None
    stage_attempts: Dict[str, int] = None

    def __post_init__(self):
        if self.asked_questions is None:
            self.asked_questions = set()
        if self.stage_attempts is None:
            self.stage_attempts = {}
    
    def add_exchange(self, question: str, response: str = ""):
        """Add question-response exchange to history"""
        exchange = {
            "timestamp": datetime.now().isoformat(),
            "question": question,
            "response": response,
            "stage": self.conversation_stage
        }
        self.conversation_history.append(exchange)

        if question:
            self.questions_asked += 1
            # Track asked questions to prevent repetition
            self.asked_questions.add(question.lower().strip())
        if response:
            self.successful_responses += 1

    def has_asked_similar(self, question: str) -> bool:
        """Check if we've asked a similar question before"""
        question_lower = question.lower().strip()
        # Check for exact or very similar questions
        for asked in self.asked_questions:
            # Check for high similarity (>70% word overlap)
            asked_words = set(asked.split())
            new_words = set(question_lower.split())
            if len(asked_words) > 0 and len(new_words) > 0:
                overlap = len(asked_words.intersection(new_words)) / min(len(asked_words), len(new_words))
                if overlap > 0.7:
                    return True
        return False
    
    def get_recent_context(self, num_exchanges: int = 3) -> str:
        """Get recent conversation context as formatted string"""
        recent = self.conversation_history[-num_exchanges:] if self.conversation_history else []
        context_str = ""
        for exchange in recent:
            if exchange["question"]:
                context_str += f"Assistant: {exchange['question']}\n"
            if exchange["response"]:
                context_str += f"User: {exchange['response']}\n"
        return context_str

    def increment_stage_attempts(self, stage: str):
        """Track how many times we've attempted each stage"""
        self.stage_attempts[stage] = self.stage_attempts.get(stage, 0) + 1

@dataclass
class AgentDecision:
    """Agent decision result"""
    action_type: str  # "ask_question", "clarify", "summarize", "end_call"
    content: str      # What to say
    confidence: float
    reasoning: str
    next_stage: Optional[str] = None
    priority_goals: List[str] = None

class ConversationAgent:
    """Intelligent conversation agent for voice interviews"""
    
    def __init__(self, ai_service: AIService = None, db_manager: DatabaseManager = None):
        self.ai_service = ai_service or AIService()
        self.db_manager = db_manager
        
        # Decision timeout settings
        self.decision_timeout = 8.0  # 8 seconds max for AI decision (allow time for DeepSeek API)
        self.fallback_timeout = 2.0  # 2 seconds for fallback
        
        # Required information checkpoints
        self.required_checkpoints = [
            "basic_info",      # Name, city
            "profession",      # Job, industry
            "personality",     # MBTI indicators
            "interests",       # Hobbies, passions
            "relationships"    # What they're looking for
        ]

        # FIXED: More flexible stage requirements
        self.stage_requirements = {
            "greeting": {
                "required": [],  # No hard requirements for greeting
                "preferred": ["name", "city"],
                "max_attempts": 3  # Don't stay in greeting forever
            },
            "professional": {
                "required": [],
                "preferred": ["profession"],
                "max_attempts": 2
            },
            "personality": {
                "required": [],
                "preferred": ["personality_traits"],
                "max_attempts": 2
            },
            "interests": {
                "required": [],
                "preferred": ["interests"],
                "max_attempts": 2
            },
            "relationships": {
                "required": [],
                "preferred": ["relationship_preferences"],
                "max_attempts": 2
            }
        }

        # Conversation stage flow
        self.stage_flow = {
            "greeting": ["professional", "personality"],
            "professional": ["personality", "interests"],
            "personality": ["interests", "relationships"],
            "interests": ["relationships", "closing"],
            "relationships": ["closing"],
            "closing": ["completed"]
        }

        # Emergency fallback questions (only for system failures)
        self.emergency_fallbacks = {
            "greeting": "What should I call you?",
            "professional": "What kind of work do you do?",
            "personality": "Tell me about your personality.",
            "interests": "What do you enjoy doing?",
            "relationships": "What are you looking for?",
            "general": "Tell me more about yourself."
        }

        logger.info("ConversationAgent initialized with improved performance settings")

    # ============ ConversationRelay Support Methods ============

    async def get_user_by_call_sid(self, call_sid: str) -> Optional[str]:
        """Get user ID by call SID for ConversationRelay"""
        try:
            if not self.db_manager:
                logger.warning("No database manager available for call_sid lookup")
                return None

            # Try to find user from active sessions or database
            # This would need to be implemented based on your database schema
            # For now, return None to trigger error handling
            logger.warning(f"get_user_by_call_sid not fully implemented for {call_sid}")
            return None

        except Exception as e:
            logger.error(f"Error finding user by call_sid {call_sid}: {e}")
            return None

    async def process_voice_conversation(self, user_id: str, user_text: str, context: dict) -> dict:
        """Process voice conversation input for ConversationRelay"""
        try:
            # Convert context dict to ConversationContext if needed
            if isinstance(context, dict):
                conversation_context = ConversationContext(
                    call_sid=context.get("call_sid", "relay_session"),
                    user_id=user_id,
                    conversation_stage=context.get("conversation_stage", "greeting"),
                    collected_info=context.get("collected_info", {}),
                    conversation_history=context.get("conversation_history", []),
                    emotional_state=context.get("emotional_state", {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}),
                    time_remaining=context.get("time_remaining", 300),
                    questions_asked=context.get("questions_asked", 0),
                    successful_responses=context.get("successful_responses", 0),
                    timeout_count=context.get("timeout_count", 0)
                )
            else:
                conversation_context = context

            # Process the input using existing logic
            decision = await self.process_input(user_text, conversation_context)

            # Convert back to dict format for ConversationRelay
            result = {
                "success": True,
                "response": decision.content,
                "context": {
                    "call_sid": conversation_context.call_sid,
                    "conversation_stage": conversation_context.conversation_stage,
                    "collected_info": conversation_context.collected_info,
                    "conversation_history": conversation_context.conversation_history,
                    "emotional_state": conversation_context.emotional_state,
                    "time_remaining": conversation_context.time_remaining,
                    "questions_asked": conversation_context.questions_asked,
                    "successful_responses": conversation_context.successful_responses,
                    "timeout_count": conversation_context.timeout_count
                },
                "should_end": decision.action_type == "end_call" or decision.next_stage == "completed",
                "confidence": decision.confidence,
                "reasoning": decision.reasoning
            }

            return result

        except Exception as e:
            logger.error(f"Error processing voice conversation: {e}")
            return {
                "success": False,
                "response": "I'm sorry, I didn't quite catch that. Could you please repeat?",
                "context": context,
                "should_end": False,
                "confidence": 0.3,
                "reasoning": f"Error in processing: {str(e)}"
            }
    
    @time_logger("process_input")
    async def process_input(self, user_input: str, context: ConversationContext) -> AgentDecision:
        """Process user input and make intelligent decision with async optimization"""
        try:
            # Update context with user input
            if user_input.strip():
                context.add_exchange("", user_input)

                # 🆕 Step 1: Rule-based intent detection for clear cases
                intent = self._detect_clear_intent(user_input, context)
                if intent != "continue":
                    logger.info(f"Clear intent detected: {intent}")
                    decision = await self._handle_clear_intent(intent, user_input, context)
                    if decision:
                        return decision

                # 🚀 ASYNC OPTIMIZATION: Parallel processing of AI tasks
                decision = await self._process_with_async_optimization(user_input, context)
            else:
                # No user input, just make decision based on context
                decision = await self._make_smart_decision(context)

            # FIXED: Record question BEFORE adding to context and check for repetition
            if decision.content and not context.has_asked_similar(decision.content):
                context.add_exchange(decision.content, "")
            elif decision.content and context.has_asked_similar(decision.content):
                # Prevent repetition - let AI regenerate with explicit instruction
                logger.warning(f"Preventing repetition of question: {decision.content}")
                decision = await self._regenerate_ai_decision_avoiding_repetition(context)
                if decision.content:
                    context.add_exchange(decision.content, "")

            # FIXED: More flexible stage transition logic
            new_stage = await self._determine_stage_transition(context, decision)
            if new_stage and new_stage != context.conversation_stage:
                logger.info(f"Agent transitioning stage: {context.conversation_stage} -> {new_stage}")
                context.conversation_stage = new_stage
                context.increment_stage_attempts(new_stage)
                decision.next_stage = new_stage

            # 🚀 ASYNC OPTIMIZATION: Save to database in background
            asyncio.create_task(self._save_agent_data_background(context, decision, user_input))

            return decision

        except Exception as e:
            logger.error(f"Error processing input: {e}")
            return self._create_fallback_decision(context)

    @time_logger("async_optimization")
    async def _process_with_async_optimization(self, user_input: str, context: ConversationContext) -> AgentDecision:
        """Process user input with async optimization for better performance"""
        start_time = asyncio.get_event_loop().time()

        try:
            # 🚀 Phase 1: Parallel execution of independent AI tasks
            logger.debug("Starting parallel AI processing...")

            info_task = asyncio.create_task(self._extract_information_safe(user_input, context))
            emotion_task = asyncio.create_task(self._update_emotional_state_safe(user_input, context))

            # Wait for both tasks with timeout protection
            try:
                await asyncio.wait_for(
                    asyncio.gather(info_task, emotion_task, return_exceptions=True),
                    timeout=5  # 5 second timeout for parallel tasks
                )
                parallel_time = asyncio.get_event_loop().time() - start_time
                logger.info(f"Parallel AI processing completed in {parallel_time:.2f}s")

            except asyncio.TimeoutError:
                logger.warning("Parallel AI processing timed out, continuing with available data")
                # Cancel pending tasks
                info_task.cancel()
                emotion_task.cancel()

            # 🚀 Phase 2: Generate decision based on available context
            decision_start = asyncio.get_event_loop().time()
            decision = await asyncio.wait_for(
                self._make_smart_decision(context),
                timeout=5.0  # 5 second timeout for decision making
            )
            decision_time = asyncio.get_event_loop().time() - decision_start

            total_time = asyncio.get_event_loop().time() - start_time
            logger.info(f"Decision generated in {decision_time:.2f}s, total processing: {total_time:.2f}s")

            return decision

        except asyncio.TimeoutError:
            logger.warning("Decision making timed out, using fallback")
            return self._create_fallback_decision(context)
        except Exception as e:
            logger.error(f"Async processing failed: {e}")
            return self._create_fallback_decision(context)

    @time_logger("extract_information")
    async def _extract_information_safe(self, user_input: str, context: ConversationContext):
        """Safe wrapper for information extraction with error handling"""
        try:
            await self._extract_information(user_input, context)
        except Exception as e:
            logger.warning(f"Information extraction failed: {e}")

    @time_logger("update_emotional_state")
    async def _update_emotional_state_safe(self, user_input: str, context: ConversationContext):
        """Safe wrapper for emotional state update with error handling"""
        try:
            await self._update_emotional_state(user_input, context)
        except Exception as e:
            logger.warning(f"Emotional state update failed: {e}")

    async def _save_agent_data_background(self, context: ConversationContext, decision: AgentDecision, user_input: str):
        """Save agent data in background without blocking user response"""
        try:
            await self._save_agent_data(context, decision, user_input)
            logger.debug("Agent data saved to database in background")
        except Exception as e:
            logger.error(f"Background database save failed: {e}")

    def _detect_clear_intent(self, user_input: str, context: ConversationContext) -> str:
        """Detect clear user intents using rule-based matching"""
        input_lower = user_input.lower().strip()
        
        # 1. Hangup/Goodbye intents - ENHANCED
        hangup_patterns = [
            "goodbye", "bye", "good bye", "see you", "farewell",
            "hang up", "end call", "end this call", "disconnect",
            "i'm done", "i am done", "we're done", "that's it",
            "i need to go", "i have to go", "gotta go", "need to go",
            "not interested", "don't want to do this", "changed my mind",
            "stop this", "quit", "exit", "cancel",
            "please hang up", "can you hang up", "hang up please",
            "end the call", "finish this", "that's all",
            "nothing else", "no more questions"
        ]
        
        if any(pattern in input_lower for pattern in hangup_patterns):
            return "hangup"
        
        # 2. Frustrated repeat - user is annoyed with repetition
        repeat_frustration_patterns = [
            "already told you", "i said", "like i said", "i mentioned",
            "told you before", "said before", "repeated", "again"
        ]
        
        if any(pattern in input_lower for pattern in repeat_frustration_patterns):
            return "frustrated_repeat"
        
        # 3. Refusal to answer - user doesn't want to share
        refusal_patterns = [
            "don't want to answer", "won't answer", "not answering",
            "none of your business", "personal", "private",
            "don't want to share", "won't share", "not sharing",
            "not comfortable", "uncomfortable sharing",
            "why should i tell you", "why do you need to know"
        ]
        
        if any(pattern in input_lower for pattern in refusal_patterns):
            return "refuse_answer"
        
        # 4. Very short/unhelpful responses - might indicate disengagement
        if len(input_lower) <= 3 and input_lower in ["ok", "yes", "no", "sure", "nah", "meh", "idk"]:
            return "disengaged"
        
        # 5. Gibberish/nonsense - random characters or meaningless words
        words = input_lower.split()
        if len(words) > 0:
            # Check if most words are very short or contain random characters
            weird_words = [w for w in words if len(w) > 6 and not any(c.isalpha() for c in w)]
            if len(weird_words) > len(words) * 0.5:  # More than 50% weird words
                return "gibberish"
        
        return "continue"
    
    async def _handle_clear_intent(self, intent: str, user_input: str, context: ConversationContext) -> AgentDecision:
        """Handle clearly detected intents with appropriate responses"""
        
        if intent == "hangup":
            return AgentDecision(
                action_type="end_call",
                content="I completely understand. Thank you so much for your time! Have a wonderful day!",
                confidence=0.95,
                reasoning=f"User expressed clear intent to end call: '{user_input}'",
                next_stage="completed"
            )
        
        elif intent == "frustrated_repeat":
            # User is frustrated with repetition - acknowledge and change topic
            missing_info = self._get_missing_information(context)
            if missing_info:
                if "interests" in missing_info:
                    new_question = "I apologize for the confusion! Let's talk about something different. What do you love doing in your free time?"
                elif "relationships" in missing_info:
                    new_question = "Sorry about that! Let me ask something else. What qualities do you value most in a partner?"
                elif "personality" in missing_info:
                    new_question = "My apologies! How about this - are you more of a morning person or night owl?"
                else:
                    new_question = "I'm sorry for the mix-up! Tell me, what's something that always makes you smile?"
            else:
                new_question = "I apologize for any confusion! Is there anything else you'd like to share about yourself?"
            
            return AgentDecision(
                action_type="clarify",
                content=new_question,
                confidence=0.85,
                reasoning="User frustrated with repetition, switching topics",
                next_stage=self._get_next_stage_for_missing_info(context)
            )
        
        elif intent == "refuse_answer":
            # User doesn't want to answer - be understanding and try different topic
            return AgentDecision(
                action_type="clarify",
                content="No worries at all! Everyone has their comfort zone. How about we talk about something lighter - what's your favorite way to spend a weekend?",
                confidence=0.8,
                reasoning="User uncomfortable with current question, switching to lighter topic",
                next_stage="interests"
            )
        
        elif intent == "disengaged":
            # User giving very short responses - try to re-engage
            emotion_state = context.emotional_state
            if emotion_state.get("engagement", 0.5) < 0.4:
                return AgentDecision(
                    action_type="clarify",
                    content="I can sense you might be busy or not feeling this right now. Would you prefer to try this another time, or is there a topic you'd actually enjoy talking about?",
                    confidence=0.75,
                    reasoning="User seems disengaged, offering alternative",
                    next_stage=context.conversation_stage
                )
            else:
                return AgentDecision(
                    action_type="ask_question",
                    content="Tell me something that genuinely excites you - what are you passionate about?",
                    confidence=0.7,
                    reasoning="User giving short responses, trying to re-engage with passion topic",
                    next_stage="interests"
                )
        
        elif intent == "gibberish":
            # User giving nonsense responses - politely redirect
            return AgentDecision(
                action_type="clarify",
                content="I want to make sure I understand you correctly. Could you tell me a bit about yourself in your own words?",
                confidence=0.6,
                reasoning="User input unclear, requesting clarification",
                next_stage=context.conversation_stage
            )
        
        return None  # No clear intent handling needed

    async def _determine_stage_transition(self, context: ConversationContext, decision: AgentDecision) -> Optional[str]:
        """FIXED: Flexible stage transition logic"""
        current_stage = context.conversation_stage

        # If decision already has next_stage, use it
        if decision.next_stage:
            return decision.next_stage

        # Check max attempts for current stage
        stage_attempts = context.stage_attempts.get(current_stage, 0)
        max_attempts = self.stage_requirements.get(current_stage, {}).get("max_attempts", 3)

        if stage_attempts >= max_attempts:
            logger.info(f"Max attempts ({max_attempts}) reached for stage {current_stage}, moving on")
            return self._get_next_logical_stage(context)

        # Check if we have enough info to progress
        if self._has_sufficient_info_for_stage(context, current_stage):
            return self._get_next_logical_stage(context)

        # Time-based transitions
        if context.time_remaining < 30:
            return "closing"

        # Check overall progress
        total_info_collected = sum(1 for v in context.collected_info.values() if v)
        if total_info_collected >= 3 and context.questions_asked > 5:
            # We have decent info, can progress
            return self._get_next_logical_stage(context)

        return None

    def _has_sufficient_info_for_stage(self, context: ConversationContext, stage: str) -> bool:
        """Check if we have sufficient info to move past current stage"""
        requirements = self.stage_requirements.get(stage, {})

        # Check required fields (must have all)
        for field in requirements.get("required", []):
            if not context.collected_info.get(field):
                return False

        # Check preferred fields (should have at least one)
        preferred_fields = requirements.get("preferred", [])
        if preferred_fields:
            has_any_preferred = any(context.collected_info.get(field) for field in preferred_fields)
            return has_any_preferred

        # If no requirements, can progress
        return True

    def _get_next_logical_stage(self, context: ConversationContext) -> Optional[str]:
        """Get the next logical stage in conversation flow"""
        current = context.conversation_stage
        possible_next = self.stage_flow.get(current, [])

        if possible_next:
            # Skip to stage where we need info
            for next_stage in possible_next:
                if not self._has_sufficient_info_for_stage(context, next_stage):
                    return next_stage
            # If all stages have info, go to the last option
            return possible_next[-1] if possible_next else None

        return None

    async def _regenerate_ai_decision_avoiding_repetition(self, context: ConversationContext) -> AgentDecision:
        """Let AI regenerate decision with explicit instruction to avoid repetition"""
        try:
            # Build special prompt that explicitly mentions avoiding repetition
            asked_questions_list = list(context.asked_questions)
            recent_questions = asked_questions_list[-5:] if len(asked_questions_list) > 5 else asked_questions_list

            repetition_prompt = f"""
IMPORTANT: You just generated a question that was too similar to previous questions.

Recent questions you've already asked:
{chr(10).join(f"- {q}" for q in recent_questions)}

Please generate a COMPLETELY DIFFERENT question that:
1. Explores a different aspect of the current stage ({context.conversation_stage})
2. Uses different wording and approach
3. Feels natural and engaging
4. Avoids any similarity to the questions listed above

Current conversation stage: {context.conversation_stage}
Missing information: {self._get_missing_information(context)}
"""

            # Use AI to generate new decision with repetition avoidance
            ai_response = await asyncio.wait_for(
                self.ai_service.make_decision(repetition_prompt, context.conversation_history),
                timeout=self.decision_timeout
            )

            if ai_response and isinstance(ai_response, dict):
                return self._parse_ai_decision(ai_response, context)
            else:
                # If AI fails, use emergency fallback
                return self._create_emergency_fallback(context)

        except Exception as e:
            logger.error(f"Failed to regenerate AI decision: {e}")
            return self._create_emergency_fallback(context)

    def _create_emergency_fallback(self, context: ConversationContext) -> AgentDecision:
        """Create emergency fallback when AI completely fails"""
        stage = context.conversation_stage
        fallback_question = self.emergency_fallbacks.get(stage, self.emergency_fallbacks["general"])

        return AgentDecision(
            action_type="ask_question",
            content=fallback_question,
            confidence=0.3,
            reasoning="Emergency fallback due to AI failure"
        )

    def _get_next_stage_for_missing_info(self, context: ConversationContext) -> str:
        """Get appropriate next stage based on missing information"""
        missing = self._get_missing_information(context)
        
        if "basic_info" in missing:
            return "greeting"
        elif "profession" in missing:
            return "professional"
        elif "personality" in missing:
            return "personality"
        elif "interests" in missing:
            return "interests"
        elif "relationships" in missing:
            return "relationships"
        else:
            return "completed"
    
    @time_logger("make_smart_decision")
    async def _make_smart_decision(self, context: ConversationContext) -> AgentDecision:
        """Make intelligent decision with fallback"""
        try:
            # Try AI decision with timeout
            decision = await asyncio.wait_for(
                self._ai_decision(context),
                timeout=self.decision_timeout
            )
            return decision
        except asyncio.TimeoutError:
            logger.warning(f"AI decision timeout for call {context.call_sid}")
            return await self._fallback_decision(context)
        except Exception as e:
            logger.error(f"AI decision error: {e}")
            return await self._fallback_decision(context)

    @time_logger("ai_decision")
    async def _ai_decision(self, context: ConversationContext) -> AgentDecision:
        """Make AI-powered decision"""
        
        # Build context for AI
        ai_context = self._build_ai_context(context)
        
        # Generate AI decision
        system_prompt = self._get_decision_system_prompt()
        user_prompt = self._build_decision_prompt(context, ai_context)
        
        try:
            # Use the actual AIService method
            # For MVP, we'll use a simplified approach
            ai_question = await self.ai_service.generate_simple_question(
                domain=context.conversation_stage,
                user_response=user_prompt
            )
            
            if ai_question:
                return AgentDecision(
                    action_type="ask_question",
                    content=ai_question,
                    confidence=0.8,
                    reasoning="AI generated question based on conversation context"
                )
            else:
                # AI failed, raise exception to trigger fallback
                raise Exception("AI service returned None")
            
        except Exception as e:
            logger.error(f"AI service error: {e}")
            raise
    
    def _get_decision_system_prompt(self) -> str:
        """Get system prompt for AI decision making"""
        return """You are an intelligent conversation agent conducting voice interviews for a dating app.
Your goal is to understand the user's personality, interests, and relationship preferences through natural conversation.

Key principles:
1. Keep the conversation natural and friendly, like talking to a friend
2. Ask one question at a time
3. Build on what the user just said
4. Adapt your style to the user's emotional state
5. Ensure you collect key information: profession, personality traits, interests, relationship goals
6. Keep responses concise (1-2 sentences max)
7. If the user seems uncomfortable, be more gentle and reassuring
8. **IMPORTANT**: If the user explicitly asks for more dating/relationship content, continue the conversation - don't end it!
9. Pay attention to user requests like "can we talk about dating/relationships/partners/love"

Response format:
{
    "action_type": "ask_question|clarify|summarize|end_call",
    "content": "what to say to the user",
    "confidence": 0.0-1.0,
    "reasoning": "why this decision was made",
    "next_stage": "optional stage transition",
    "priority_goals": ["list", "of", "current", "priorities"]
}"""

    def _build_decision_prompt(self, context: ConversationContext, ai_context: Dict) -> str:
        """Build user prompt for AI decision"""
        recent_conversation = context.get_recent_context(3)
        
        missing_info = self._get_missing_information(context)
        
        prompt = f"""
Current situation:
- Time remaining: {context.time_remaining} seconds
- Current stage: {context.conversation_stage}
- Questions asked: {context.questions_asked}
- Successful responses: {context.successful_responses}
- Timeout count: {context.timeout_count}

Recent conversation:
{recent_conversation}

Information collected so far:
{json.dumps(context.collected_info, indent=2)}

Still need to learn about:
{', '.join(missing_info) if missing_info else 'All key information collected'}

User's apparent emotional state:
- Engagement: {context.emotional_state.get('engagement', 0.5)}
- Comfort level: {context.emotional_state.get('comfort', 0.5)}
- Enthusiasm: {context.emotional_state.get('enthusiasm', 0.5)}

What should I say next to continue this natural conversation while gathering the needed information?
"""
        return prompt
    
    def _build_ai_context(self, context: ConversationContext) -> Dict:
        """Build context dictionary for AI processing"""
        return {
            "conversation_stage": context.conversation_stage,
            "time_remaining": context.time_remaining,
            "collected_info": context.collected_info,
            "emotional_state": context.emotional_state,
            "recent_exchanges": context.conversation_history[-3:] if context.conversation_history else [],
            "missing_info": self._get_missing_information(context)
        }
    
    def _parse_ai_decision(self, ai_response: Dict, context: ConversationContext) -> AgentDecision:
        """Parse AI response into AgentDecision"""
        try:
            return AgentDecision(
                action_type=ai_response.get("action_type", "ask_question"),
                content=ai_response.get("content", ""),
                confidence=float(ai_response.get("confidence", 0.5)),
                reasoning=ai_response.get("reasoning", ""),
                next_stage=ai_response.get("next_stage"),
                priority_goals=ai_response.get("priority_goals", [])
            )
        except Exception as e:
            logger.error(f"Error parsing AI decision: {e}")
            return self._create_fallback_decision(context)
    
    async def _fallback_decision(self, context: ConversationContext) -> AgentDecision:
        """Smart fallback decision when AI fails"""
        try:
            return await asyncio.wait_for(
                self._smart_template_decision(context),
                timeout=self.fallback_timeout
            )
        except:
            return self._create_fallback_decision(context)
    
    async def _smart_template_decision(self, context: ConversationContext) -> AgentDecision:
        """Intelligent template-based decision"""
        missing_info = self._get_missing_information(context)
        
        # If we're running out of time, prioritize missing critical info
        if context.time_remaining < 60 and missing_info:
            return self._create_urgent_question(missing_info[0], context)
        
        # Check if we should advance to next stage
        next_stage = self.should_change_stage(context)
        
        # Normal flow - ask about next logical topic
        if context.conversation_stage == "greeting":
            if context.collected_info.get("name"):
                # We have basic info, move to professional
                return AgentDecision(
                    action_type="ask_question",
                    content="That sounds great! What kind of work do you do?",
                    confidence=0.8,
                    reasoning="Basic info collected, moving to professional questions",
                    next_stage="professional"
                )
            else:
                return AgentDecision(
                    action_type="ask_question",
                    content="Hi! Great to chat with you! Could you start by telling me your name and which city you're in?",
                    confidence=0.8,
                    reasoning="Opening greeting to collect basic info"
                )
        
        elif context.conversation_stage == "professional":
            if context.collected_info.get("profession") and next_stage:
                return AgentDecision(
                    action_type="ask_question",
                    content="Do you prefer hanging out with friends or having some alone time to recharge?",
                    confidence=0.8,
                    reasoning="Professional info collected, moving to personality",
                    next_stage="personality"
                )
            else:
                return AgentDecision(
                    action_type="ask_question", 
                    content="What kind of work do you do?",
                    confidence=0.7,
                    reasoning="Need to collect professional information"
                )
        
        elif context.conversation_stage == "personality":
            if context.collected_info.get("personality_traits") and next_stage:
                return AgentDecision(
                    action_type="ask_question",
                    content="Outside of work, what gets you most excited? What are you passionate about?",
                    confidence=0.8,
                    reasoning="Personality traits collected, moving to interests",
                    next_stage="interests"
                )
            else:
                return AgentDecision(
                    action_type="ask_question",
                    content="Do you prefer hanging out with friends or having some alone time to recharge?",
                    confidence=0.7,
                    reasoning="Collecting personality traits for MBTI analysis"
                )
        
        elif context.conversation_stage == "interests":
            if context.collected_info.get("interests") and next_stage:
                return AgentDecision(
                    action_type="ask_question",
                    content="What does an ideal relationship look like to you?",
                    confidence=0.8,
                    reasoning="Interests collected, moving to relationship preferences",
                    next_stage="relationships"
                )
            else:
                return AgentDecision(
                    action_type="ask_question",
                    content="Outside of work, what gets you most excited? What are you passionate about?",
                    confidence=0.7,
                    reasoning="Need to understand user's interests and hobbies"
                )
        
        elif context.conversation_stage == "relationships":
            # Check if user is explicitly asking for more relationship/dating content
            recent_input = context.conversation_history[-1].get("user_response", "").lower() if context.conversation_history else ""
            wants_more_dating_content = any(keyword in recent_input for keyword in [
                "dating", "relationship", "partner", "love", "romantic", "感性", "两性", "loved ones"
            ])

            if wants_more_dating_content:
                # User wants to continue with relationship topics - don't end yet
                return AgentDecision(
                    action_type="ask_question",
                    content="I'd love to hear more about that! What qualities do you think are most important in a partner?",
                    confidence=0.8,
                    reasoning="User explicitly requested more relationship/dating content"
                )
            elif context.collected_info.get("relationship_preferences") and len(missing_info) <= 1:
                # Only end if we have enough info AND user isn't asking for more
                return AgentDecision(
                    action_type="summarize",
                    content="Thank you so much for sharing! I'll analyze our conversation and have your results ready in your profile shortly.",
                    confidence=0.9,
                    reasoning="All key information collected, time to close",
                    next_stage="completed"
                )
            else:
                return AgentDecision(
                    action_type="ask_question",
                    content="What does an ideal relationship look like to you?",
                    confidence=0.7,
                    reasoning="Understanding relationship preferences"
                )
        
        else:
            # Move to closing
            return AgentDecision(
                action_type="summarize",
                content="Thank you so much for sharing! I'll analyze our conversation and have your results ready in your profile shortly.",
                confidence=0.9,
                reasoning="All information collected, time to close",
                next_stage="completed"
            )
    
    def _create_urgent_question(self, info_type: str, context: ConversationContext) -> AgentDecision:
        """Create urgent question for missing critical info"""
        urgent_questions = {
            "basic_info": "Could you quickly tell me your name and city?",
            "profession": "What's your job or field of work?",
            "personality": "Are you more of an introvert or extrovert?",
            "interests": "What do you enjoy doing in your free time?",
            "relationships": "What are you looking for in a relationship?"
        }
        
        return AgentDecision(
            action_type="ask_question",
            content=urgent_questions.get(info_type, "Could you tell me a bit more about yourself?"),
            confidence=0.6,
            reasoning=f"Urgent collection of {info_type} due to time constraint"
        )
    
    def _create_fallback_decision(self, context: ConversationContext) -> AgentDecision:
        """Last resort fallback decision"""
        stage = context.conversation_stage
        fallback_question = self.emergency_fallbacks.get(stage, self.emergency_fallbacks["general"])

        return AgentDecision(
            action_type="ask_question",
            content=fallback_question,
            confidence=0.3,
            reasoning="Fallback decision due to system error"
        )
    
    @time_logger("save_agent_data")
    async def _save_agent_data(self, context: ConversationContext, decision: AgentDecision, user_input: str = ""):
        """Save agent decision and conversation context to database"""
        if not self.db_manager:
            logger.debug("No database manager available, skipping agent data save")
            return
        
        try:
            # 1. Save agent decision to agent_decisions table
            await self._save_agent_decision(context, decision, user_input)
            
            # 2. Save/update conversation context to agent_conversations table
            await self._save_conversation_context(context)
            
            # 3. Update agent goals based on information collected
            await self._update_agent_goals(context, decision)
            
            # 4. Save agent memory (context and user profile info)
            await self._save_agent_memory(context, user_input)
            
            # 5. Record performance metrics
            await self._record_agent_metrics(context, decision)
            
            # 6. Update agent strategies if needed
            await self._update_agent_strategies(context, decision)
            
            # 7. Save learning outcomes from this interaction
            await self._save_agent_learning(context, decision, user_input)
            
        except Exception as e:
            logger.error(f"Failed to save agent data: {e}")
    
    async def _save_agent_decision(self, context: ConversationContext, decision: AgentDecision, user_input: str):
        """Save individual agent decision to database"""
        try:
            # Determine decision type based on action
            decision_type_map = {
                "ask_question": "question_generate",
                "clarify": "strategy_change", 
                "summarize": "stage_advance",
                "end_call": "stage_advance"
            }
            
            decision_type = decision_type_map.get(decision.action_type, "question_generate")
            
            # Prepare decision data
            decision_data = {
                "action_type": decision.action_type,
                "content": decision.content,
                "user_input": user_input,
                "conversation_stage": context.conversation_stage,
                "next_stage": decision.next_stage,
                "emotional_state": context.emotional_state,
                "collected_info_count": len(context.collected_info),
                "timestamp": datetime.now().isoformat()
            }
            
            # Execute database insert
            if self.db_manager.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("""
                        INSERT INTO agent_decisions 
                        (call_sid, decision_type, decision_data, reasoning, confidence)
                        VALUES (:call_sid, :decision_type, :decision_data, :reasoning, :confidence)
                    """)
                    await session.execute(sql, {
                        "call_sid": context.call_sid,
                        "decision_type": decision_type,
                        "decision_data": json.dumps(decision_data),
                        "reasoning": decision.reasoning,
                        "confidence": decision.confidence
                    })
                    await session.commit()
                    
                logger.debug(f"Agent decision saved for call {context.call_sid}")
            
        except Exception as e:
            logger.error(f"Failed to save agent decision: {e}")
    
    async def _save_conversation_context(self, context: ConversationContext):
        """Save/update conversation context to database"""
        try:
            if not self.db_manager.is_connected:
                return
                
            # Calculate engagement and quality scores
            engagement_score = context.emotional_state.get("engagement", 0.5)
            comfort_score = context.emotional_state.get("comfort", 0.5)
            enthusiasm_score = context.emotional_state.get("enthusiasm", 0.5)
            
            # Calculate overall conversation quality
            conversation_quality = (engagement_score + comfort_score + enthusiasm_score) / 3.0
            
            # Prepare topic transitions (stages visited)
            topic_transitions = []
            for exchange in context.conversation_history:
                stage = exchange.get("stage", context.conversation_stage)
                if stage not in topic_transitions:
                    topic_transitions.append(stage)
            
            from backend.database.connection import get_async_db_session
            from sqlalchemy import text
            
            async with get_async_db_session() as session:
                # Use UPSERT (INSERT ... ON CONFLICT)
                sql = text("""
                    INSERT INTO agent_conversations 
                    (call_sid, user_id, conversation_stage, topic_transitions, emotional_state,
                     engagement_score, conversation_quality, total_exchanges, successful_responses, timeout_count)
                    VALUES (:call_sid, :user_id, :conversation_stage, :topic_transitions, :emotional_state,
                     :engagement_score, :conversation_quality, :total_exchanges, :successful_responses, :timeout_count)
                    ON CONFLICT (call_sid) DO UPDATE SET
                        conversation_stage = EXCLUDED.conversation_stage,
                        topic_transitions = EXCLUDED.topic_transitions,
                        emotional_state = EXCLUDED.emotional_state,
                        engagement_score = EXCLUDED.engagement_score,
                        conversation_quality = EXCLUDED.conversation_quality,
                        total_exchanges = EXCLUDED.total_exchanges,
                        successful_responses = EXCLUDED.successful_responses,
                        timeout_count = EXCLUDED.timeout_count,
                        updated_at = NOW()
                """)
                await session.execute(sql, {
                    "call_sid": context.call_sid,
                    "user_id": context.user_id,
                    "conversation_stage": context.conversation_stage,
                    "topic_transitions": json.dumps(topic_transitions),
                    "emotional_state": json.dumps(context.emotional_state),
                    "engagement_score": engagement_score,
                    "conversation_quality": conversation_quality,
                    "total_exchanges": len(context.conversation_history),
                    "successful_responses": context.successful_responses,
                    "timeout_count": context.timeout_count
                })
                await session.commit()
                
            logger.debug(f"Conversation context saved for call {context.call_sid}")
            
        except Exception as e:
            logger.error(f"Failed to save conversation context: {e}")
    
    async def _update_agent_goals(self, context: ConversationContext, decision: AgentDecision):
        """Update agent goals based on information collection progress"""
        try:
            if not self.db_manager.is_connected:
                return
            
            # Define information collection goals
            goals = [
                ("collect_basic_info", ["name", "city"]),
                ("collect_profession", ["profession"]),
                ("collect_personality", ["personality_traits"]),
                ("collect_interests", ["interests"]),
                ("collect_relationships", ["relationship_preferences"])
            ]
            
            from backend.database.connection import get_async_db_session
            from sqlalchemy import text
            
            async with get_async_db_session() as session:
                for goal_name, required_fields in goals:
                    # Calculate progress
                    collected = sum(1 for field in required_fields if context.collected_info.get(field))
                    progress = collected / len(required_fields)
                    
                    # Determine status
                    if progress >= 1.0:
                        status = "completed"
                    elif progress > 0:
                        status = "active"
                    else:
                        status = "active"
                    
                    # Upsert goal
                    sql = text("""
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES (:call_sid, :goal_name, :goal_status, :goal_progress, :goal_priority, :goal_data)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    """)
                    await session.execute(sql, {
                        "call_sid": context.call_sid,
                        "goal_name": goal_name,
                        "goal_status": status,
                        "goal_progress": progress,
                        "goal_priority": len(goals) - goals.index((goal_name, required_fields)),
                        "goal_data": json.dumps({"required_fields": required_fields, "collected": collected})
                    })
                await session.commit()
            
            logger.debug(f"Agent goals updated for call {context.call_sid}")
            
        except Exception as e:
            logger.error(f"Failed to update agent goals: {e}")
    
    async def _save_agent_memory(self, context: ConversationContext, user_input: str):
        """Save agent memory (short-term context, long-term user profile)"""
        try:
            if not self.db_manager.is_connected:
                return
            
            from backend.database.connection import get_async_db_session
            from sqlalchemy import text
            
            async with get_async_db_session() as session:
                # Short-term memory: Recent conversation context
                short_term_data = {
                    "recent_exchanges": context.conversation_history[-3:] if context.conversation_history else [],
                    "current_stage": context.conversation_stage,
                    "last_user_input": user_input,
                    "emotional_state": context.emotional_state
                }
                
                sql = text("""
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES (:call_sid, :memory_type, :memory_data)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                """)
                await session.execute(sql, {
                    "call_sid": context.call_sid,
                    "memory_type": "short_term",
                    "memory_data": json.dumps(short_term_data)
                })
                
                # Long-term memory: User profile information
                if context.collected_info:
                    long_term_data = {
                        "collected_info": context.collected_info,
                        "conversation_quality": (
                            context.emotional_state.get("engagement", 0.5) +
                            context.emotional_state.get("comfort", 0.5) +
                            context.emotional_state.get("enthusiasm", 0.5)
                        ) / 3.0,
                        "total_exchanges": len(context.conversation_history),
                        "successful_responses": context.successful_responses
                    }
                    
                    await session.execute(sql, {
                        "call_sid": context.call_sid,
                        "memory_type": "user_profile",
                        "memory_data": json.dumps(long_term_data)
                    })
                
                await session.commit()
            
            logger.debug(f"Agent memory saved for call {context.call_sid}")
            
        except Exception as e:
            logger.error(f"Failed to save agent memory: {e}")
    
    async def _record_agent_metrics(self, context: ConversationContext, decision: AgentDecision):
        """Record performance metrics for this decision"""
        try:
            if not self.db_manager.is_connected:
                return
            
            from backend.database.connection import get_async_db_session
            from sqlalchemy import text
            
            async with get_async_db_session() as session:
                sql = text("""
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES (:call_sid, :metric_name, :metric_value, :metric_unit)
                """)
                
                # Decision confidence metric
                await session.execute(sql, {
                    "call_sid": context.call_sid,
                    "metric_name": "decision_confidence",
                    "metric_value": decision.confidence,
                    "metric_unit": "score"
                })
                
                # Engagement score metric
                engagement = context.emotional_state.get("engagement", 0.5)
                await session.execute(sql, {
                    "call_sid": context.call_sid,
                    "metric_name": "user_engagement",
                    "metric_value": engagement,
                    "metric_unit": "score"
                })
                
                # Information collection rate
                info_count = len(context.collected_info)
                exchanges_count = max(1, len(context.conversation_history))
                collection_rate = info_count / exchanges_count
                
                await session.execute(sql, {
                    "call_sid": context.call_sid,
                    "metric_name": "info_collection_rate",
                    "metric_value": collection_rate,
                    "metric_unit": "rate"
                })
                
                await session.commit()
            
            logger.debug(f"Agent metrics recorded for call {context.call_sid}")
            
        except Exception as e:
            logger.error(f"Failed to record agent metrics: {e}")
    
    async def _update_agent_strategies(self, context: ConversationContext, decision: AgentDecision):
        """Update agent strategies based on conversation progress"""
        try:
            if not self.db_manager.is_connected:
                return
            
            # Determine active strategies based on context
            strategies = []
            
            # Engagement strategy based on emotional state
            engagement = context.emotional_state.get("engagement", 0.5)
            if engagement < 0.4:
                strategies.append(("re_engagement", {
                    "trigger_threshold": 0.4,
                    "tactics": ["passion_questions", "lighter_topics", "enthusiasm_boost"],
                    "current_engagement": engagement
                }))
            
            # Information collection strategy
            missing_info = self._get_missing_information(context)
            if missing_info:
                strategies.append(("info_collection", {
                    "missing_fields": missing_info,
                    "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"],
                    "current_stage": context.conversation_stage
                }))
            
            # Time management strategy
            if context.time_remaining < 120:  # Less than 2 minutes
                strategies.append(("time_management", {
                    "time_remaining": context.time_remaining,
                    "priority_info": missing_info[:2] if missing_info else [],
                    "fast_track": True
                }))
            
            from backend.database.connection import get_async_db_session
            from sqlalchemy import text
            
            async with get_async_db_session() as session:
                sql = text("""
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES (:call_sid, :strategy_name, :strategy_config, :is_active)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                """)
                
                for strategy_name, strategy_config in strategies:
                    await session.execute(sql, {
                        "call_sid": context.call_sid,
                        "strategy_name": strategy_name,
                        "strategy_config": json.dumps(strategy_config),
                        "is_active": True
                    })
                
                await session.commit()
            
            logger.debug(f"Agent strategies updated for call {context.call_sid}")
            
        except Exception as e:
            logger.error(f"Failed to update agent strategies: {e}")
    
    async def _save_agent_learning(self, context: ConversationContext, decision: AgentDecision, user_input: str):
        """Save learning outcomes from this interaction"""
        try:
            if not self.db_manager.is_connected:
                return
            
            learning_events = []
            
            # Learn from user engagement changes
            if len(context.conversation_history) > 1:
                # Compare current vs previous engagement (simplified)
                current_engagement = context.emotional_state.get("engagement", 0.5)
                
                if current_engagement > 0.7:
                    learning_events.append({
                        "type": "emotional_response",
                        "trigger": f"Question type: {decision.action_type}",
                        "input": {"question": decision.content, "user_response": user_input},
                        "outcome": {"engagement_increase": True, "new_engagement": current_engagement},
                        "effectiveness": current_engagement
                    })
                elif current_engagement < 0.3:
                    learning_events.append({
                        "type": "emotional_response", 
                        "trigger": f"Low engagement after: {decision.action_type}",
                        "input": {"question": decision.content, "user_response": user_input},
                        "outcome": {"engagement_decrease": True, "new_engagement": current_engagement},
                        "effectiveness": 1.0 - current_engagement
                    })
            
            # Learn from successful information extraction
            if user_input and context.collected_info:
                info_extracted = len([k for k, v in context.collected_info.items() if v])
                if info_extracted > 0:
                    learning_events.append({
                        "type": "question_success",
                        "trigger": f"Information extraction from: {decision.action_type}",
                        "input": {"question": decision.content, "user_response": user_input},
                        "outcome": {"info_extracted": info_extracted, "fields": list(context.collected_info.keys())},
                        "effectiveness": min(1.0, info_extracted / 3.0)  # Max 3 pieces of info per response
                    })
            
            # Learn from conversation flow
            if decision.next_stage and decision.next_stage != context.conversation_stage:
                learning_events.append({
                    "type": "conversation_outcome",
                    "trigger": f"Stage transition: {context.conversation_stage} -> {decision.next_stage}",
                    "input": {"current_context": context.conversation_stage, "decision": decision.action_type},
                    "outcome": {"successful_transition": True, "new_stage": decision.next_stage},
                    "effectiveness": decision.confidence
                })
            
            from backend.database.connection import get_async_db_session
            from sqlalchemy import text
            
            async with get_async_db_session() as session:
                sql = text("""
                    INSERT INTO agent_learning 
                    (call_sid, user_id, learning_type, trigger_event, input_data, learning_outcome, 
                     effectiveness_score, applied_immediately)
                    VALUES (:call_sid, :user_id, :learning_type, :trigger_event, :input_data, :learning_outcome, 
                     :effectiveness_score, :applied_immediately)
                """)
                
                for event in learning_events:
                    await session.execute(sql, {
                        "call_sid": context.call_sid,
                        "user_id": context.user_id,
                        "learning_type": event["type"],
                        "trigger_event": event["trigger"],
                        "input_data": json.dumps(event["input"]),
                        "learning_outcome": json.dumps(event["outcome"]),
                        "effectiveness_score": event["effectiveness"],
                        "applied_immediately": True
                    })
                
                await session.commit()
            
            logger.debug(f"Agent learning recorded for call {context.call_sid}: {len(learning_events)} events")
            
        except Exception as e:
            logger.error(f"Failed to save agent learning: {e}")
    
    def _get_missing_information(self, context: ConversationContext) -> List[str]:
        """Get list of missing required information"""
        missing = []
        info = context.collected_info
        
        if not info.get("name") or not info.get("city"):
            missing.append("basic_info")
        if not info.get("profession"):
            missing.append("profession")  
        if not info.get("personality_traits"):
            missing.append("personality")
        if not info.get("interests"):
            missing.append("interests")
        if not info.get("relationship_preferences"):
            missing.append("relationships")
            
        return missing
    
    @time_logger("extract_information_detailed")
    async def _extract_information(self, user_input: str, context: ConversationContext):
        """Extract information from user input using AI"""
        try:
            # Use AI to extract information
            conversation_context = {
                "conversation_stage": context.conversation_stage,
                "collected_info": context.collected_info
            }
            
            extracted_info = await self.ai_service.extract_information(user_input, conversation_context)
            
            # Merge extracted information with existing info
            for key, value in extracted_info.items():
                if value:  # Only add non-empty values
                    if key in ["personality_traits", "interests", "relationship_preferences"]:
                        # Handle arrays - merge without duplicates
                        if key not in context.collected_info:
                            context.collected_info[key] = []
                        
                        if isinstance(value, list):
                            for item in value:
                                if item not in context.collected_info[key]:
                                    context.collected_info[key].append(item)
                        else:
                            if value not in context.collected_info[key]:
                                context.collected_info[key].append(value)
                    else:
                        # Handle strings - only update if not already set or if new value is more specific
                        if key not in context.collected_info or not context.collected_info[key]:
                            context.collected_info[key] = value
                        elif len(str(value)) > len(str(context.collected_info[key])):
                            # New value is more detailed
                            context.collected_info[key] = value
            
            logger.info(f"AI extracted and merged info: {extracted_info}")
            
        except Exception as e:
            logger.error(f"AI information extraction failed: {e}")
            # Fallback to simple extraction
            await self._simple_extract_information(user_input, context)
    
    async def _simple_extract_information(self, user_input: str, context: ConversationContext):
        """ENHANCED: Fallback simple keyword-based extraction with better patterns"""
        input_lower = user_input.lower()

        # Extract name (enhanced patterns)
        name_patterns = [
            r"i'm\s+([a-zA-Z]+)",
            r"my name is\s+([a-zA-Z]+)",
            r"i am\s+([a-zA-Z]+)",
            r"call me\s+([a-zA-Z]+)",
            r"it's\s+([a-zA-Z]+)",
            r"this is\s+([a-zA-Z]+)"
        ]

        import re
        for pattern in name_patterns:
            match = re.search(pattern, input_lower)
            if match and len(match.group(1)) > 1:
                context.collected_info["name"] = match.group(1).title()
                break

        # Extract city (enhanced patterns)
        city_patterns = [
            r"from\s+([a-zA-Z\s]+?)(?:\s|$|,|\.|!|\?)",
            r"in\s+([a-zA-Z\s]+?)(?:\s|$|,|\.|!|\?)",
            r"live in\s+([a-zA-Z\s]+?)(?:\s|$|,|\.|!|\?)",
            r"based in\s+([a-zA-Z\s]+?)(?:\s|$|,|\.|!|\?)",
            r"located in\s+([a-zA-Z\s]+?)(?:\s|$|,|\.|!|\?)"
        ]

        for pattern in city_patterns:
            match = re.search(pattern, input_lower)
            if match:
                city = match.group(1).strip()
                if len(city) > 2 and city.replace(' ', '').isalpha():
                    context.collected_info["city"] = city.title()
                    break

        # Extract profession (enhanced patterns)
        profession_patterns = [
            r"i work as\s+(?:a\s+|an\s+)?([a-zA-Z\s]+?)(?:\s|$|,|\.|!|\?)",
            r"i'm\s+(?:a\s+|an\s+)?([a-zA-Z\s]+?)(?:\s|$|,|\.|!|\?)",
            r"i am\s+(?:a\s+|an\s+)?([a-zA-Z\s]+?)(?:\s|$|,|\.|!|\?)",
            r"my job is\s+([a-zA-Z\s]+?)(?:\s|$|,|\.|!|\?)",
            r"work in\s+([a-zA-Z\s]+?)(?:\s|$|,|\.|!|\?)"
        ]

        for pattern in profession_patterns:
            match = re.search(pattern, input_lower)
            if match:
                profession = match.group(1).strip()
                if len(profession) > 2:
                    context.collected_info["profession"] = profession.title()
                    break
    
    @time_logger("update_emotional_state_detailed")
    async def _update_emotional_state(self, user_input: str, context: ConversationContext):
        """Update user's emotional state using AI analysis"""
        try:
            # Use AI to analyze emotional state
            emotion_analysis = await self.ai_service.analyze_emotion(user_input, context.conversation_history)
            
            # Update emotional state with AI analysis
            for key, value in emotion_analysis.items():
                if key in ["engagement", "comfort", "enthusiasm"]:
                    # Smooth the transition - blend with previous state
                    previous_value = context.emotional_state.get(key, 0.5)
                    # Use weighted average: 70% new AI analysis, 30% previous state
                    blended_value = 0.7 * value + 0.3 * previous_value
                    context.emotional_state[key] = max(0.0, min(1.0, blended_value))
            
            logger.info(f"AI emotion analysis updated: {context.emotional_state}")
            
        except Exception as e:
            logger.error(f"AI emotion analysis failed: {e}")
            # Fallback to simple analysis
            await self._simple_emotional_analysis(user_input, context)
    
    async def _simple_emotional_analysis(self, user_input: str, context: ConversationContext):
        """Fallback simple emotional state estimation"""
        input_lower = user_input.lower()
        
        # Engagement indicators
        if len(user_input.split()) > 10:  # Longer responses indicate engagement
            context.emotional_state["engagement"] = min(1.0, context.emotional_state.get("engagement", 0.5) + 0.1)
        elif len(user_input.split()) < 3:  # Very short responses
            context.emotional_state["engagement"] = max(0.0, context.emotional_state.get("engagement", 0.5) - 0.1)
        
        # Enthusiasm indicators
        enthusiasm_words = ["love", "amazing", "excited", "great", "awesome", "fantastic"]
        if any(word in input_lower for word in enthusiasm_words):
            context.emotional_state["enthusiasm"] = min(1.0, context.emotional_state.get("enthusiasm", 0.5) + 0.2)
        
        # Discomfort indicators
        discomfort_words = ["um", "uh", "i guess", "maybe", "not sure", "don't know"]
        if any(phrase in input_lower for phrase in discomfort_words):
            context.emotional_state["comfort"] = max(0.0, context.emotional_state.get("comfort", 0.5) - 0.1)
    
    def should_change_stage(self, context: ConversationContext) -> Optional[str]:
        """FIXED: More flexible stage transition logic"""
        current_stage = context.conversation_stage

        # Check if current stage goals are met with flexible requirements
        if current_stage == "greeting":
            # Can move from greeting if we have ANY basic info OR sufficient attempts
            if (context.collected_info.get("name") or
                context.collected_info.get("profession") or
                len(context.collected_info) >= 2 or
                context.stage_attempts.get("greeting", 0) >= 3):
                return "professional"
        elif current_stage == "professional":
            if (context.collected_info.get("profession") or
                context.stage_attempts.get("professional", 0) >= 2):
                return "personality"
        elif current_stage == "personality":
            if (context.collected_info.get("personality_traits") or
                context.stage_attempts.get("personality", 0) >= 2):
                return "interests"
        elif current_stage == "interests":
            if (context.collected_info.get("interests") or
                context.stage_attempts.get("interests", 0) >= 2):
                return "relationships"
        elif current_stage == "relationships":
            if (context.collected_info.get("relationship_preferences") or
                context.stage_attempts.get("relationships", 0) >= 2):
                return "closing"

        # Time-based transitions
        if context.time_remaining < 30:
            return "closing"

        return None