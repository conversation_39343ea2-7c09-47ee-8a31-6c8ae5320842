"""
AgentFactory - Agent系统工厂类
统一初始化和配置所有Agent组件
"""

import logging
import asyncio
from typing import Optional, Dict, Any

from .config import CoreConfig, ServiceConfig, validate_all_configs
from .core.database import DatabaseManager
from .core.user_manager import UserManager
from .core.analysis_engine import AnalysisEngine
from .core.match_engine import MatchEngine
from .services.sms_service import SMSService
from .services.voice_service import VoiceService
from .services.linkedin_service import LinkedInService
from .agent_interface import AgentInterface

logger = logging.getLogger(__name__)

class AgentFactory:
    """Agent系统工厂类 - 统一初始化所有组件"""
    
    def __init__(self):
        """初始化工厂"""
        self.components = {}
        self.initialized = False
        
    async def initialize_system(self, redis_client=None) -> Dict[str, Any]:
        """初始化整个Agent系统"""
        try:
            logger.info("Starting Agent system initialization...")
            
            # 1. 验证配置
            if not validate_all_configs():
                raise ValueError("Configuration validation failed")
            
            # 2. 初始化数据库管理器
            db_manager = DatabaseManager(redis_client=redis_client)
            await db_manager.connect()
            self.components['db_manager'] = db_manager
            
            # 3. 初始化用户管理器
            user_manager = UserManager(redis_client=redis_client)
            user_manager.db_manager = db_manager  # 注入数据库管理器
            self.components['user_manager'] = user_manager
            
            # 4. 初始化分析引擎
            analysis_engine = AnalysisEngine()
            self.components['analysis_engine'] = analysis_engine
            
            # 5. 初始化匹配引擎
            match_engine = MatchEngine(
                user_manager=user_manager,
                analysis_engine=analysis_engine
            )
            self.components['match_engine'] = match_engine
            
            # 6. 初始化服务层
            sms_service = SMSService(
                redis_client=redis_client,
                db_manager=db_manager
            )
            self.components['sms_service'] = sms_service
            
            voice_service = VoiceService(
                user_manager=user_manager,
                analysis_engine=analysis_engine
            )
            self.components['voice_service'] = voice_service
            
            linkedin_service = LinkedInService(
                user_manager=user_manager,
                analysis_engine=analysis_engine
            )
            self.components['linkedin_service'] = linkedin_service
            
            # 7. 初始化主接口
            agent_interface = AgentInterface(
                user_manager=user_manager,
                sms_service=sms_service,
                voice_service=voice_service,
                linkedin_service=linkedin_service,
                analysis_engine=analysis_engine,
                match_engine=match_engine
            )
            self.components['agent_interface'] = agent_interface
            
            self.initialized = True
            
            logger.info("Agent system initialization completed successfully")
            
            return {
                "status": "success",
                "components": list(self.components.keys()),
                "config_info": {
                    "environment": CoreConfig.ENVIRONMENT,
                    "debug": CoreConfig.DEBUG,
                    "database_url": CoreConfig.DATABASE_URL[:50] + "...",
                    "redis_url": CoreConfig.REDIS_URL[:50] + "..."
                }
            }
            
        except Exception as e:
            logger.error(f"Agent system initialization failed: {e}")
            await self.cleanup()
            raise
    
    def get_component(self, component_name: str):
        """获取组件实例"""
        if not self.initialized:
            raise RuntimeError("Agent system not initialized")
        
        component = self.components.get(component_name)
        if not component:
            raise ValueError(f"Component '{component_name}' not found")
        
        return component
    
    def get_agent_interface(self) -> AgentInterface:
        """获取主接口"""
        return self.get_component('agent_interface')
    
    def get_user_manager(self) -> UserManager:
        """获取用户管理器"""
        return self.get_component('user_manager')
    
    def get_analysis_engine(self) -> AnalysisEngine:
        """获取分析引擎"""
        return self.get_component('analysis_engine')
    
    def get_match_engine(self) -> MatchEngine:
        """获取匹配引擎"""
        return self.get_component('match_engine')
    
    def get_sms_service(self) -> SMSService:
        """获取SMS服务"""
        return self.get_component('sms_service')
    
    def get_voice_service(self) -> VoiceService:
        """获取语音服务"""
        return self.get_component('voice_service')
    
    def get_linkedin_service(self) -> LinkedInService:
        """获取LinkedIn服务"""
        return self.get_component('linkedin_service')
    
    async def cleanup(self):
        """清理资源"""
        try:
            logger.info("Cleaning up Agent system...")
            
            # 断开数据库连接
            if 'db_manager' in self.components:
                await self.components['db_manager'].disconnect()
            
            # 清理其他资源
            self.components.clear()
            self.initialized = False
            
            logger.info("Agent system cleanup completed")
            
        except Exception as e:
            logger.error(f"Agent system cleanup error: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        if not self.initialized:
            return {
                "initialized": False,
                "components": [],
                "status": "not_initialized"
            }
        
        status = {
            "initialized": True,
            "components": {},
            "status": "healthy"
        }
        
        # 检查各组件状态
        for name, component in self.components.items():
            try:
                if hasattr(component, 'check_service_status'):
                    status["components"][name] = component.check_service_status()
                elif hasattr(component, 'check_connection'):
                    status["components"][name] = component.check_connection()
                else:
                    status["components"][name] = {"status": "active"}
            except Exception as e:
                status["components"][name] = {"status": "error", "error": str(e)}
        
        return status

# 全局工厂实例
agent_factory = AgentFactory()

async def initialize_agent_system(redis_client=None) -> AgentFactory:
    """初始化Agent系统的便捷函数"""
    await agent_factory.initialize_system(redis_client)
    return agent_factory

def get_agent_interface() -> AgentInterface:
    """获取Agent接口的便捷函数"""
    return agent_factory.get_agent_interface()

def get_system_status() -> Dict[str, Any]:
    """获取系统状态的便捷函数"""
    return agent_factory.get_system_status()

async def cleanup_agent_system():
    """清理Agent系统的便捷函数"""
    await agent_factory.cleanup()
