"""
DatabaseManager - 数据库管理器
提供异步数据库操作接口
"""

import logging
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from collections import defaultdict

from ..utils.data_models import User, SMSVerification
from ..config.core_config import CoreConfig

logger = logging.getLogger(__name__)

class DatabaseManager:
    """异步数据库管理器"""
    
    def __init__(self, redis_client=None):
        """初始化数据库管理器"""
        self.redis = redis_client
        self.is_connected = False
        
        # 内存存储备用 (MVP版本)
        self._memory_store = {
            'users': {},
            'sms_verifications': {}
        }
        
        logger.info("DatabaseManager initialized")
    
    async def connect(self) -> bool:
        """连接数据库"""
        try:
            # 检查异步数据库连接是否可用
            from backend.database.connection import test_async_database_connection
            
            result = await test_async_database_connection()
            if result["status"] == "connected":
                self.is_connected = True
                logger.info("Async database connection established")
                return True
            else:
                logger.error(f"Async database connection failed: {result.get('error', 'Unknown error')}")
                self.is_connected = False
                return False
                
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            self.is_connected = False
            return False
    
    async def disconnect(self) -> None:
        """断开数据库连接"""
        try:
            from backend.database.connection import close_connections
            await close_connections()
            self.is_connected = False
            logger.info("Database disconnected")
        except Exception as e:
            logger.error(f"Database disconnection failed: {e}")
    
    # ============ 用户操作 ============
    
    async def save_user(self, user: User) -> bool:
        """异步保存用户到数据库"""
        try:
            if self.is_connected:
                # 使用backend的异步数据库连接
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    # 使用 UPSERT 语句 (INSERT ... ON CONFLICT)
                    sql = text("""
                        INSERT INTO users (
                            id, phone_number, email, first_name, last_name, age, city, profession,
                            sms_verified, voice_call_completed, linkedin_verified,
                            verification_status, verification_level, status, created_at, updated_at, last_login
                        ) VALUES (
                            :id, :phone_number, :email, :first_name, :last_name, :age, :city, :profession,
                            :sms_verified, :voice_call_completed, :linkedin_verified,
                            :verification_status, :verification_level, :status, :created_at, :updated_at, :last_login
                        )
                        ON CONFLICT (id) DO UPDATE SET
                            phone_number = EXCLUDED.phone_number,
                            email = EXCLUDED.email,
                            first_name = EXCLUDED.first_name,
                            last_name = EXCLUDED.last_name,
                            age = EXCLUDED.age,
                            city = EXCLUDED.city,
                            profession = EXCLUDED.profession,
                            sms_verified = EXCLUDED.sms_verified,
                            voice_call_completed = EXCLUDED.voice_call_completed,
                            linkedin_verified = EXCLUDED.linkedin_verified,
                            verification_status = EXCLUDED.verification_status,
                            verification_level = EXCLUDED.verification_level,
                            status = EXCLUDED.status,
                            updated_at = EXCLUDED.updated_at,
                            last_login = EXCLUDED.last_login
                    """)

                    await session.execute(sql, {
                        'id': user.user_id,
                        'phone_number': user.phone_number,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'age': user.age,
                        'city': user.city,
                        'profession': user.profession,
                        'sms_verified': user.sms_verified,
                        'voice_call_completed': user.voice_call_completed,
                        'linkedin_verified': user.linkedin_verified,
                        'verification_status': user.verification_status.value,
                        'verification_level': user.verification_level.value,
                        'status': user.status.value,
                        'created_at': user.created_at,
                        'updated_at': user.updated_at,
                        'last_login': user.last_login
                    })

                    await session.commit()
                    logger.debug(f"User saved to PostgreSQL (async): {user.user_id}")

            else:
                # 内存存储备用
                self._memory_store['users'][user.user_id] = {
                    'user_id': user.user_id,
                    'phone_number': user.phone_number,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'age': user.age,
                    'city': user.city,
                    'profession': user.profession,
                    'status': user.status.value,
                    'sms_verified': user.sms_verified,
                    'voice_call_completed': user.voice_call_completed,
                    'linkedin_verified': user.linkedin_verified,
                    'verification_status': user.verification_status.value,
                    'verification_level': user.verification_level.value,
                    'created_at': user.created_at.isoformat(),
                    'updated_at': user.updated_at.isoformat(),
                    'last_login': user.last_login.isoformat()
                }
                logger.debug(f"User saved to memory: {user.user_id}")

            return True

        except Exception as e:
            logger.error(f"Failed to save user {user.user_id}: {e}")
            return False
    
    async def load_user(self, user_id: str) -> Optional[User]:
        """异步从数据库加载用户"""
        try:
            if self.is_connected:
                # 使用backend的异步数据库连接
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("SELECT * FROM users WHERE id = :user_id")
                    result = await session.execute(sql, {'user_id': user_id})
                    row = result.fetchone()

                    if row:
                        # 将数据库行转换为User对象
                        from ..utils.data_models import UserStatus, VerificationStatus, VerificationLevel
                        
                        user = User(
                            user_id=str(row.id),
                            phone_number=row.phone_number,
                            email=row.email,
                            first_name=row.first_name,
                            last_name=row.last_name,
                            age=row.age,
                            city=row.city,
                            profession=row.profession,
                            status=UserStatus(row.status),
                            sms_verified=row.sms_verified,
                            voice_call_completed=row.voice_call_completed,
                            linkedin_verified=row.linkedin_verified,
                            verification_status=VerificationStatus(row.verification_status),
                            verification_level=VerificationLevel(row.verification_level),
                            created_at=row.created_at,
                            updated_at=row.updated_at,
                            last_login=row.last_login
                        )
                        
                        logger.debug(f"User loaded from PostgreSQL (async): {user_id}")
                        return user

            # 内存存储备用
            user_data = self._memory_store['users'].get(user_id)
            if user_data:
                from ..utils.data_models import UserStatus, VerificationStatus, VerificationLevel
                
                user = User(
                    user_id=user_data['user_id'],
                    phone_number=user_data['phone_number'],
                    email=user_data.get('email'),
                    first_name=user_data.get('first_name'),
                    last_name=user_data.get('last_name'),
                    age=user_data.get('age'),
                    city=user_data.get('city'),
                    profession=user_data.get('profession'),
                    status=UserStatus(user_data['status']),
                    sms_verified=user_data['sms_verified'],
                    voice_call_completed=user_data['voice_call_completed'],
                    linkedin_verified=user_data['linkedin_verified'],
                    verification_status=VerificationStatus(user_data['verification_status']),
                    verification_level=VerificationLevel(user_data['verification_level']),
                    created_at=datetime.fromisoformat(user_data['created_at']),
                    updated_at=datetime.fromisoformat(user_data['updated_at']),
                    last_login=datetime.fromisoformat(user_data['last_login'])
                )
                
                logger.debug(f"User loaded from memory: {user_id}")
                return user

            return None

        except Exception as e:
            logger.error(f"Failed to load user {user_id}: {e}")
            return None
    
    async def load_user_by_phone(self, phone_number: str) -> Optional[User]:
        """异步通过手机号加载用户"""
        try:
            if self.is_connected:
                # 使用backend的异步数据库连接
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("SELECT * FROM users WHERE phone_number = :phone_number")
                    result = await session.execute(sql, {'phone_number': phone_number})
                    row = result.fetchone()

                    if row:
                        # 将数据库行转换为User对象
                        from ..utils.data_models import UserStatus, VerificationStatus, VerificationLevel
                        
                        user = User(
                            user_id=str(row.id),
                            phone_number=row.phone_number,
                            email=row.email,
                            first_name=row.first_name,
                            last_name=row.last_name,
                            age=row.age,
                            city=row.city,
                            profession=row.profession,
                            status=UserStatus(row.status),
                            sms_verified=row.sms_verified,
                            voice_call_completed=row.voice_call_completed,
                            linkedin_verified=row.linkedin_verified,
                            verification_status=VerificationStatus(row.verification_status),
                            verification_level=VerificationLevel(row.verification_level),
                            created_at=row.created_at,
                            updated_at=row.updated_at,
                            last_login=row.last_login
                        )
                        
                        logger.debug(f"User loaded from PostgreSQL by phone (async): {phone_number}")
                        return user

            # 内存存储备用
            for user_data in self._memory_store['users'].values():
                if user_data.get('phone_number') == phone_number:
                    return await self.load_user(user_data['user_id'])

            return None

        except Exception as e:
            logger.error(f"Failed to load user by phone {phone_number}: {e}")
            return None
    
    # ============ SMS验证操作 ============
    
    async def save_sms_verification(self, verification: SMSVerification) -> bool:
        """异步保存SMS验证记录"""
        try:
            if self.is_connected:
                # 使用backend的异步数据库连接
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text

                async with get_async_db_session() as session:
                    sql = text("""
                        INSERT INTO sms_verifications (
                            id, phone_number, verification_code, expires_at, verified_at, attempts, created_at
                        ) VALUES (
                            :id, :phone_number, :verification_code, :expires_at, :verified_at, :attempts, :created_at
                        )
                        ON CONFLICT (id) DO UPDATE SET
                            verification_code = EXCLUDED.verification_code,
                            expires_at = EXCLUDED.expires_at,
                            verified_at = EXCLUDED.verified_at,
                            attempts = EXCLUDED.attempts
                    """)

                    await session.execute(sql, {
                        'id': verification.verification_id,
                        'phone_number': verification.phone_number,
                        'verification_code': verification.verification_code,
                        'expires_at': verification.expires_at,
                        'verified_at': verification.verified_at,
                        'attempts': verification.attempts,
                        'created_at': verification.created_at
                    })

                    await session.commit()
                    logger.debug(f"SMS verification saved to PostgreSQL (async): {verification.phone_number}")

            else:
                # 内存存储备用
                self._memory_store['sms_verifications'][verification.phone_number] = {
                    'verification_id': verification.verification_id,
                    'phone_number': verification.phone_number,
                    'verification_code': verification.verification_code,
                    'expires_at': verification.expires_at.isoformat(),
                    'verified_at': verification.verified_at.isoformat() if verification.verified_at else None,
                    'attempts': verification.attempts,
                    'created_at': verification.created_at.isoformat()
                }
                logger.debug(f"SMS verification saved to memory: {verification.phone_number}")

            return True

        except Exception as e:
            logger.error(f"Failed to save SMS verification: {e}")
            return False
    
    async def load_sms_verification(self, phone_number: str) -> Optional[SMSVerification]:
        """异步加载SMS验证记录"""
        try:
            if self.is_connected:
                # 使用backend的异步数据库连接
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    # 查询最新的验证记录
                    sql = text("""
                        SELECT * FROM sms_verifications 
                        WHERE phone_number = :phone_number 
                        ORDER BY created_at DESC 
                        LIMIT 1
                    """)
                    result = await session.execute(sql, {'phone_number': phone_number})
                    row = result.fetchone()
                    
                    if row:
                        # 确保时间是无时区的，与数据模型保持一致
                        expires_at = row.expires_at
                        if expires_at.tzinfo is not None:
                            expires_at = expires_at.replace(tzinfo=None)
                        
                        verified_at = row.verified_at
                        if verified_at and verified_at.tzinfo is not None:
                            verified_at = verified_at.replace(tzinfo=None)
                            
                        created_at = row.created_at
                        if created_at.tzinfo is not None:
                            created_at = created_at.replace(tzinfo=None)
                        
                        verification = SMSVerification(
                            verification_id=str(row.id),
                            phone_number=row.phone_number,
                            verification_code=row.verification_code,
                            expires_at=expires_at,
                            verified_at=verified_at,
                            attempts=row.attempts,
                            created_at=created_at
                        )
                        
                        logger.info(f"SMS verification loaded - Phone: {phone_number}")
                        logger.info(f"  Code: {verification.verification_code}")
                        logger.info(f"  Expires at: {verification.expires_at} (tzinfo: {verification.expires_at.tzinfo})")
                        logger.info(f"  Attempts: {verification.attempts}")
                        
                        return verification
            
            # 内存存储备用
            data = self._memory_store['sms_verifications'].get(phone_number)
            if data:
                verification = SMSVerification(
                    verification_id=data['verification_id'],
                    phone_number=data['phone_number'],
                    verification_code=data['verification_code'],
                    expires_at=datetime.fromisoformat(data['expires_at']),
                    verified_at=datetime.fromisoformat(data['verified_at']) if data['verified_at'] else None,
                    attempts=data['attempts'],
                    created_at=datetime.fromisoformat(data['created_at'])
                )
                
                logger.debug(f"SMS verification loaded from memory: {phone_number}")
                return verification
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to load SMS verification: {e}")
            return None
    
    # ============ 语音会话操作 ============
    
    # ============ Session持久化操作 ============

    async def save_active_session(self, call_sid: str, user_id: str, session_data: dict) -> bool:
        """保存活跃的语音会话Session"""
        try:
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text

                async with get_async_db_session() as session:
                    sql = text("""
                        INSERT INTO voice_sessions (
                            id, user_id, twilio_call_sid, session_data, session_status,
                            last_activity, from_number, current_stage, questions_asked,
                            responses_received, created_at
                        ) VALUES (
                            uuid_generate_v4(), :user_id, :twilio_call_sid, :session_data,
                            'active', NOW(), :from_number, :current_stage, :questions_asked,
                            :responses_received, NOW()
                        )
                        ON CONFLICT (twilio_call_sid) DO UPDATE SET
                            session_data = EXCLUDED.session_data,
                            last_activity = NOW(),
                            current_stage = EXCLUDED.current_stage,
                            questions_asked = EXCLUDED.questions_asked,
                            responses_received = EXCLUDED.responses_received
                    """)

                    await session.execute(sql, {
                        'user_id': user_id,
                        'twilio_call_sid': call_sid,
                        'session_data': json.dumps(session_data),
                        'from_number': session_data.get('from_number', ''),
                        'current_stage': session_data.get('current_stage', 'greeting'),
                        'questions_asked': session_data.get('questions_asked', 0),
                        'responses_received': session_data.get('responses_received', 0)
                    })

                    await session.commit()
                    logger.debug(f"Active session saved to PostgreSQL: {call_sid}")
            else:
                # 内存存储备用
                if 'active_sessions' not in self._memory_store:
                    self._memory_store['active_sessions'] = {}

                self._memory_store['active_sessions'][call_sid] = {
                    'call_sid': call_sid,
                    'user_id': user_id,
                    'session_data': session_data,
                    'session_status': 'active',
                    'last_activity': datetime.now().isoformat(),
                    'created_at': datetime.now().isoformat()
                }
                logger.debug(f"Active session saved to memory: {call_sid}")

            return True

        except Exception as e:
            logger.error(f"Failed to save active session: {e}")
            return False

    async def load_active_session(self, call_sid: str) -> Optional[dict]:
        """加载活跃的语音会话Session"""
        try:
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text

                async with get_async_db_session() as session:
                    sql = text("""
                        SELECT session_data, user_id, from_number, current_stage,
                               questions_asked, responses_received, last_activity
                        FROM voice_sessions
                        WHERE twilio_call_sid = :call_sid
                        AND session_status = 'active'
                        AND last_activity > NOW() - INTERVAL '2 hours'
                    """)

                    result = await session.execute(sql, {'call_sid': call_sid})
                    row = result.fetchone()

                    if row:
                        session_data = json.loads(row.session_data) if row.session_data else {}
                        logger.debug(f"Active session loaded from PostgreSQL: {call_sid}")
                        return session_data
            else:
                # 内存存储备用
                session_record = self._memory_store.get('active_sessions', {}).get(call_sid)
                if session_record:
                    logger.debug(f"Active session loaded from memory: {call_sid}")
                    return session_record.get('session_data', {})

            return None

        except Exception as e:
            logger.error(f"Failed to load active session: {e}")
            return None

    async def update_session_activity(self, call_sid: str, session_data: dict) -> bool:
        """更新Session活动时间和数据"""
        try:
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text

                async with get_async_db_session() as session:
                    sql = text("""
                        UPDATE voice_sessions SET
                            session_data = :session_data,
                            last_activity = NOW(),
                            current_stage = :current_stage,
                            questions_asked = :questions_asked,
                            responses_received = :responses_received
                        WHERE twilio_call_sid = :call_sid
                        AND session_status = 'active'
                    """)

                    await session.execute(sql, {
                        'call_sid': call_sid,
                        'session_data': json.dumps(session_data),
                        'current_stage': session_data.get('current_stage', 'greeting'),
                        'questions_asked': session_data.get('questions_asked', 0),
                        'responses_received': session_data.get('responses_received', 0)
                    })

                    await session.commit()
                    logger.debug(f"Session activity updated: {call_sid}")
            else:
                # 内存存储备用
                if call_sid in self._memory_store.get('active_sessions', {}):
                    self._memory_store['active_sessions'][call_sid]['session_data'] = session_data
                    self._memory_store['active_sessions'][call_sid]['last_activity'] = datetime.now().isoformat()

            return True

        except Exception as e:
            logger.error(f"Failed to update session activity: {e}")
            return False

    async def complete_voice_session(self, call_sid: str, call_duration: int, analysis_data: dict) -> bool:
        """标记语音会话完成并保存最终数据"""
        try:
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text

                async with get_async_db_session() as session:
                    sql = text("""
                        UPDATE voice_sessions SET
                            session_status = 'completed',
                            call_duration = :call_duration,
                            analysis_data = :analysis_data,
                            completed_at = NOW()
                        WHERE twilio_call_sid = :call_sid
                    """)

                    await session.execute(sql, {
                        'call_sid': call_sid,
                        'call_duration': call_duration,
                        'analysis_data': json.dumps(analysis_data)
                    })

                    await session.commit()
                    logger.debug(f"Voice session completed: {call_sid}")
            else:
                # 内存存储备用
                if call_sid in self._memory_store.get('active_sessions', {}):
                    session_record = self._memory_store['active_sessions'][call_sid]
                    session_record['session_status'] = 'completed'
                    session_record['call_duration'] = call_duration
                    session_record['analysis_data'] = analysis_data
                    session_record['completed_at'] = datetime.now().isoformat()

            return True

        except Exception as e:
            logger.error(f"Failed to complete voice session: {e}")
            return False

    async def save_voice_session(self, user_id: str, call_sid: str, call_duration: int,
                                analysis_data: dict) -> bool:
        """保存语音会话记录（兼容性方法）"""
        return await self.complete_voice_session(call_sid, call_duration, analysis_data)
    
    async def save_user_profile(self, user_id: str, voice_analysis: dict, final_profile: dict,
                               confidence_scores: dict, mbti_type: str, verification_level: str) -> bool:
        """保存用户画像分析结果"""
        try:
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("""
                        INSERT INTO user_profiles (
                            user_id, voice_analysis, final_profile, confidence_scores, 
                            mbti_type, verification_level, last_updated, created_at
                        ) VALUES (
                            :user_id, :voice_analysis, :final_profile, :confidence_scores,
                            :mbti_type, :verification_level, NOW(), NOW()
                        )
                        ON CONFLICT (user_id) DO UPDATE SET
                            voice_analysis = EXCLUDED.voice_analysis,
                            final_profile = EXCLUDED.final_profile,
                            confidence_scores = EXCLUDED.confidence_scores,
                            mbti_type = EXCLUDED.mbti_type,
                            verification_level = EXCLUDED.verification_level,
                            last_updated = NOW()
                    """)
                    
                    await session.execute(sql, {
                        'user_id': user_id,
                        'voice_analysis': json.dumps(voice_analysis),
                        'final_profile': json.dumps(final_profile),
                        'confidence_scores': json.dumps(confidence_scores),
                        'mbti_type': mbti_type,
                        'verification_level': verification_level
                    })
                    
                    await session.commit()
                    logger.debug(f"User profile saved to PostgreSQL: {user_id}")
            else:
                # 内存存储备用
                if 'user_profiles' not in self._memory_store:
                    self._memory_store['user_profiles'] = {}
                
                self._memory_store['user_profiles'][user_id] = {
                    'user_id': user_id,
                    'voice_analysis': voice_analysis,
                    'final_profile': final_profile,
                    'confidence_scores': confidence_scores,
                    'mbti_type': mbti_type,
                    'verification_level': verification_level,
                    'last_updated': datetime.now().isoformat(),
                    'created_at': datetime.now().isoformat()
                }
                logger.debug(f"User profile saved to memory: {user_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to save user profile: {e}")
            return False
    
    async def save_profile_card(self, user_id: str, card_type: str, title: str, 
                               description: str, confidence: float, tags: list, evidence: list) -> str:
        """保存用户画像卡片"""
        try:
            card_id = str(uuid.uuid4())
            
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("""
                        INSERT INTO profile_cards (
                            id, user_id, card_type, title, description, confidence, 
                            tags, evidence, created_at, updated_at
                        ) VALUES (
                            :id, :user_id, :card_type, :title, :description, :confidence,
                            :tags, :evidence, NOW(), NOW()
                        )
                    """)
                    
                    await session.execute(sql, {
                        'id': card_id,
                        'user_id': user_id,
                        'card_type': card_type,
                        'title': title,
                        'description': description,
                        'confidence': confidence,
                        'tags': tags,
                        'evidence': evidence
                    })
                    
                    await session.commit()
                    logger.debug(f"Profile card saved to PostgreSQL: {card_id}")
            else:
                # 内存存储备用
                if 'profile_cards' not in self._memory_store:
                    self._memory_store['profile_cards'] = {}
                
                self._memory_store['profile_cards'][card_id] = {
                    'id': card_id,
                    'user_id': user_id,
                    'card_type': card_type,
                    'title': title,
                    'description': description,
                    'confidence': confidence,
                    'tags': tags,
                    'evidence': evidence,
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                }
                logger.debug(f"Profile card saved to memory: {card_id}")
            
            return card_id
            
        except Exception as e:
            logger.error(f"Failed to save profile card: {e}")
            return ""
    
    # ============ 对话消息操作 ============
    
    async def save_message(self, user_id: str, role: str, content: str, 
                          message_type: str = "text", is_important: bool = False) -> str:
        """保存对话消息"""
        try:
            message_id = str(uuid.uuid4())
            
            if self.is_connected:
                # TODO: 实现真实的数据库保存
                # 这里应该保存到 user_chat_messages 表或类似的消息表
                pass
            
            # 内存存储备用 - 简单实现
            if 'messages' not in self._memory_store:
                self._memory_store['messages'] = {}
            
            if user_id not in self._memory_store['messages']:
                self._memory_store['messages'][user_id] = []
            
            message_data = {
                'message_id': message_id,
                'user_id': user_id,
                'role': role,
                'content': content,
                'message_type': message_type,
                'is_important': is_important,
                'timestamp': datetime.now().isoformat()
            }
            
            self._memory_store['messages'][user_id].append(message_data)
            
            logger.debug(f"Message saved for user {user_id}: {message_id}")
            return message_id
            
        except Exception as e:
            logger.error(f"Failed to save message: {e}")
            return ""
    
    async def load_recent_messages(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """加载最近的消息"""
        try:
            if self.is_connected:
                # TODO: 实现真实的数据库查询
                pass
            
            # 内存存储备用
            messages = self._memory_store.get('messages', {}).get(user_id, [])
            return messages[-limit:] if len(messages) > limit else messages
            
        except Exception as e:
            logger.error(f"Failed to load recent messages: {e}")
            return []
    
    # ============ 工具方法 ============
    
    def get_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        return {
            "connected": self.is_connected,
            "mode": "memory_simulation" if not self.is_connected else "database",
            "memory_store_counts": {
                key: len(value) for key, value in self._memory_store.items()
            }
        }
