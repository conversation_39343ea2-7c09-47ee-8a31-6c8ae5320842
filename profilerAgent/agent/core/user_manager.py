"""
UserManager - 统一用户状态管理
MVP版本：简单事件系统 + 权限验证
"""

from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from collections import defaultdict
import logging
import json
import uuid
import asyncio

from ..utils import (
    User, UserStatus, VerificationStatus, UserPermission, ConversationStage,
    VerificationLevel, EventData, EventType, ConversationContext,
    USER_PERMISSIONS, VERIFICATION_PERMISSIONS
)
from .database import DatabaseManager

logger = logging.getLogger(__name__)

class ConversationManager:
    """对话管理器 - 分层存储 + 智能加载"""

    def __init__(self, db_manager=None, redis_client=None):
        self.db_manager = db_manager
        self.redis = redis_client

        # 加载策略配置
        self.RECENT_MESSAGE_LIMIT = 10
        self.IMPORTANT_MESSAGE_CACHE_TTL = 3600  # 1小时
        self.CONTEXT_CACHE_TTL = 1800  # 30分钟

        logger.info("ConversationManager initialized")

    def add_message(self, user_id: str, role: str, content: str,
                   message_type: str = "text", is_important: bool = False) -> str:
        """添加消息到对话历史"""
        message_id = str(uuid.uuid4())
        timestamp = datetime.now()

        message_data = {
            'message_id': message_id,
            'user_id': user_id,
            'role': role,
            'content': content,
            'message_type': message_type,
            'is_important': is_important,
            'timestamp': timestamp.isoformat()
        }

        # 1. 保存到数据库 (完整存储)
        if self.db_manager:
            asyncio.run(self.db_manager.save_message(
                user_id, role, content, message_type, is_important
            ))

        # 2. 更新Redis缓存 (最近消息)
        self._update_recent_messages_cache(user_id, message_data)

        # 3. 如果是重要消息，单独缓存
        if is_important:
            self._cache_important_message(user_id, message_data)

        # 4. 清除相关的上下文缓存
        self._invalidate_context_cache(user_id)

        logger.debug(f"Added message for user {user_id}: {role} - {content[:50]}...")
        return message_id

    def get_conversation_context(self, user_id: str, context_type: str = "recent") -> Dict[str, Any]:
        """获取对话上下文"""
        cache_key = f"context:{user_id}:{context_type}"

        # 1. 尝试从Redis获取
        if self.redis:
            cached_context = self.redis.get(cache_key)
            if cached_context:
                logger.debug(f"Context cache hit for {user_id}:{context_type}")
                return json.loads(cached_context)

        # 2. 从数据库构建上下文
        if context_type == "recent":
            context = self._build_recent_context(user_id)
        elif context_type == "analysis":
            context = self._build_analysis_context(user_id)
        elif context_type == "important":
            context = self._build_important_context(user_id)
        else:
            context = self._build_recent_context(user_id)

        # 3. 缓存结果
        if self.redis:
            self.redis.setex(cache_key, self.CONTEXT_CACHE_TTL, json.dumps(context))

        logger.debug(f"Built context for {user_id}:{context_type} - {len(context.get('messages', []))} messages")
        return context

    def _build_recent_context(self, user_id: str) -> Dict[str, Any]:
        """构建最近消息上下文"""
        messages = self._load_recent_messages_from_cache_or_db(user_id, self.RECENT_MESSAGE_LIMIT)

        return {
            'messages': messages,
            'total_count': len(messages),
            'context_type': 'recent',
            'has_full_history': False
        }

    def _build_analysis_context(self, user_id: str) -> Dict[str, Any]:
        """构建AI分析用的上下文"""
        # 最近消息 + 重要消息，去重
        recent = self._load_recent_messages_from_cache_or_db(user_id, self.RECENT_MESSAGE_LIMIT)
        important = self._load_important_messages_from_cache_or_db(user_id)

        # 合并去重
        seen_content = set()
        context_messages = []

        # 先添加重要消息
        for msg in important:
            if msg['content'] not in seen_content:
                context_messages.append(msg)
                seen_content.add(msg['content'])

        # 再添加最近消息
        for msg in recent:
            if msg['content'] not in seen_content:
                context_messages.append(msg)
                seen_content.add(msg['content'])

        # 按时间排序
        context_messages.sort(key=lambda x: x['timestamp'])

        return {
            'messages': context_messages,
            'total_count': len(context_messages),
            'context_type': 'analysis',
            'has_full_history': False,
            'recent_count': len(recent),
            'important_count': len(important)
        }

    def _build_important_context(self, user_id: str) -> Dict[str, Any]:
        """构建重要消息上下文"""
        messages = self._load_important_messages_from_cache_or_db(user_id)

        return {
            'messages': messages,
            'total_count': len(messages),
            'context_type': 'important',
            'has_full_history': False
        }

    # ============ 缓存操作 ============

    def _update_recent_messages_cache(self, user_id: str, message_data: Dict[str, Any]):
        """更新最近消息缓存"""
        if not self.redis:
            return

        cache_key = f"recent_messages:{user_id}"

        # 获取现有缓存
        cached_messages = self.redis.get(cache_key)
        if cached_messages:
            messages = json.loads(cached_messages)
        else:
            messages = []

        # 添加新消息
        messages.append(message_data)

        # 保持最近N条消息
        if len(messages) > self.RECENT_MESSAGE_LIMIT:
            messages = messages[-self.RECENT_MESSAGE_LIMIT:]

        # 更新缓存
        self.redis.setex(cache_key, self.IMPORTANT_MESSAGE_CACHE_TTL, json.dumps(messages))

    def _cache_important_message(self, user_id: str, message_data: Dict[str, Any]):
        """缓存重要消息"""
        if not self.redis:
            return

        cache_key = f"important_messages:{user_id}"

        # 获取现有重要消息
        cached_messages = self.redis.get(cache_key)
        if cached_messages:
            messages = json.loads(cached_messages)
        else:
            messages = []

        # 添加新的重要消息
        messages.append(message_data)

        # 更新缓存
        self.redis.setex(cache_key, self.IMPORTANT_MESSAGE_CACHE_TTL, json.dumps(messages))

    def _invalidate_context_cache(self, user_id: str):
        """清除上下文缓存"""
        if not self.redis:
            return

        # 清除所有相关的上下文缓存
        cache_patterns = [
            f"context:{user_id}:recent",
            f"context:{user_id}:analysis",
            f"context:{user_id}:important"
        ]

        for pattern in cache_patterns:
            self.redis.delete(pattern)

    # ============ 数据加载 ============

    def _load_recent_messages_from_cache_or_db(self, user_id: str, limit: int) -> List[Dict[str, Any]]:
        """从缓存或数据库加载最近消息"""
        # 1. 尝试从Redis缓存加载
        if self.redis:
            cache_key = f"recent_messages:{user_id}"
            cached_messages = self.redis.get(cache_key)
            if cached_messages:
                messages = json.loads(cached_messages)
                return messages[-limit:] if len(messages) > limit else messages

        # 2. 从数据库加载
        if self.db_manager:
            try:
                return asyncio.run(self.db_manager.load_recent_messages(user_id, limit))
            except Exception as e:
                logger.error(f"Failed to load messages from database: {e}")

        # 3. 返回空列表
        return []

    def _load_important_messages_from_cache_or_db(self, user_id: str) -> List[Dict[str, Any]]:
        """从缓存或数据库加载重要消息"""
        # 1. 尝试从Redis缓存加载
        if self.redis:
            cache_key = f"important_messages:{user_id}"
            cached_messages = self.redis.get(cache_key)
            if cached_messages:
                return json.loads(cached_messages)

        # 2. 从数据库加载
        if self.db:
            return self._load_important_messages_from_db(user_id)

        # 3. 返回空列表
        return []

    # ============ 数据库操作 (占位符) ============

    def _save_message_to_db(self, message_data: Dict[str, Any]):
        """保存消息到数据库"""
        if not self.db:
            return

        # TODO: 实现数据库保存逻辑
        logger.debug(f"Saving message to DB: {message_data['message_id']}")

    def _load_recent_messages_from_db(self, user_id: str, limit: int) -> List[Dict[str, Any]]:
        """从数据库加载最近消息"""
        if not self.db:
            return []

        # TODO: 实现数据库查询逻辑
        logger.debug(f"Loading recent messages from DB: {user_id}, limit: {limit}")
        return []

    def _load_important_messages_from_db(self, user_id: str) -> List[Dict[str, Any]]:
        """从数据库加载重要消息"""
        if not self.db:
            return []

        # TODO: 实现数据库查询逻辑
        logger.debug(f"Loading important messages from DB: {user_id}")
        return []

    def get_conversation_stats(self, user_id: str) -> Dict[str, Any]:
        """获取对话统计信息"""
        recent_context = self.get_conversation_context(user_id, "recent")
        important_context = self.get_conversation_context(user_id, "important")

        return {
            'user_id': user_id,
            'recent_message_count': len(recent_context.get('messages', [])),
            'important_message_count': len(important_context.get('messages', [])),
            'has_cached_recent': bool(self.redis and self.redis.exists(f"recent_messages:{user_id}")),
            'has_cached_important': bool(self.redis and self.redis.exists(f"important_messages:{user_id}"))
        }

class SimpleEventBus:
    """简单事件总线 - MVP版本"""

    def __init__(self):
        self.handlers: Dict[str, List[Callable]] = defaultdict(list)

    def subscribe(self, event_type: str, handler: Callable):
        """订阅事件"""
        self.handlers[event_type].append(handler)
        logger.debug(f"Subscribed handler for event: {event_type}")

    def publish(self, event_type: str, data: Dict[str, Any]):
        """发布事件"""
        handlers = self.handlers.get(event_type, [])
        logger.info(f"Publishing event {event_type} to {len(handlers)} handlers")

        for handler in handlers:
            try:
                handler(data)
            except Exception as e:
                logger.error(f"Event handler failed for {event_type}: {e}")
                # 继续执行其他处理器，不中断主流程

class UserManager:
    """用户状态管理器 - MVP版本"""

    def __init__(self, db_connection=None, redis_client=None):
        """初始化用户管理器"""
        self.db = db_connection
        self.redis = redis_client
        self.event_bus = SimpleEventBus()

        # 数据库管理器
        self.db_manager = DatabaseManager(redis_client=redis_client)

        # 对话管理器
        self.conversation_manager = ConversationManager(self.db_manager, redis_client)

        # 内存缓存 (L1缓存)
        self._user_cache: Dict[str, User] = {}

        # 缓存配置
        self.memory_cache_ttl = timedelta(minutes=5)  # 内存缓存短TTL
        self.redis_cache_ttl = timedelta(hours=24)    # Redis缓存长TTL
        self._cache_timestamps: Dict[str, datetime] = {}

        logger.info("UserManager initialized with conversation management")

    # ============ 事件系统 ============

    def subscribe_to_event(self, event_type: str, handler: Callable):
        """订阅事件"""
        self.event_bus.subscribe(event_type, handler)

    def _emit_event(self, event_type: EventType, user_id: str, data: Dict[str, Any] = None):
        """发布事件"""
        event_data = EventData(
            event_type=event_type,
            user_id=user_id,
            data=data or {}
        )

        self.event_bus.publish(event_type.value, {
            'user_id': user_id,
            'event_data': event_data,
            **event_data.data
        })

    # ============ 用户管理 ============

    async def create_user(self, user: User) -> User:
        """创建用户"""
        # 保存到数据库 (使用数据库管理器)
        if self.db_manager:
            await self._save_user_to_db(user)

        # 缓存用户
        self._cache_user(user)

        # 发布事件
        self._emit_event(EventType.USER_CREATED, user.user_id, {
            'phone_number': user.phone_number,
            'status': user.status.value
        })

        logger.info(f"Created user: {user.user_id}")
        return user

    async def get_user(self, user_id: str) -> Optional[User]:
        """获取用户信息 - 分层缓存策略"""
        # L1: 内存缓存 (最快)
        if self._is_memory_cache_valid(user_id):
            logger.debug(f"User cache hit (memory): {user_id}")
            return self._user_cache.get(user_id)

        # L2: Redis缓存 (快)
        user = self._load_user_from_redis(user_id)
        if user:
            logger.debug(f"User cache hit (Redis): {user_id}")
            # 更新内存缓存
            self._user_cache[user_id] = user
            self._cache_timestamps[user_id] = datetime.now()
            return user

        # L3: 数据库 (慢但可靠)
        if self.db_manager:
            user = await self._load_user_from_db(user_id)
            if user:
                logger.debug(f"User loaded from database: {user_id}")
                # 缓存到所有层
                self._cache_user(user)
                return user

        logger.debug(f"User not found: {user_id}")
        return None

    async def get_user_by_phone(self, phone_number: str) -> Optional[User]:
        """通过手机号获取用户"""
        # 直接从数据库查询（避免缓存不一致问题）
        if self.db_manager:
            user = await self._load_user_by_phone(phone_number)
            if user:
                self._cache_user(user)
                return user

        # 备用：遍历内存缓存
        for user in self._user_cache.values():
            if user.phone_number == phone_number:
                return user

        return None

    async def update_user(self, user: User) -> User:
        """更新用户信息"""
        # 更新缓存
        self._cache_user(user)

        # 更新数据库
        if self.db_manager:
            await self._save_user_to_db(user)

        logger.debug(f"Updated user: {user.user_id}")
        return user

    # ============ 状态管理 ============

    async def get_user_status(self, user_id: str) -> Optional[UserStatus]:
        """获取用户状态"""
        user = await self.get_user(user_id)
        return user.status if user else None

    async def is_registered_user(self, user_id: str) -> bool:
        """检查是否为注册用户"""
        user = await self.get_user(user_id)
        return user and user.status == UserStatus.ACTIVE and user.sms_verified

    async def upgrade_to_registered(self, user_id: str) -> bool:
        """升级用户为注册用户"""
        user = await self.get_user(user_id)
        if not user:
            logger.error(f"User not found: {user_id}")
            return False

        if user.status == UserStatus.ACTIVE and user.sms_verified:
            logger.info(f"User already registered: {user_id}")
            return True

        # 升级用户状态
        user.upgrade_to_registered()
        user.last_interaction = datetime.now()

        # 保存更新
        self._update_user(user)

        logger.info(f"User upgraded to registered: {user_id}")
        return True

    async def mark_voice_completed(self, user_id: str, voice_data: Optional[Dict[str, Any]] = None) -> bool:
        """标记语音通话完成"""
        user = await self.get_user(user_id)
        if not user:
            logger.error(f"User not found: {user_id}")
            return False

        # 更新用户状态
        user.voice_call_completed = True
        user.last_interaction = datetime.now()

        # 如果用户未激活，升级为活跃用户
        if user.status != UserStatus.ACTIVE:
            user.upgrade_to_registered()

        # 保存更新
        self._update_user(user)

        # 发布事件
        self._emit_event(EventType.VOICE_COMPLETED, user_id, voice_data or {})

        logger.info(f"Voice call completed for user: {user_id}")
        return True

    async def mark_linkedin_verified(self, user_id: str, verification_data: Optional[Dict[str, Any]] = None) -> bool:
        """标记LinkedIn验证完成"""
        user = await self.get_user(user_id)
        if not user:
            logger.error(f"User not found: {user_id}")
            return False

        # 更新验证状态
        user.linkedin_verified = True
        user.verification_level = VerificationLevel.VERIFIED
        user.last_interaction = datetime.now()

        # 保存更新
        self._update_user(user)

        # 发布事件
        self._emit_event(EventType.LINKEDIN_VERIFIED, user_id, verification_data or {})

        logger.info(f"LinkedIn verified for user: {user_id}")
        return True

    # ============ 权限验证 ============

    async def check_permission(self, user_id: str, permission: UserPermission) -> bool:
        """检查用户权限 - 基于验证状态而非用户状态"""
        user = await self.get_user(user_id)
        if not user:
            return False

        # 首先检查用户状态权限
        user_status_permissions = USER_PERMISSIONS.get(user.status, [])
        if permission not in user_status_permissions:
            return False

        # 然后检查验证状态权限
        verification_permissions = VERIFICATION_PERMISSIONS.get(user.verification_status, [])
        return permission in verification_permissions

    async def require_permission(self, user_id: str, permission: UserPermission) -> None:
        """要求用户具有特定权限，否则抛出异常"""
        if not await self.check_permission(user_id, permission):
            user = await self.get_user(user_id)
            status = user.status.value if user else "unknown"
            raise PermissionError(f"User {user_id} (status: {status}) lacks permission: {permission.value}")

    async def can_access_matching(self, user_id: str) -> bool:
        """检查是否可以访问匹配功能"""
        return await self.check_permission(user_id, UserPermission.MATCHING_ACCESS)

    async def require_voice_completed(self, user_id: str) -> None:
        """要求完成语音通话"""
        user = await self.get_user(user_id)
        if not user or not user.voice_call_completed:
            raise PermissionError("请先完成语音通话以解锁完整功能")

    async def get_available_permissions(self, user_id: str) -> List[UserPermission]:
        """获取用户可用权限列表"""
        user = await self.get_user(user_id)
        if not user:
            return []

        # 获取用户状态和验证状态的交集权限
        user_status_permissions = set(USER_PERMISSIONS.get(user.status, []))
        verification_permissions = set(VERIFICATION_PERMISSIONS.get(user.verification_status, []))

        # 返回交集
        return list(user_status_permissions.intersection(verification_permissions))

    # ============ 对话管理 ============

    async def get_conversation_context(self, user_id: str, context_type: str = "recent") -> Optional[Dict[str, Any]]:
        """获取对话上下文"""
        user = await self.get_user(user_id)
        if not user:
            return None

        # 使用ConversationManager获取上下文
        context = self.conversation_manager.get_conversation_context(user_id, context_type)

        # 添加用户状态信息
        context['user_stage'] = user.current_conversation_stage.value
        context['user_message_count'] = user.message_count

        return context

    async def add_conversation_message(self, user_id: str, role: str, content: str,
                               is_important: bool = False) -> Optional[str]:
        """添加对话消息"""
        user = await self.get_user(user_id)
        if not user:
            logger.error(f"User not found: {user_id}")
            return None

        # 判断消息重要性
        if not is_important:
            is_important = self._is_message_important(role, content)

        # 使用ConversationManager添加消息
        message_id = self.conversation_manager.add_message(
            user_id, role, content,
            message_type="text",
            is_important=is_important
        )

        # 更新用户统计
        user.message_count += 1
        user.last_interaction = datetime.now()
        self._update_user(user)

        logger.debug(f"Added message for user {user_id}: {role} - {content[:50]}...")
        return message_id

    async def update_conversation_stage(self, user_id: str, new_stage: ConversationStage) -> bool:
        """更新对话阶段"""
        user = await self.get_user(user_id)
        if not user:
            return False

        # 更新阶段
        old_stage = user.current_conversation_stage
        user.current_conversation_stage = new_stage
        user.last_interaction = datetime.now()

        # 保存更新
        self._update_user(user)

        # 添加系统消息记录阶段变化
        self.add_conversation_message(
            user_id,
            "system",
            f"Conversation stage changed: {old_stage.value} → {new_stage.value}",
            is_important=True
        )

        logger.info(f"User {user_id} conversation stage: {old_stage.value} → {new_stage.value}")
        return True

    def _is_message_important(self, role: str, content: str) -> bool:
        """判断消息是否重要"""
        # 简单的重要性判断逻辑
        if role == "system":
            return True

        # 用户提供个人信息的消息通常重要
        important_keywords = [
            "my name", "i work", "i am", "i live", "my job",
            "i like", "i love", "i prefer", "my hobby", "i enjoy"
        ]

        content_lower = content.lower()
        return any(keyword in content_lower for keyword in important_keywords)

    async def get_conversation_summary(self, user_id: str) -> Dict[str, Any]:
        """获取对话摘要"""
        # 获取分析用的上下文
        analysis_context = await self.get_conversation_context(user_id, "analysis")
        conversation_stats = self.conversation_manager.get_conversation_stats(user_id)

        user = await self.get_user(user_id)

        return {
            'user_id': user_id,
            'current_stage': user.current_conversation_stage.value if user else "unknown",
            'total_messages': user.message_count if user else 0,
            'analysis_context_messages': len(analysis_context.get('messages', [])),
            'recent_messages': conversation_stats.get('recent_message_count', 0),
            'important_messages': conversation_stats.get('important_message_count', 0),
            'has_cached_data': conversation_stats.get('has_cached_recent', False)
        }

    # ============ 缓存管理 ============

    def _cache_user(self, user: User) -> None:
        """缓存用户到内存和Redis"""
        # L1: 内存缓存
        self._user_cache[user.user_id] = user
        self._cache_timestamps[user.user_id] = datetime.now()

        # L2: Redis缓存
        if self.redis:
            user_data = {
                'user_id': user.user_id,
                'phone_number': user.phone_number,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'status': user.status.value,
                'sms_verified': user.sms_verified,
                'voice_call_completed': user.voice_call_completed,
                'linkedin_verified': user.linkedin_verified,
                'verification_status': user.verification_status.value,
                'verification_level': user.verification_level.value,
                'current_conversation_stage': user.current_conversation_stage.value,
                'created_at': user.created_at.isoformat(),
                'updated_at': user.updated_at.isoformat(),
                'last_login': user.last_login.isoformat(),
                'metadata': user.metadata
            }

            cache_key = f"user:{user.user_id}"
            self.redis.setex(
                cache_key,
                int(self.redis_cache_ttl.total_seconds()),
                json.dumps(user_data)
            )

    def _is_memory_cache_valid(self, user_id: str) -> bool:
        """检查内存缓存是否有效"""
        if user_id not in self._user_cache:
            return False

        timestamp = self._cache_timestamps.get(user_id)
        if not timestamp:
            return False

        return datetime.now() - timestamp < self.memory_cache_ttl

    def _load_user_from_redis(self, user_id: str) -> Optional[User]:
        """从Redis加载用户"""
        if not self.redis:
            return None

        cache_key = f"user:{user_id}"
        user_data = self.redis.get(cache_key)

        if not user_data:
            return None

        try:
            data = json.loads(user_data)
            user = User(
                user_id=data['user_id'],
                phone_number=data.get('phone_number'),
                first_name=data.get('first_name'),
                last_name=data.get('last_name'),
                status=UserStatus(data['status']),
                sms_verified=data.get('sms_verified', False),
                voice_call_completed=data.get('voice_call_completed', False),
                linkedin_verified=data.get('linkedin_verified', False),
                verification_status=VerificationStatus(data.get('verification_status', 'pending')),
                verification_level=VerificationLevel(data.get('verification_level', 'unverified')),
                current_conversation_stage=ConversationStage(data.get('current_conversation_stage', 'registration')),
                created_at=datetime.fromisoformat(data['created_at']) if 'created_at' in data else datetime.now(),
                updated_at=datetime.fromisoformat(data['updated_at']) if 'updated_at' in data else datetime.now(),
                last_login=datetime.fromisoformat(data['last_login']) if 'last_login' in data else datetime.now(),
                metadata=data.get('metadata', {})
            )

            logger.debug(f"Loaded user from Redis: {user_id}")
            return user

        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.error(f"Failed to deserialize user from Redis: {e}")
            # 清除损坏的缓存
            self.redis.delete(cache_key)
            return None

    def _update_user(self, user: User) -> None:
        """更新用户信息"""
        # 更新所有缓存层
        self._cache_user(user)

        # 保存到数据库
        if self.db_manager:
            self._save_user_to_db(user)

    def clear_cache(self) -> None:
        """清空所有缓存"""
        # 清空内存缓存
        self._user_cache.clear()
        self._cache_timestamps.clear()

        # 清空Redis缓存 (可选，通常不需要)
        # if self.redis:
        #     # 这会清空所有用户缓存，慎用
        #     pass

        logger.info("User cache cleared")

    # ============ 数据库操作 (占位符) ============

    async def _save_user_to_db(self, user: User) -> None:
        """异步保存用户到数据库"""
        try:
            # 调用异步的数据库保存方法
            await self.db_manager.save_user(user)
            logger.debug(f"User saved to database: {user.user_id}")
        except Exception as e:
            logger.error(f"Failed to save user to database: {e}")

    async def _load_user_from_db(self, user_id: str) -> Optional[User]:
        """异步从数据库加载用户"""
        try:
            # 调用异步的数据库加载方法
            user = await self.db_manager.load_user(user_id)
            if user:
                logger.debug(f"User loaded from database: {user_id}")
            return user
        except Exception as e:
            logger.error(f"Failed to load user from database: {e}")
            return None

    async def _load_user_by_phone(self, phone_number: str) -> Optional[User]:
        """异步通过手机号从数据库加载用户"""
        try:
            # 调用异步的数据库加载方法
            user = await self.db_manager.load_user_by_phone(phone_number)
            if user:
                logger.debug(f"User loaded by phone: {phone_number}")
            return user
        except Exception as e:
            logger.error(f"Failed to load user by phone: {e}")
            return None

    # ============ 工具方法 ============

    async def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """获取用户统计信息"""
        user = await self.get_user(user_id)
        context = await self.get_conversation_context(user_id)

        if not user:
            return {}

        return {
            'user_id': user.user_id,
            'status': user.status.value,
            'verification_level': user.verification_level.value,
            'voice_completed': user.voice_call_completed,
            'linkedin_verified': user.linkedin_verified,
            'message_count': user.message_count,
            'current_stage': user.current_conversation_stage.value,
            'created_at': user.created_at.isoformat(),
            'last_interaction': user.last_interaction.isoformat(),
            'available_permissions': [p.value for p in await self.get_available_permissions(user_id)],
            'stage_attempts': context.stage_attempts if context else {}
        }

    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        stats = {
            'memory_cached_users': len(self._user_cache),
            'event_handlers': {event_type: len(handlers)
                             for event_type, handlers in self.event_bus.handlers.items()},
            'memory_cache_ttl_minutes': self.memory_cache_ttl.total_seconds() / 60,
            'redis_cache_ttl_hours': self.redis_cache_ttl.total_seconds() / 3600,
            'has_redis': self.redis is not None,
            'has_database': self.db is not None
        }

        # Redis统计信息
        if self.redis:
            try:
                # 获取Redis中的用户缓存数量
                user_keys = self.redis.keys("user:*")
                conversation_keys = self.redis.keys("recent_messages:*")
                important_keys = self.redis.keys("important_messages:*")

                stats.update({
                    'redis_cached_users': len(user_keys),
                    'redis_cached_conversations': len(conversation_keys),
                    'redis_cached_important_messages': len(important_keys)
                })
            except Exception as e:
                logger.error(f"Failed to get Redis stats: {e}")
                stats['redis_error'] = str(e)

        return stats

    # ============ JWT令牌管理 ============

    def create_access_token(self, user_id: str) -> str:
        """创建访问令牌"""
        try:
            import jwt
            from ..config.core_config import CoreConfig
            from datetime import timezone

            # 使用UTC时间创建JWT token
            now_utc = datetime.now(timezone.utc)
            exp_utc = now_utc + timedelta(minutes=CoreConfig.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)

            payload = {
                "user_id": user_id,
                "type": "access",
                "exp": int(exp_utc.timestamp()),
                "iat": int(now_utc.timestamp())
            }

            token = jwt.encode(payload, CoreConfig.JWT_SECRET_KEY, algorithm="HS256")
            logger.debug(f"Created access token for user: {user_id}")
            return token

        except Exception as e:
            logger.error(f"Failed to create access token: {e}")
            raise

    def create_refresh_token(self, user_id: str) -> str:
        """创建刷新令牌"""
        try:
            import jwt
            from ..config.core_config import CoreConfig
            from datetime import timezone

            # 使用UTC时间创建JWT refresh token
            now_utc = datetime.now(timezone.utc)
            exp_utc = now_utc + timedelta(days=CoreConfig.JWT_REFRESH_TOKEN_EXPIRE_DAYS)

            payload = {
                "user_id": user_id,
                "type": "refresh",
                "exp": int(exp_utc.timestamp()),
                "iat": int(now_utc.timestamp())
            }

            token = jwt.encode(payload, CoreConfig.JWT_REFRESH_SECRET_KEY, algorithm="HS256")
            logger.debug(f"Created refresh token for user: {user_id}")
            return token

        except Exception as e:
            logger.error(f"Failed to create refresh token: {e}")
            raise

    def verify_access_token(self, token: str) -> Optional[str]:
        """验证访问令牌并返回用户ID"""
        try:
            import jwt
            from ..config.core_config import CoreConfig

            payload = jwt.decode(token, CoreConfig.JWT_SECRET_KEY, algorithms=["HS256"])

            if payload.get("type") != "access":
                return None

            return payload.get("user_id")

        except jwt.ExpiredSignatureError:
            logger.debug("Access token expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.debug(f"Invalid access token: {e}")
            return None
        except Exception as e:
            logger.error(f"Token verification error: {e}")
            return None

    def verify_refresh_token(self, token: str) -> Optional[str]:
        """验证刷新令牌并返回用户ID"""
        try:
            import jwt
            from ..config.core_config import CoreConfig

            payload = jwt.decode(token, CoreConfig.JWT_REFRESH_SECRET_KEY, algorithms=["HS256"])

            if payload.get("type") != "refresh":
                return None

            return payload.get("user_id")

        except jwt.ExpiredSignatureError:
            logger.debug("Refresh token expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.debug(f"Invalid refresh token: {e}")
            return None
        except Exception as e:
            logger.error(f"Refresh token verification error: {e}")
            return None

    # ============ 用户偏好管理 ============

    def get_user_preferences(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户偏好设置"""
        # MVP版本：返回默认偏好
        # 实际实现应该从数据库获取
        return {
            "age_range_min": 18,
            "age_range_max": 45,
            "max_distance": 50,
            "preferred_mbti_types": [],
            "deal_breakers": [],
            "interests": []
        }

    def update_user_preferences(self, user_id: str, preferences: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户偏好设置"""
        # MVP版本：简单返回更新后的偏好
        # 实际实现应该保存到数据库
        logger.info(f"Updated preferences for user: {user_id}")
        return preferences

    def check_database_connection(self) -> Dict[str, Any]:
        """检查数据库连接状态"""
        try:
            # 使用数据库管理器检查连接
            return self.db_manager.check_connection()
        except Exception as e:
            return {"connected": False, "error": str(e)}
