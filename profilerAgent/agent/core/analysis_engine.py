"""
AnalysisEngine - AI分析核心
合并原有的profile_analyzer和adaptive_profiler功能
使用DeepSeek进行用户画像分析和生成
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import json
import logging
import asyncio
from dataclasses import asdict

from ..config.core_config import CoreConfig
from ..config.templates import Templates
from ..utils import (
    AnalysisResult, ProfileCard, VoiceAnalysis,
    UserFeedback, APIResponse
)

logger = logging.getLogger(__name__)

class DeepSeekClient:
    """DeepSeek API客户端"""

    def __init__(self, api_key: str, base_url: str = "https://api.deepseek.com",
                 model: str = "deepseek-chat"):
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.timeout = CoreConfig.DEEPSEEK_TIMEOUT

        # 请求配置
        self.default_params = {
            "model": model,
            "temperature": CoreConfig.DEEPSEEK_TEMPERATURE,
            "max_tokens": CoreConfig.DEEPSEEK_MAX_TOKENS
        }

        logger.info(f"DeepSeek client initialized: {model}")

    async def analyze_async(self, system_prompt: str, user_prompt: str,
                           response_format: str = "json") -> Dict[str, Any]:
        """异步分析请求"""
        try:
            # 构建请求
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            request_data = {
                **self.default_params,
                "messages": messages
            }

            # 模拟API调用 (实际实现需要使用HTTP客户端)
            # TODO: 实现真实的DeepSeek API调用
            response = await self._mock_deepseek_call(request_data)

            if response_format == "json":
                return self._parse_json_response(response)
            else:
                return {"content": response}

        except Exception as e:
            logger.error(f"DeepSeek API call failed: {e}")
            raise

    def analyze(self, system_prompt: str, user_prompt: str,
                response_format: str = "json") -> Dict[str, Any]:
        """同步分析请求"""
        try:
            # 在MVP阶段使用同步调用
            return asyncio.run(self.analyze_async(system_prompt, user_prompt, response_format))
        except Exception as e:
            logger.error(f"DeepSeek analysis failed: {e}")
            return {"error": str(e)}

    async def _mock_deepseek_call(self, request_data: Dict[str, Any]) -> str:
        """模拟DeepSeek API调用 - MVP测试用"""
        # 模拟API延迟
        await asyncio.sleep(0.1)

        # 根据请求内容返回模拟响应
        user_content = request_data["messages"][-1]["content"]

        if "MBTI" in user_content or "personality" in user_content:
            return self._generate_mock_personality_analysis()
        elif "LinkedIn" in user_content or "verification" in user_content:
            return self._generate_mock_verification_analysis()
        elif "profile cards" in user_content:
            return self._generate_mock_profile_cards()
        else:
            return self._generate_mock_general_analysis()

    def _generate_mock_personality_analysis(self) -> str:
        """生成模拟的性格分析"""
        return json.dumps({
            "mbti_dimensions": {
                "E_I": 0.7,  # 偏外向
                "S_N": 0.4,  # 偏感觉
                "T_F": 0.6,  # 偏思考
                "J_P": 0.8   # 偏判断
            },
            "mbti_type": "ESTJ",
            "personality_traits": {
                "leadership": 0.8,
                "analytical": 0.7,
                "social": 0.6,
                "creative": 0.4,
                "organized": 0.9
            },
            "interests": ["technology", "leadership", "problem-solving", "team-building"],
            "professional_info": {
                "career_focus": "technology_leadership",
                "experience_level": "senior",
                "industry": "technology"
            },
            "confidence_scores": {
                "mbti": 0.85,
                "personality": 0.78,
                "interests": 0.82,
                "professional": 0.90
            },
            "evidence": [
                "Shows strong organizational skills in conversation",
                "Demonstrates leadership qualities",
                "Analytical approach to problem-solving",
                "Clear communication style"
            ]
        })

    def _generate_mock_verification_analysis(self) -> str:
        """生成模拟的验证分析"""
        return json.dumps({
            "consistency_score": 0.85,
            "verification_status": "verified",
            "consistency_breakdown": {
                "basic_info": 0.95,
                "professional": 0.88,
                "skills": 0.80,
                "communication": 0.82
            },
            "inconsistencies": [],
            "recommendation": "verified"
        })

    def _generate_mock_profile_cards(self) -> str:
        """生成模拟的画像卡片"""
        return json.dumps({
            "cards": [
                {
                    "type": "professional",
                    "title": "Tech Leader",
                    "description": "Experienced technology professional with strong leadership skills",
                    "confidence": 0.88,
                    "tags": ["leadership", "technology", "management"]
                },
                {
                    "type": "personality",
                    "title": "The Organized Achiever",
                    "description": "Goal-oriented individual who thrives on structure and achievement",
                    "confidence": 0.82,
                    "tags": ["organized", "goal-oriented", "achiever"]
                },
                {
                    "type": "social",
                    "title": "Team Builder",
                    "description": "Natural ability to bring people together and build effective teams",
                    "confidence": 0.75,
                    "tags": ["collaborative", "team-focused", "social"]
                }
            ]
        })

    def _generate_mock_general_analysis(self) -> str:
        """生成模拟的通用分析"""
        return json.dumps({
            "analysis_type": "general",
            "key_insights": [
                "Professional and articulate communication style",
                "Shows interest in technology and innovation",
                "Demonstrates analytical thinking"
            ],
            "confidence": 0.75
        })

    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """解析JSON响应"""
        try:
            return json.loads(response)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            return {"error": "Invalid JSON response", "raw_response": response}

class AnalysisEngine:
    """AI分析引擎 - MVP版本"""

    def __init__(self, deepseek_config: Optional[Dict[str, Any]] = None):
        """初始化分析引擎"""
        # 获取DeepSeek配置
        if deepseek_config is None:
            deepseek_config = CoreConfig.get_deepseek_config()

        # 初始化DeepSeek客户端
        self.deepseek_client = DeepSeekClient(
            api_key=deepseek_config["api_key"],
            base_url=deepseek_config["base_url"],
            model=deepseek_config["model"]
        )

        # 分析配置
        self.min_confidence_threshold = CoreConfig.MIN_CONFIDENCE_THRESHOLD
        self.mbti_analysis_threshold = CoreConfig.MBTI_ANALYSIS_THRESHOLD

        # 缓存分析结果 (简单内存缓存)
        self._analysis_cache: Dict[str, AnalysisResult] = {}
        self._profile_cards_cache: Dict[str, List[ProfileCard]] = {}

        logger.info("AnalysisEngine initialized")

    def analyze_conversation(self, user_id: str, conversation_data: Dict[str, Any]) -> AnalysisResult:
        """分析对话内容生成用户画像"""
        try:
            # 构建分析提示
            system_prompt = Templates.AI_PROMPTS["voice_analysis"]["system_prompt"]

            # 准备对话文本
            conversation_text = self._format_conversation_for_analysis(conversation_data)

            user_prompt = Templates.AI_PROMPTS["voice_analysis"]["analysis_prompt"].format(
                conversation_text=conversation_text
            )

            # 调用DeepSeek分析
            logger.info(f"Starting conversation analysis for user: {user_id}")
            analysis_response = self.deepseek_client.analyze(system_prompt, user_prompt, "json")

            if "error" in analysis_response:
                raise Exception(f"DeepSeek analysis failed: {analysis_response['error']}")

            # 构建分析结果
            result = self._build_analysis_result(user_id, analysis_response, "conversation")

            # 缓存结果
            self._analysis_cache[user_id] = result

            logger.info(f"Conversation analysis completed for user: {user_id}")
            return result

        except Exception as e:
            logger.error(f"Conversation analysis failed for user {user_id}: {e}")
            return self._create_fallback_analysis_result(user_id, "conversation", str(e))

    def analyze_voice_transcript(self, user_id: str, voice_data: VoiceAnalysis) -> AnalysisResult:
        """分析语音转录生成画像"""
        try:
            # 构建语音分析提示
            system_prompt = Templates.AI_PROMPTS["voice_analysis"]["system_prompt"]

            # 准备语音转录文本
            transcript_text = voice_data.transcript

            user_prompt = Templates.AI_PROMPTS["voice_analysis"]["analysis_prompt"].format(
                conversation_text=transcript_text
            )

            # 调用DeepSeek分析
            logger.info(f"Starting voice analysis for user: {user_id}")
            analysis_response = self.deepseek_client.analyze(system_prompt, user_prompt, "json")

            if "error" in analysis_response:
                raise Exception(f"DeepSeek analysis failed: {analysis_response['error']}")

            # 构建分析结果
            result = self._build_analysis_result(user_id, analysis_response, "voice")

            # 添加语音特定信息
            result.raw_analysis["voice_quality"] = voice_data.quality_score
            result.raw_analysis["call_duration"] = voice_data.call_duration
            result.raw_analysis["engagement_score"] = voice_data.calculate_engagement_score()

            # 缓存结果
            self._analysis_cache[user_id] = result

            logger.info(f"Voice analysis completed for user: {user_id}")
            return result

        except Exception as e:
            logger.error(f"Voice analysis failed for user {user_id}: {e}")
            return self._create_fallback_analysis_result(user_id, "voice", str(e))

    def combine_analyses(self, user_id: str, conversation_result: AnalysisResult,
                        voice_result: AnalysisResult) -> AnalysisResult:
        """合并对话和语音分析结果"""
        try:
            # 合并MBTI维度 (加权平均)
            combined_mbti = {}
            for dimension in ["E_I", "S_N", "T_F", "J_P"]:
                conv_score = conversation_result.mbti_dimensions.get(dimension, 0.5)
                voice_score = voice_result.mbti_dimensions.get(dimension, 0.5)
                # 语音分析权重稍高 (0.6 vs 0.4)
                combined_mbti[dimension] = voice_score * 0.6 + conv_score * 0.4

            # 确定最终MBTI类型
            mbti_type = self._determine_mbti_type(combined_mbti)

            # 合并性格特征
            combined_traits = self._merge_personality_traits(
                conversation_result.personality_traits,
                voice_result.personality_traits
            )

            # 合并兴趣爱好
            combined_interests = list(set(
                conversation_result.interests + voice_result.interests
            ))

            # 合并职业信息
            combined_professional = {
                **conversation_result.professional_info,
                **voice_result.professional_info
            }

            # 计算合并后的置信度
            combined_confidence = self._calculate_combined_confidence(
                conversation_result.confidence_scores,
                voice_result.confidence_scores
            )

            # 创建合并结果
            combined_result = AnalysisResult(
                user_id=user_id,
                analysis_type="combined",
                mbti_type=mbti_type,
                mbti_dimensions=combined_mbti,
                personality_traits=combined_traits,
                interests=combined_interests,
                professional_info=combined_professional,
                confidence_scores=combined_confidence,
                raw_analysis={
                    "conversation_analysis": conversation_result.raw_analysis,
                    "voice_analysis": voice_result.raw_analysis,
                    "combination_method": "weighted_average"
                }
            )

            # 缓存合并结果
            self._analysis_cache[user_id] = combined_result

            logger.info(f"Combined analysis completed for user: {user_id}")
            return combined_result

        except Exception as e:
            logger.error(f"Analysis combination failed for user {user_id}: {e}")
            return self._create_fallback_analysis_result(user_id, "combined", str(e))

    def generate_profile_cards(self, user_id: str, analysis_result: Optional[AnalysisResult] = None) -> List[ProfileCard]:
        """生成用户画像卡片"""
        try:
            # 获取分析结果
            if analysis_result is None:
                analysis_result = self._analysis_cache.get(user_id)
                if not analysis_result:
                    raise ValueError(f"No analysis result found for user: {user_id}")

            cards = []

            # 1. 生成职业卡片
            if analysis_result.professional_info:
                professional_card = self._generate_professional_card(user_id, analysis_result)
                if professional_card:
                    cards.append(professional_card)

            # 2. 生成性格卡片
            if analysis_result.mbti_type:
                personality_card = self._generate_personality_card(user_id, analysis_result)
                if personality_card:
                    cards.append(personality_card)

            # 3. 生成兴趣卡片
            if analysis_result.interests:
                interest_cards = self._generate_interest_cards(user_id, analysis_result)
                cards.extend(interest_cards)

            # 4. 生成社交卡片
            social_card = self._generate_social_card(user_id, analysis_result)
            if social_card:
                cards.append(social_card)

            # 缓存卡片
            self._profile_cards_cache[user_id] = cards

            logger.info(f"Generated {len(cards)} profile cards for user: {user_id}")
            return cards

        except Exception as e:
            logger.error(f"Profile card generation failed for user {user_id}: {e}")
            return []

    def update_profile_with_feedback(self, user_id: str, feedback: UserFeedback) -> bool:
        """根据用户反馈更新画像"""
        try:
            # 获取当前分析结果
            current_analysis = self._analysis_cache.get(user_id)
            if not current_analysis:
                logger.warning(f"No analysis found for feedback update: {user_id}")
                return False

            # 根据反馈类型调整置信度
            adjustment = self._calculate_confidence_adjustment(feedback)

            # 更新置信度分数
            if feedback.card_id in current_analysis.confidence_scores:
                current_score = current_analysis.confidence_scores[feedback.card_id]
                new_score = max(0.0, min(1.0, current_score + adjustment))
                current_analysis.confidence_scores[feedback.card_id] = new_score

                logger.info(f"Updated confidence for {feedback.card_id}: {current_score:.2f} → {new_score:.2f}")

            # 如果反馈是负面的，可能需要重新分析
            if feedback.feedback_type == "inaccurate" and adjustment < -0.2:
                logger.info(f"Negative feedback received for user {user_id}, consider re-analysis")

            return True

        except Exception as e:
            logger.error(f"Profile update with feedback failed for user {user_id}: {e}")
            return False

    def get_analysis_result(self, user_id: str) -> Optional[AnalysisResult]:
        """获取用户的分析结果"""
        return self._analysis_cache.get(user_id)

    def get_profile_cards(self, user_id: str) -> List[ProfileCard]:
        """获取用户的画像卡片"""
        return self._profile_cards_cache.get(user_id, [])

    def calculate_confidence_scores(self, analysis_data: Dict[str, Any]) -> Dict[str, float]:
        """计算置信度分数"""
        confidence_scores = {}

        # MBTI置信度
        if "mbti_dimensions" in analysis_data:
            mbti_confidence = self._calculate_mbti_confidence(analysis_data["mbti_dimensions"])
            confidence_scores["mbti"] = mbti_confidence

        # 性格特征置信度
        if "personality_traits" in analysis_data:
            personality_confidence = self._calculate_personality_confidence(analysis_data["personality_traits"])
            confidence_scores["personality"] = personality_confidence

        # 兴趣爱好置信度
        if "interests" in analysis_data:
            interests_confidence = len(analysis_data["interests"]) * 0.1  # 简单计算
            confidence_scores["interests"] = min(1.0, interests_confidence)

        # 职业信息置信度
        if "professional_info" in analysis_data:
            professional_confidence = len(analysis_data["professional_info"]) * 0.2
            confidence_scores["professional"] = min(1.0, professional_confidence)

        return confidence_scores

    # ============ 工具方法 ============

    def _format_conversation_for_analysis(self, conversation_data: Dict[str, Any]) -> str:
        """格式化对话数据用于分析"""
        messages = conversation_data.get("messages", [])

        formatted_text = ""
        for msg in messages:
            role = msg.get("role", "unknown")
            content = msg.get("content", "")
            formatted_text += f"{role}: {content}\n"

        return formatted_text

    def _build_analysis_result(self, user_id: str, analysis_response: Dict[str, Any],
                              analysis_type: str) -> AnalysisResult:
        """构建分析结果对象"""
        return AnalysisResult(
            user_id=user_id,
            analysis_type=analysis_type,
            mbti_type=analysis_response.get("mbti_type"),
            mbti_dimensions=analysis_response.get("mbti_dimensions", {}),
            personality_traits=analysis_response.get("personality_traits", {}),
            interests=analysis_response.get("interests", []),
            professional_info=analysis_response.get("professional_info", {}),
            confidence_scores=analysis_response.get("confidence_scores", {}),
            raw_analysis=analysis_response
        )

    def _create_fallback_analysis_result(self, user_id: str, analysis_type: str, error: str) -> AnalysisResult:
        """创建失败时的备用分析结果"""
        return AnalysisResult(
            user_id=user_id,
            analysis_type=analysis_type,
            mbti_type=None,
            mbti_dimensions={},
            personality_traits={},
            interests=[],
            professional_info={},
            confidence_scores={"error": 0.0},
            raw_analysis={"error": error, "fallback": True}
        )

    def _determine_mbti_type(self, mbti_dimensions: Dict[str, float]) -> str:
        """根据维度分数确定MBTI类型"""
        mbti_letters = []

        # E/I
        mbti_letters.append("E" if mbti_dimensions.get("E_I", 0.5) > 0.5 else "I")
        # S/N
        mbti_letters.append("S" if mbti_dimensions.get("S_N", 0.5) > 0.5 else "N")
        # T/F
        mbti_letters.append("T" if mbti_dimensions.get("T_F", 0.5) > 0.5 else "F")
        # J/P
        mbti_letters.append("J" if mbti_dimensions.get("J_P", 0.5) > 0.5 else "P")

        return "".join(mbti_letters)

    def _merge_personality_traits(self, traits1: Dict[str, float], traits2: Dict[str, float]) -> Dict[str, float]:
        """合并性格特征"""
        merged = {}
        all_traits = set(traits1.keys()) | set(traits2.keys())

        for trait in all_traits:
            score1 = traits1.get(trait, 0.5)
            score2 = traits2.get(trait, 0.5)
            merged[trait] = (score1 + score2) / 2

        return merged

    def _calculate_combined_confidence(self, conf1: Dict[str, float], conf2: Dict[str, float]) -> Dict[str, float]:
        """计算合并后的置信度"""
        combined = {}
        all_keys = set(conf1.keys()) | set(conf2.keys())

        for key in all_keys:
            score1 = conf1.get(key, 0.5)
            score2 = conf2.get(key, 0.5)
            # 使用几何平均提高置信度
            combined[key] = (score1 * score2) ** 0.5

        return combined

    def _generate_professional_card(self, user_id: str, analysis: AnalysisResult) -> Optional[ProfileCard]:
        """生成职业画像卡片"""
        professional_info = analysis.professional_info
        if not professional_info:
            return None

        # 根据职业信息选择合适的模板
        career_focus = professional_info.get("career_focus", "general")

        if "technology" in career_focus.lower():
            template = Templates.get_profile_card_template("professional", "tech_innovator")
        elif "business" in career_focus.lower():
            template = Templates.get_profile_card_template("professional", "business_strategist")
        elif "creative" in career_focus.lower():
            template = Templates.get_profile_card_template("professional", "creative_professional")
        else:
            template = Templates.get_profile_card_template("professional", "people_leader")

        confidence = analysis.confidence_scores.get("professional", 0.7)

        card = ProfileCard.create_card(
            user_id=user_id,
            card_type="professional",
            title=template["title"],
            description=template["description"],
            confidence=confidence
        )

        card.tags = template["tags"]
        card.data_sources = [analysis.analysis_type]

        return card

    def _generate_personality_card(self, user_id: str, analysis: AnalysisResult) -> Optional[ProfileCard]:
        """生成性格画像卡片"""
        mbti_type = analysis.mbti_type
        if not mbti_type:
            return None

        # 获取MBTI模板
        template = Templates.get_profile_card_template("personality", mbti_type)
        confidence = analysis.confidence_scores.get("mbti", 0.7)

        card = ProfileCard.create_card(
            user_id=user_id,
            card_type="personality",
            title=template["title"],
            description=template["description"],
            confidence=confidence
        )

        card.tags = template["tags"]
        card.data_sources = [analysis.analysis_type]

        return card

    def _generate_interest_cards(self, user_id: str, analysis: AnalysisResult) -> List[ProfileCard]:
        """生成兴趣画像卡片"""
        interests = analysis.interests
        if not interests:
            return []

        cards = []
        interest_templates = Templates.PROFILE_CARD_TEMPLATES["interests"]

        # 为主要兴趣生成卡片
        for interest in interests[:2]:  # 最多2个兴趣卡片
            # 匹配兴趣模板
            template_key = self._match_interest_template(interest, interest_templates)
            template = interest_templates.get(template_key, interest_templates["music_lover"])

            confidence = analysis.confidence_scores.get("interests", 0.6)

            card = ProfileCard.create_card(
                user_id=user_id,
                card_type="interests",
                title=template["title"],
                description=template["description"],
                confidence=confidence
            )

            card.tags = template["tags"] + [interest]
            card.data_sources = [analysis.analysis_type]
            cards.append(card)

        return cards

    def _generate_social_card(self, user_id: str, analysis: AnalysisResult) -> Optional[ProfileCard]:
        """生成社交画像卡片"""
        personality_traits = analysis.personality_traits
        mbti_dimensions = analysis.mbti_dimensions

        # 根据性格特征确定社交类型
        social_score = personality_traits.get("social", 0.5)
        extroversion = mbti_dimensions.get("E_I", 0.5)

        if social_score > 0.7 and extroversion > 0.6:
            template = Templates.get_profile_card_template("social", "social_connector")
        elif personality_traits.get("analytical", 0.5) > 0.7:
            template = Templates.get_profile_card_template("social", "deep_thinker")
        else:
            template = Templates.get_profile_card_template("social", "team_player")

        confidence = (analysis.confidence_scores.get("personality", 0.7) +
                     analysis.confidence_scores.get("mbti", 0.7)) / 2

        card = ProfileCard.create_card(
            user_id=user_id,
            card_type="social",
            title=template["title"],
            description=template["description"],
            confidence=confidence
        )

        card.tags = template["tags"]
        card.data_sources = [analysis.analysis_type]

        return card

    def _match_interest_template(self, interest: str, templates: Dict[str, Any]) -> str:
        """匹配兴趣模板"""
        interest_lower = interest.lower()

        if any(word in interest_lower for word in ["music", "song", "concert"]):
            return "music_lover"
        elif any(word in interest_lower for word in ["travel", "adventure", "hiking", "outdoor"]):
            return "adventure_seeker"
        elif any(word in interest_lower for word in ["food", "cooking", "restaurant", "cuisine"]):
            return "foodie_explorer"
        elif any(word in interest_lower for word in ["fitness", "gym", "sport", "exercise"]):
            return "fitness_enthusiast"
        else:
            return "music_lover"  # 默认模板

    def _calculate_confidence_adjustment(self, feedback: UserFeedback) -> float:
        """计算反馈的置信度调整值"""
        if feedback.feedback_type == "accurate":
            return 0.1
        elif feedback.feedback_type == "partially_correct":
            return 0.05
        elif feedback.feedback_type == "inaccurate":
            return -0.15
        else:
            return 0.0

    def _calculate_mbti_confidence(self, mbti_dimensions: Dict[str, float]) -> float:
        """计算MBTI置信度"""
        # 基于维度分数的极端程度计算置信度
        confidences = []
        for score in mbti_dimensions.values():
            # 越接近0.5置信度越低，越接近0或1置信度越高
            confidence = abs(score - 0.5) * 2
            confidences.append(confidence)

        return sum(confidences) / len(confidences) if confidences else 0.5

    def _calculate_personality_confidence(self, personality_traits: Dict[str, float]) -> float:
        """计算性格特征置信度"""
        if not personality_traits:
            return 0.5

        # 基于特征分数的分布计算置信度
        scores = list(personality_traits.values())

        # 分数越极端（远离0.5）置信度越高
        confidence = sum(abs(score - 0.5) for score in scores) / len(scores) * 2

        return min(1.0, confidence)

    def get_analysis_stats(self) -> Dict[str, Any]:
        """获取分析引擎统计信息"""
        return {
            "cached_analyses": len(self._analysis_cache),
            "cached_profile_cards": len(self._profile_cards_cache),
            "min_confidence_threshold": self.min_confidence_threshold,
            "mbti_analysis_threshold": self.mbti_analysis_threshold,
            "deepseek_model": self.deepseek_client.model
        }
