"""
MatchEngine - 匹配算法核心
MBTI兼容性 + 兴趣匹配 + 地理位置 + 职业兼容性
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import logging
import math
import random
from collections import defaultdict

from ..config.core_config import CoreConfig
from ..config.templates import Templates
from ..utils import (
    AnalysisResult, MatchResult, User, UserStatus, VerificationStatus,
    UserPermission, APIResponse
)

logger = logging.getLogger(__name__)

class MatchEngine:
    """匹配引擎 - MVP版本"""

    def __init__(self, user_manager=None, analysis_engine=None):
        """初始化匹配引擎"""
        self.user_manager = user_manager
        self.analysis_engine = analysis_engine

        # 匹配算法配置
        self.matching_score_threshold = CoreConfig.MATCHING_SCORE_THRESHOLD
        self.max_daily_matches = CoreConfig.MAX_DAILY_MATCHES

        # 权重配置 (总和应为1.0)
        self.weights = {
            "mbti_compatibility": 0.35,      # MBTI兼容性权重最高
            "interest_overlap": 0.25,        # 兴趣重叠
            "professional_compatibility": 0.20,  # 职业兼容性
            "location_compatibility": 0.15,  # 地理位置
            "personality_similarity": 0.05   # 性格相似度
        }

        # 缓存匹配结果
        self._match_cache: Dict[str, List[MatchResult]] = {}
        self._compatibility_cache: Dict[Tuple[str, str], float] = {}

        # 匹配历史记录 (简单内存存储)
        self._match_history: Dict[str, List[str]] = defaultdict(list)
        self._daily_match_count: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))

        logger.info("MatchEngine initialized")

    def find_matches(self, user_id: str, limit: int = None) -> List[MatchResult]:
        """为用户找到匹配对象"""
        try:
            if limit is None:
                limit = self.max_daily_matches

            # 检查用户权限
            if not self.user_manager.check_permission(user_id, UserPermission.MATCHING_ACCESS):
                logger.warning(f"User {user_id} lacks matching permission")
                return []

            # 检查今日匹配限制
            today = datetime.now().strftime("%Y-%m-%d")
            if self._daily_match_count[user_id][today] >= self.max_daily_matches:
                logger.info(f"User {user_id} has reached daily match limit")
                return []

            # 获取用户分析结果
            user_analysis = self.analysis_engine.get_analysis_result(user_id)
            if not user_analysis:
                logger.warning(f"No analysis result found for user: {user_id}")
                return []

            # 获取候选用户池
            candidate_users = self._get_candidate_users(user_id)
            if not candidate_users:
                logger.info(f"No candidate users found for: {user_id}")
                return []

            # 计算匹配分数
            match_candidates = []
            for candidate_id in candidate_users:
                match_score = self.calculate_match_score(user_id, candidate_id)

                if match_score >= self.matching_score_threshold:
                    match_result = self._create_match_result(user_id, candidate_id, match_score)
                    match_candidates.append(match_result)

            # 按分数排序并限制数量
            match_candidates.sort(key=lambda x: x.match_score, reverse=True)
            final_matches = match_candidates[:limit]

            # 更新匹配历史和计数
            for match in final_matches:
                self._match_history[user_id].append(match.user2_id)
                self._daily_match_count[user_id][today] += 1

            # 缓存结果
            self._match_cache[user_id] = final_matches

            logger.info(f"Found {len(final_matches)} matches for user: {user_id}")
            return final_matches

        except Exception as e:
            logger.error(f"Match finding failed for user {user_id}: {e}")
            return []

    def calculate_match_score(self, user1_id: str, user2_id: str) -> float:
        """计算两个用户的匹配分数"""
        try:
            # 检查缓存
            cache_key = tuple(sorted([user1_id, user2_id]))
            if cache_key in self._compatibility_cache:
                return self._compatibility_cache[cache_key]

            # 获取用户分析结果
            user1_analysis = self.analysis_engine.get_analysis_result(user1_id)
            user2_analysis = self.analysis_engine.get_analysis_result(user2_id)

            if not user1_analysis or not user2_analysis:
                return 0.0

            # 计算各维度兼容性
            scores = {}

            # 1. MBTI兼容性
            scores["mbti_compatibility"] = self.calculate_mbti_compatibility(
                user1_analysis.mbti_type, user2_analysis.mbti_type
            )

            # 2. 兴趣重叠度
            scores["interest_overlap"] = self.calculate_interest_overlap(
                user1_analysis.interests, user2_analysis.interests
            )

            # 3. 职业兼容性
            scores["professional_compatibility"] = self.calculate_professional_compatibility(
                user1_analysis.professional_info, user2_analysis.professional_info
            )

            # 4. 地理位置兼容性 (模拟)
            scores["location_compatibility"] = self.calculate_location_compatibility(
                user1_analysis.professional_info.get("location", "unknown"),
                user2_analysis.professional_info.get("location", "unknown")
            )

            # 5. 性格相似度
            scores["personality_similarity"] = self.calculate_personality_similarity(
                user1_analysis.personality_traits, user2_analysis.personality_traits
            )

            # 计算加权总分
            total_score = sum(
                scores[dimension] * self.weights[dimension]
                for dimension in scores.keys()
            )

            # 缓存结果
            self._compatibility_cache[cache_key] = total_score

            logger.debug(f"Match score for {user1_id}-{user2_id}: {total_score:.3f}")
            return total_score

        except Exception as e:
            logger.error(f"Match score calculation failed for {user1_id}-{user2_id}: {e}")
            return 0.0

    def calculate_mbti_compatibility(self, mbti1: Optional[str], mbti2: Optional[str]) -> float:
        """计算MBTI兼容性"""
        if not mbti1 or not mbti2:
            return 0.5  # 默认中等兼容性

        # 使用Templates中的MBTI兼容性配置
        return Templates.get_mbti_compatibility_score(mbti1, mbti2)

    def calculate_interest_overlap(self, interests1: List[str], interests2: List[str]) -> float:
        """计算兴趣重叠度"""
        if not interests1 or not interests2:
            return 0.3  # 默认较低重叠度

        # 转换为小写进行比较
        set1 = set(interest.lower() for interest in interests1)
        set2 = set(interest.lower() for interest in interests2)

        # 计算Jaccard相似度
        intersection = len(set1 & set2)
        union = len(set1 | set2)

        if union == 0:
            return 0.0

        jaccard_similarity = intersection / union

        # 调整分数：有共同兴趣时给予额外加分
        if intersection > 0:
            bonus = min(0.2, intersection * 0.1)  # 每个共同兴趣加0.1分，最多0.2
            jaccard_similarity += bonus

        return min(1.0, jaccard_similarity)

    def calculate_professional_compatibility(self, prof1: Dict[str, Any], prof2: Dict[str, Any]) -> float:
        """计算职业兼容性"""
        if not prof1 or not prof2:
            return 0.5

        compatibility_score = 0.0
        factors = 0

        # 1. 行业兼容性
        industry1 = prof1.get("industry", "").lower()
        industry2 = prof2.get("industry", "").lower()
        if industry1 and industry2:
            if industry1 == industry2:
                compatibility_score += 0.8  # 同行业高兼容性
            elif self._are_related_industries(industry1, industry2):
                compatibility_score += 0.6  # 相关行业中等兼容性
            else:
                compatibility_score += 0.4  # 不同行业仍有一定兼容性
            factors += 1

        # 2. 经验级别兼容性
        exp1 = prof1.get("experience_level", "").lower()
        exp2 = prof2.get("experience_level", "").lower()
        if exp1 and exp2:
            exp_compatibility = self._calculate_experience_compatibility(exp1, exp2)
            compatibility_score += exp_compatibility
            factors += 1

        # 3. 职业焦点兼容性
        focus1 = prof1.get("career_focus", "").lower()
        focus2 = prof2.get("career_focus", "").lower()
        if focus1 and focus2:
            if focus1 == focus2:
                compatibility_score += 0.7
            elif self._are_complementary_focuses(focus1, focus2):
                compatibility_score += 0.8  # 互补的职业焦点可能更好
            else:
                compatibility_score += 0.5
            factors += 1

        return compatibility_score / factors if factors > 0 else 0.5

    def calculate_location_compatibility(self, location1: str, location2: str) -> float:
        """计算地理位置兼容性"""
        if not location1 or not location2 or location1 == "unknown" or location2 == "unknown":
            return 0.7  # 默认较高兼容性（MVP阶段）

        location1 = location1.lower()
        location2 = location2.lower()

        # 完全相同的位置
        if location1 == location2:
            return 1.0

        # 模拟城市距离计算
        distance_score = self._calculate_distance_score(location1, location2)
        return distance_score

    def calculate_personality_similarity(self, traits1: Dict[str, float], traits2: Dict[str, float]) -> float:
        """计算性格相似度"""
        if not traits1 or not traits2:
            return 0.5

        # 获取共同的性格特征
        common_traits = set(traits1.keys()) & set(traits2.keys())
        if not common_traits:
            return 0.5

        # 计算每个特征的相似度
        similarities = []
        for trait in common_traits:
            score1 = traits1[trait]
            score2 = traits2[trait]
            # 使用余弦相似度的简化版本
            similarity = 1 - abs(score1 - score2)
            similarities.append(similarity)

        # 返回平均相似度
        return sum(similarities) / len(similarities)

    # ============ 工具方法 ============

    def _get_candidate_users(self, user_id: str) -> List[str]:
        """获取候选用户池"""
        # MVP版本：模拟候选用户池
        # 实际实现中应该从数据库查询已注册的用户

        # 排除自己和已匹配过的用户
        excluded_users = {user_id} | set(self._match_history.get(user_id, []))

        # 模拟候选用户池
        all_candidate_ids = [
            f"user_{i}" for i in range(1, 21)  # 模拟20个候选用户
            if f"user_{i}" not in excluded_users
        ]

        # 随机选择一部分作为候选（模拟真实场景）
        candidate_count = min(10, len(all_candidate_ids))
        candidates = random.sample(all_candidate_ids, candidate_count)

        logger.debug(f"Generated {len(candidates)} candidates for user {user_id}")
        return candidates

    def _create_match_result(self, user1_id: str, user2_id: str, match_score: float) -> MatchResult:
        """创建匹配结果"""
        # 获取详细的兼容性分析
        compatibility_breakdown = self._get_compatibility_breakdown(user1_id, user2_id)

        # 生成推荐理由
        recommendation_reason = self._generate_recommendation_reason(compatibility_breakdown)

        # 生成匹配解释
        match_explanation = self._generate_match_explanation(user1_id, user2_id, compatibility_breakdown)

        match_result = MatchResult.create_match(user1_id, user2_id, match_score)
        match_result.compatibility_breakdown = compatibility_breakdown
        match_result.recommendation_reason = recommendation_reason
        match_result.match_explanation = match_explanation

        # 设置详细兼容性分数
        match_result.mbti_compatibility = compatibility_breakdown.get("mbti_compatibility", 0.0)
        match_result.interest_overlap = compatibility_breakdown.get("interest_overlap", 0.0)
        match_result.location_compatibility = compatibility_breakdown.get("location_compatibility", 0.0)
        match_result.professional_compatibility = compatibility_breakdown.get("professional_compatibility", 0.0)

        return match_result

    def _get_compatibility_breakdown(self, user1_id: str, user2_id: str) -> Dict[str, float]:
        """获取详细的兼容性分析"""
        user1_analysis = self.analysis_engine.get_analysis_result(user1_id)
        user2_analysis = self.analysis_engine.get_analysis_result(user2_id)

        if not user1_analysis or not user2_analysis:
            return {}

        return {
            "mbti_compatibility": self.calculate_mbti_compatibility(
                user1_analysis.mbti_type, user2_analysis.mbti_type
            ),
            "interest_overlap": self.calculate_interest_overlap(
                user1_analysis.interests, user2_analysis.interests
            ),
            "professional_compatibility": self.calculate_professional_compatibility(
                user1_analysis.professional_info, user2_analysis.professional_info
            ),
            "location_compatibility": self.calculate_location_compatibility(
                user1_analysis.professional_info.get("location", "unknown"),
                user2_analysis.professional_info.get("location", "unknown")
            ),
            "personality_similarity": self.calculate_personality_similarity(
                user1_analysis.personality_traits, user2_analysis.personality_traits
            )
        }

    def _generate_recommendation_reason(self, compatibility_breakdown: Dict[str, float]) -> str:
        """生成推荐理由"""
        reasons = []

        # 根据最高的兼容性分数生成理由
        sorted_compatibility = sorted(
            compatibility_breakdown.items(),
            key=lambda x: x[1],
            reverse=True
        )

        for dimension, score in sorted_compatibility[:2]:  # 取前两个最高分
            if score >= 0.7:
                if dimension == "mbti_compatibility":
                    reasons.append(Templates.MATCH_TEMPLATES["compatibility_reasons"]["mbti_compatible"])
                elif dimension == "interest_overlap":
                    reasons.append(Templates.MATCH_TEMPLATES["compatibility_reasons"]["shared_interests"])
                elif dimension == "professional_compatibility":
                    reasons.append(Templates.MATCH_TEMPLATES["compatibility_reasons"]["professional_synergy"])
                elif dimension == "personality_similarity":
                    reasons.append(Templates.MATCH_TEMPLATES["compatibility_reasons"]["communication_style"])

        if not reasons:
            reasons.append(Templates.MATCH_TEMPLATES["compatibility_reasons"]["values_alignment"])

        return "; ".join(reasons)

    def _generate_match_explanation(self, user1_id: str, user2_id: str,
                                   compatibility_breakdown: Dict[str, float]) -> str:
        """生成详细的匹配解释"""
        user1_analysis = self.analysis_engine.get_analysis_result(user1_id)
        user2_analysis = self.analysis_engine.get_analysis_result(user2_id)

        if not user1_analysis or not user2_analysis:
            return "Based on personality and interest analysis"

        explanation_parts = []

        # MBTI兼容性解释
        if compatibility_breakdown.get("mbti_compatibility", 0) >= 0.7:
            explanation_parts.append(
                f"Your personality types ({user1_analysis.mbti_type} and {user2_analysis.mbti_type}) complement each other well"
            )

        # 兴趣重叠解释
        common_interests = set(user1_analysis.interests) & set(user2_analysis.interests)
        if common_interests:
            interests_str = ", ".join(list(common_interests)[:3])
            explanation_parts.append(f"You both enjoy {interests_str}")

        # 职业兼容性解释
        if compatibility_breakdown.get("professional_compatibility", 0) >= 0.6:
            explanation_parts.append("Your professional backgrounds align well")

        return ". ".join(explanation_parts) if explanation_parts else "You have great potential for connection"

    def _are_related_industries(self, industry1: str, industry2: str) -> bool:
        """判断两个行业是否相关"""
        related_groups = [
            {"technology", "software", "tech", "it", "engineering"},
            {"finance", "banking", "investment", "fintech"},
            {"healthcare", "medical", "pharmaceutical", "biotech"},
            {"education", "academic", "research", "university"},
            {"media", "entertainment", "creative", "design", "marketing"},
            {"consulting", "business", "management", "strategy"}
        ]

        for group in related_groups:
            if industry1 in group and industry2 in group:
                return True

        return False

    def _calculate_experience_compatibility(self, exp1: str, exp2: str) -> float:
        """计算经验级别兼容性"""
        experience_levels = {
            "entry": 1, "junior": 2, "mid": 3, "senior": 4, "executive": 5
        }

        level1 = experience_levels.get(exp1, 3)
        level2 = experience_levels.get(exp2, 3)

        # 经验级别差距越小兼容性越高
        level_diff = abs(level1 - level2)

        if level_diff == 0:
            return 0.9  # 相同级别
        elif level_diff == 1:
            return 0.8  # 相邻级别
        elif level_diff == 2:
            return 0.6  # 差两级
        else:
            return 0.4  # 差距较大

    def _are_complementary_focuses(self, focus1: str, focus2: str) -> bool:
        """判断职业焦点是否互补"""
        complementary_pairs = [
            ("technology", "business"),
            ("creative", "analytical"),
            ("leadership", "technical"),
            ("strategy", "execution"),
            ("innovation", "operations")
        ]

        for pair in complementary_pairs:
            if (focus1 in pair[0] and focus2 in pair[1]) or (focus1 in pair[1] and focus2 in pair[0]):
                return True

        return False

    def _calculate_distance_score(self, location1: str, location2: str) -> float:
        """计算地理距离分数（模拟）"""
        # MVP版本：简单的城市距离模拟
        major_cities = {
            "new york": (40.7128, -74.0060),
            "los angeles": (34.0522, -118.2437),
            "chicago": (41.8781, -87.6298),
            "san francisco": (37.7749, -122.4194),
            "seattle": (47.6062, -122.3321),
            "boston": (42.3601, -71.0589),
            "austin": (30.2672, -97.7431),
            "denver": (39.7392, -104.9903)
        }

        # 如果位置不在已知城市中，返回默认分数
        if location1 not in major_cities or location2 not in major_cities:
            return 0.7

        # 计算简单的欧几里得距离
        lat1, lon1 = major_cities[location1]
        lat2, lon2 = major_cities[location2]

        distance = math.sqrt((lat1 - lat2)**2 + (lon1 - lon2)**2)

        # 将距离转换为兼容性分数 (距离越近分数越高)
        max_distance = 50  # 假设的最大距离
        score = max(0.0, 1.0 - (distance / max_distance))

        return min(1.0, score + 0.3)  # 确保最低分数为0.3

    # ============ 管理方法 ============

    def get_user_matches(self, user_id: str) -> List[MatchResult]:
        """获取用户的匹配结果"""
        return self._match_cache.get(user_id, [])

    def get_match_history(self, user_id: str) -> List[str]:
        """获取用户的匹配历史"""
        return self._match_history.get(user_id, [])

    def get_daily_match_count(self, user_id: str) -> int:
        """获取用户今日匹配次数"""
        today = datetime.now().strftime("%Y-%m-%d")
        return self._daily_match_count[user_id][today]

    def clear_user_cache(self, user_id: str) -> None:
        """清除用户的匹配缓存"""
        self._match_cache.pop(user_id, None)

        # 清除相关的兼容性缓存
        keys_to_remove = [
            key for key in self._compatibility_cache.keys()
            if user_id in key
        ]
        for key in keys_to_remove:
            del self._compatibility_cache[key]

        logger.info(f"Cleared match cache for user: {user_id}")

    def update_match_weights(self, new_weights: Dict[str, float]) -> bool:
        """更新匹配算法权重"""
        # 验证权重总和为1.0
        total_weight = sum(new_weights.values())
        if abs(total_weight - 1.0) > 0.01:
            logger.error(f"Invalid weights sum: {total_weight}, should be 1.0")
            return False

        # 验证所有必需的权重都存在
        required_weights = set(self.weights.keys())
        provided_weights = set(new_weights.keys())

        if required_weights != provided_weights:
            logger.error(f"Missing weights: {required_weights - provided_weights}")
            return False

        # 更新权重并清除缓存
        self.weights.update(new_weights)
        self._compatibility_cache.clear()

        logger.info(f"Updated match weights: {new_weights}")
        return True

    def get_matching_stats(self) -> Dict[str, Any]:
        """获取匹配引擎统计信息"""
        total_matches = sum(len(matches) for matches in self._match_cache.values())
        total_users_with_matches = len(self._match_cache)

        # 计算平均匹配分数
        all_scores = [
            match.match_score
            for matches in self._match_cache.values()
            for match in matches
        ]
        avg_score = sum(all_scores) / len(all_scores) if all_scores else 0.0

        return {
            "total_matches_generated": total_matches,
            "users_with_matches": total_users_with_matches,
            "average_match_score": round(avg_score, 3),
            "cached_compatibility_scores": len(self._compatibility_cache),
            "matching_score_threshold": self.matching_score_threshold,
            "max_daily_matches": self.max_daily_matches,
            "current_weights": self.weights
        }

    def record_match_feedback(self, user_id: str, match_id: str, action: str) -> APIResponse:
        """记录匹配反馈"""
        try:
            # MVP版本：简单记录反馈
            # 实际实现应该更新数据库中的匹配状态

            feedback_data = {
                "user_id": user_id,
                "match_id": match_id,
                "action": action,
                "timestamp": datetime.now().isoformat()
            }

            logger.info(f"Recorded match feedback: {feedback_data}")

            return APIResponse.success_response({
                "feedback_recorded": True,
                "user_id": user_id,
                "match_id": match_id,
                "action": action
            })

        except Exception as e:
            logger.error(f"Failed to record match feedback: {e}")
            return APIResponse.error_response(str(e), "FEEDBACK_RECORD_FAILED")

    def check_mutual_interest(self, match_id: str) -> APIResponse:
        """检查是否双向匹配"""
        try:
            # MVP版本：简单的双向匹配检查
            # 实际实现应该查询数据库检查双方是否都表示了兴趣

            # 模拟双向匹配检查
            mutual_match = False  # 默认为False，实际应该查询数据库

            return APIResponse.success_response({
                "mutual_match": mutual_match,
                "match_id": match_id
            })

        except Exception as e:
            logger.error(f"Failed to check mutual interest: {e}")
            return APIResponse.error_response(str(e), "MUTUAL_CHECK_FAILED")

    def get_user_match_statistics(self, user_id: str) -> Dict[str, Any]:
        """获取用户匹配统计"""
        try:
            # MVP版本：返回基础统计
            # 实际实现应该从数据库获取真实统计数据

            return {
                "total_matches_received": 0,
                "total_likes_given": 0,
                "total_likes_received": 0,
                "mutual_matches": 0,
                "last_match_date": None
            }

        except Exception as e:
            logger.error(f"Failed to get match statistics: {e}")
            return {}
