"""
CoreConfig - 核心配置
DeepSeek API、数据库等核心配置
"""

import os
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# 加载.env文件 - 修复路径问题
import os.path
env_path = os.path.join(os.path.dirname(__file__), '..', '..', '.env')
load_dotenv(env_path)

class CoreConfig:
    """核心配置 - MVP版本"""

    # ============ 应用环境配置 ============
    APP_ENV = os.getenv("APP_ENV", "development")
    DEBUG = os.getenv("DEBUG", "true").lower() == "true"

    # ============ DeepSeek API配置 ============
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")
    DEEPSEEK_BASE_URL = "https://api.deepseek.com"
    DEEPSEEK_MODEL = "deepseek-chat"
    DEEPSEEK_TEMPERATURE = 0.3
    DEEPSEEK_MAX_TOKENS = 2000
    DEEPSEEK_TIMEOUT = 60  # 请求超时时间（秒）

    # ============ 数据库配置 ============
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://dating_app_user:dating_app_password@localhost:5432/dating_app_db")
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6380/0")

    # 数据库连接池配置
    DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", "10"))
    DB_MAX_OVERFLOW = int(os.getenv("DB_MAX_OVERFLOW", "20"))
    DB_POOL_TIMEOUT = int(os.getenv("DB_POOL_TIMEOUT", "30"))

    # Redis配置
    REDIS_MAX_CONNECTIONS = int(os.getenv("REDIS_MAX_CONNECTIONS", "10"))
    REDIS_SOCKET_TIMEOUT = int(os.getenv("REDIS_SOCKET_TIMEOUT", "5"))

    # ============ 应用配置 ============
    # 移除重复的DEBUG定义
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    ENVIRONMENT = os.getenv("ENVIRONMENT", "development")  # development, staging, production

    # 安全配置
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-jwt-secret-key")
    JWT_REFRESH_SECRET_KEY = os.getenv("JWT_REFRESH_SECRET_KEY", "your-jwt-refresh-secret-key")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    JWT_REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7"))

    # ============ 业务配置 ============
    # 用户配置
    MAX_DAILY_MATCHES = int(os.getenv("MAX_DAILY_MATCHES", "3"))
    VOICE_CALL_MAX_DURATION = int(os.getenv("VOICE_CALL_MAX_DURATION", "600"))  # 10分钟
    CONVERSATION_TIMEOUT = int(os.getenv("CONVERSATION_TIMEOUT", "1800"))  # 30分钟

    # 分析配置
    MIN_CONFIDENCE_THRESHOLD = float(os.getenv("MIN_CONFIDENCE_THRESHOLD", "0.6"))
    MBTI_ANALYSIS_THRESHOLD = float(os.getenv("MBTI_ANALYSIS_THRESHOLD", "0.7"))
    MATCHING_SCORE_THRESHOLD = float(os.getenv("MATCHING_SCORE_THRESHOLD", "0.65"))

    # ============ 配置验证 ============
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置是否完整"""
        required_configs = [
            cls.DEEPSEEK_API_KEY,
            cls.DATABASE_URL,
            cls.REDIS_URL
        ]

        missing_configs = [config for config in required_configs if not config]
        if missing_configs:
            raise ValueError(f"Missing required configurations: {missing_configs}")

        return True

    # ============ 配置获取方法 ============
    @classmethod
    def get_deepseek_config(cls) -> Dict[str, Any]:
        """获取DeepSeek配置"""
        return {
            "api_key": cls.DEEPSEEK_API_KEY,
            "base_url": cls.DEEPSEEK_BASE_URL,
            "model": cls.DEEPSEEK_MODEL,
            "temperature": cls.DEEPSEEK_TEMPERATURE,
            "max_tokens": cls.DEEPSEEK_MAX_TOKENS,
            "timeout": cls.DEEPSEEK_TIMEOUT
        }

    @classmethod
    def get_database_config(cls) -> Dict[str, Any]:
        """获取数据库配置"""
        return {
            "url": cls.DATABASE_URL,
            "pool_size": cls.DB_POOL_SIZE,
            "max_overflow": cls.DB_MAX_OVERFLOW,
            "pool_timeout": cls.DB_POOL_TIMEOUT
        }

    @classmethod
    def get_redis_config(cls) -> Dict[str, Any]:
        """获取Redis配置"""
        return {
            "url": cls.REDIS_URL,
            "max_connections": cls.REDIS_MAX_CONNECTIONS,
            "socket_timeout": cls.REDIS_SOCKET_TIMEOUT
        }

    @classmethod
    def get_business_config(cls) -> Dict[str, Any]:
        """获取业务配置"""
        return {
            "max_daily_matches": cls.MAX_DAILY_MATCHES,
            "voice_call_max_duration": cls.VOICE_CALL_MAX_DURATION,
            "conversation_timeout": cls.CONVERSATION_TIMEOUT,
            "min_confidence_threshold": cls.MIN_CONFIDENCE_THRESHOLD,
            "mbti_analysis_threshold": cls.MBTI_ANALYSIS_THRESHOLD,
            "matching_score_threshold": cls.MATCHING_SCORE_THRESHOLD
        }

    @classmethod
    def is_production(cls) -> bool:
        """检查是否为生产环境"""
        return cls.ENVIRONMENT.lower() == "production"

    @classmethod
    def is_development(cls) -> bool:
        """检查是否为开发环境"""
        return cls.ENVIRONMENT.lower() == "development"
