2025-07-31 18:42:21,817 - __main__ - INFO - 🚀 开始 ConversationAgent 时间日志测试
2025-07-31 18:42:21,817 - __main__ - INFO - ============================================================
2025-07-31 18:42:22,055 - profilerAgent.agent.services.conversation_agent - INFO - ConversationAgent initialized with improved performance settings
2025-07-31 18:42:22,055 - __main__ - INFO - 📝 开始模拟对话流程...
2025-07-31 18:42:22,055 - __main__ - INFO - ----------------------------------------
2025-07-31 18:42:22,055 - __main__ - INFO - 
🔄 步骤 1: 开始对话
2025-07-31 18:42:22,055 - __main__ - INFO - 用户输入: ''
2025-07-31 18:42:22,055 - __main__ - INFO - 当前阶段: greeting
2025-07-31 18:42:22,556 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 0.501s
2025-07-31 18:42:22,556 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 0.501s
2025-07-31 18:42:22,556 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  process_input completed in 0.502s
2025-07-31 18:42:22,556 - __main__ - INFO - 📤 Agent 回应: 'Hi! What's your name?'
2025-07-31 18:42:22,556 - __main__ - INFO - 🎯 决策类型: ask_question
2025-07-31 18:42:22,556 - __main__ - INFO - 📊 置信度: 0.80
2025-07-31 18:42:22,557 - __main__ - INFO - ⏱️  步骤总耗时: 0.502s
2025-07-31 18:42:22,557 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.000s
2025-07-31 18:42:22,658 - __main__ - INFO - 
🔄 步骤 2: 用户介绍自己
2025-07-31 18:42:22,658 - __main__ - INFO - 用户输入: 'Hi, my name is Alice and I'm from New York'
2025-07-31 18:42:22,658 - __main__ - INFO - 当前阶段: greeting
2025-07-31 18:42:22,858 - profilerAgent.agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.6399999999999999, 'comfort': 0.57, 'enthusiasm': 0.5}
2025-07-31 18:42:22,859 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state_detailed completed in 0.200s
2025-07-31 18:42:22,859 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state completed in 0.200s
2025-07-31 18:42:22,959 - profilerAgent.agent.services.conversation_agent - INFO - AI extracted and merged info: {'name': 'TestUser'}
2025-07-31 18:42:22,960 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 0.301s
2025-07-31 18:42:22,960 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 0.302s
2025-07-31 18:42:22,960 - profilerAgent.agent.services.conversation_agent - INFO - Parallel AI processing completed in 0.30s
2025-07-31 18:42:23,461 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 0.501s
2025-07-31 18:42:23,461 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 0.502s
2025-07-31 18:42:23,462 - profilerAgent.agent.services.conversation_agent - INFO - Decision generated in 0.50s, total processing: 0.80s
2025-07-31 18:42:23,462 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  async_optimization completed in 0.804s
2025-07-31 18:42:23,462 - profilerAgent.agent.services.conversation_agent - WARNING - Preventing repetition of question: Hi! What's your name?
2025-07-31 18:42:23,462 - profilerAgent.agent.services.conversation_agent - ERROR - Failed to regenerate AI decision: 'MockAIService' object has no attribute 'make_decision'
2025-07-31 18:42:23,462 - profilerAgent.agent.services.conversation_agent - INFO - Agent transitioning stage: greeting -> professional
2025-07-31 18:42:23,462 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  process_input completed in 0.804s
2025-07-31 18:42:23,462 - __main__ - INFO - 📤 Agent 回应: 'What should I call you?'
2025-07-31 18:42:23,462 - __main__ - INFO - 🎯 决策类型: ask_question
2025-07-31 18:42:23,462 - __main__ - INFO - 📊 置信度: 0.30
2025-07-31 18:42:23,462 - __main__ - INFO - ⏱️  步骤总耗时: 0.804s
2025-07-31 18:42:23,462 - __main__ - INFO - 🔄 阶段转换: -> professional
2025-07-31 18:42:23,462 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.000s
2025-07-31 18:42:23,563 - __main__ - INFO - 
🔄 步骤 3: 职业信息
2025-07-31 18:42:23,563 - __main__ - INFO - 用户输入: 'I work as a software engineer at a tech company'
2025-07-31 18:42:23,563 - __main__ - INFO - 当前阶段: professional
2025-07-31 18:42:23,765 - profilerAgent.agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.6819999999999999, 'comfort': 0.591, 'enthusiasm': 0.5}
2025-07-31 18:42:23,765 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state_detailed completed in 0.201s
2025-07-31 18:42:23,765 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state completed in 0.202s
2025-07-31 18:42:23,865 - profilerAgent.agent.services.conversation_agent - INFO - AI extracted and merged info: {'profession': 'Software Engineer'}
2025-07-31 18:42:23,865 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 0.302s
2025-07-31 18:42:23,865 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 0.302s
2025-07-31 18:42:23,865 - profilerAgent.agent.services.conversation_agent - INFO - Parallel AI processing completed in 0.30s
2025-07-31 18:42:24,366 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 0.501s
2025-07-31 18:42:24,366 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 0.501s
2025-07-31 18:42:24,366 - profilerAgent.agent.services.conversation_agent - INFO - Decision generated in 0.50s, total processing: 0.80s
2025-07-31 18:42:24,366 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  async_optimization completed in 0.803s
2025-07-31 18:42:24,367 - profilerAgent.agent.services.conversation_agent - INFO - Agent transitioning stage: professional -> personality
2025-07-31 18:42:24,367 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  process_input completed in 0.803s
2025-07-31 18:42:24,367 - __main__ - INFO - 📤 Agent 回应: 'What kind of work do you do?'
2025-07-31 18:42:24,367 - __main__ - INFO - 🎯 决策类型: ask_question
2025-07-31 18:42:24,367 - __main__ - INFO - 📊 置信度: 0.80
2025-07-31 18:42:24,367 - __main__ - INFO - ⏱️  步骤总耗时: 0.803s
2025-07-31 18:42:24,367 - __main__ - INFO - 🔄 阶段转换: -> personality
2025-07-31 18:42:24,367 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.000s
2025-07-31 18:42:24,467 - __main__ - INFO - 
🔄 步骤 4: 性格特征
2025-07-31 18:42:24,468 - __main__ - INFO - 用户输入: 'I'm more of an extrovert, I love meeting new people'
2025-07-31 18:42:24,468 - __main__ - INFO - 当前阶段: personality
2025-07-31 18:42:24,669 - profilerAgent.agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.6945999999999999, 'comfort': 0.5972999999999999, 'enthusiasm': 0.78}
2025-07-31 18:42:24,669 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state_detailed completed in 0.201s
2025-07-31 18:42:24,669 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state completed in 0.201s
2025-07-31 18:42:24,769 - profilerAgent.agent.services.conversation_agent - INFO - AI extracted and merged info: {'name': 'TestUser', 'personality_traits': 'Extrovert', 'interests': 'Reading, Coding'}
2025-07-31 18:42:24,769 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 0.301s
2025-07-31 18:42:24,769 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 0.301s
2025-07-31 18:42:24,769 - profilerAgent.agent.services.conversation_agent - INFO - Parallel AI processing completed in 0.30s
2025-07-31 18:42:25,270 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 0.501s
2025-07-31 18:42:25,271 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 0.501s
2025-07-31 18:42:25,271 - profilerAgent.agent.services.conversation_agent - INFO - Decision generated in 0.50s, total processing: 0.80s
2025-07-31 18:42:25,271 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  async_optimization completed in 0.803s
2025-07-31 18:42:25,271 - profilerAgent.agent.services.conversation_agent - INFO - Agent transitioning stage: personality -> relationships
2025-07-31 18:42:25,271 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  process_input completed in 0.803s
2025-07-31 18:42:25,271 - __main__ - INFO - 📤 Agent 回应: 'Are you more of an introvert or extrovert?'
2025-07-31 18:42:25,271 - __main__ - INFO - 🎯 决策类型: ask_question
2025-07-31 18:42:25,271 - __main__ - INFO - 📊 置信度: 0.80
2025-07-31 18:42:25,271 - __main__ - INFO - ⏱️  步骤总耗时: 0.803s
2025-07-31 18:42:25,271 - __main__ - INFO - 🔄 阶段转换: -> relationships
2025-07-31 18:42:25,271 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.000s
2025-07-31 18:42:25,372 - __main__ - INFO - 
🔄 步骤 5: 兴趣爱好
2025-07-31 18:42:25,372 - __main__ - INFO - 用户输入: 'I enjoy reading books, coding, and hiking on weekends'
2025-07-31 18:42:25,372 - __main__ - INFO - 当前阶段: relationships
2025-07-31 18:42:25,574 - profilerAgent.agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.6983799999999999, 'comfort': 0.59919, 'enthusiasm': 0.584}
2025-07-31 18:42:25,574 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state_detailed completed in 0.201s
2025-07-31 18:42:25,574 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state completed in 0.201s
2025-07-31 18:42:25,674 - profilerAgent.agent.services.conversation_agent - INFO - AI extracted and merged info: {'interests': 'Reading, Coding'}
2025-07-31 18:42:25,674 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 0.301s
2025-07-31 18:42:25,674 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 0.301s
2025-07-31 18:42:25,674 - profilerAgent.agent.services.conversation_agent - INFO - Parallel AI processing completed in 0.30s
2025-07-31 18:42:26,178 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 0.504s
2025-07-31 18:42:26,179 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 0.505s
2025-07-31 18:42:26,179 - profilerAgent.agent.services.conversation_agent - INFO - Decision generated in 0.50s, total processing: 0.81s
2025-07-31 18:42:26,179 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  async_optimization completed in 0.806s
2025-07-31 18:42:26,179 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  process_input completed in 0.807s
2025-07-31 18:42:26,179 - __main__ - INFO - 📤 Agent 回应: 'What are you looking for in a relationship?'
2025-07-31 18:42:26,179 - __main__ - INFO - 🎯 决策类型: ask_question
2025-07-31 18:42:26,179 - __main__ - INFO - 📊 置信度: 0.80
2025-07-31 18:42:26,179 - __main__ - INFO - ⏱️  步骤总耗时: 0.807s
2025-07-31 18:42:26,179 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.000s
2025-07-31 18:42:26,280 - __main__ - INFO - 
🔄 步骤 6: 关系偏好
2025-07-31 18:42:26,280 - __main__ - INFO - 用户输入: 'I'm looking for a long-term relationship with someone who shares my values'
2025-07-31 18:42:26,281 - __main__ - INFO - 当前阶段: relationships
2025-07-31 18:42:26,482 - profilerAgent.agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.6995139999999999, 'comfort': 0.599757, 'enthusiasm': 0.5252}
2025-07-31 18:42:26,483 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state_detailed completed in 0.202s
2025-07-31 18:42:26,483 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state completed in 0.202s
2025-07-31 18:42:26,582 - profilerAgent.agent.services.conversation_agent - INFO - AI extracted and merged info: {'name': 'TestUser', 'relationship_preferences': 'Long-term commitment'}
2025-07-31 18:42:26,583 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 0.302s
2025-07-31 18:42:26,583 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 0.302s
2025-07-31 18:42:26,585 - profilerAgent.agent.services.conversation_agent - INFO - Parallel AI processing completed in 0.30s
2025-07-31 18:42:27,086 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 0.501s
2025-07-31 18:42:27,086 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 0.501s
2025-07-31 18:42:27,086 - profilerAgent.agent.services.conversation_agent - INFO - Decision generated in 0.50s, total processing: 0.81s
2025-07-31 18:42:27,086 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  async_optimization completed in 0.806s
2025-07-31 18:42:27,087 - profilerAgent.agent.services.conversation_agent - WARNING - Preventing repetition of question: What are you looking for in a relationship?
2025-07-31 18:42:27,087 - profilerAgent.agent.services.conversation_agent - ERROR - Failed to regenerate AI decision: 'MockAIService' object has no attribute 'make_decision'
2025-07-31 18:42:27,087 - profilerAgent.agent.services.conversation_agent - INFO - Agent transitioning stage: relationships -> closing
2025-07-31 18:42:27,087 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  process_input completed in 0.806s
2025-07-31 18:42:27,087 - __main__ - INFO - 📤 Agent 回应: 'What are you looking for?'
2025-07-31 18:42:27,087 - __main__ - INFO - 🎯 决策类型: ask_question
2025-07-31 18:42:27,087 - __main__ - INFO - 📊 置信度: 0.30
2025-07-31 18:42:27,087 - __main__ - INFO - ⏱️  步骤总耗时: 0.806s
2025-07-31 18:42:27,087 - __main__ - INFO - 🔄 阶段转换: -> closing
2025-07-31 18:42:27,087 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.000s
2025-07-31 18:42:27,187 - __main__ - INFO - 
============================================================
2025-07-31 18:42:27,187 - __main__ - INFO - 📊 测试总结
2025-07-31 18:42:27,187 - __main__ - INFO - ============================================================
2025-07-31 18:42:27,187 - __main__ - INFO - ⏱️  总测试时间: 5.132s
2025-07-31 18:42:27,187 - __main__ - INFO - 🔢 处理步骤数: 6
2025-07-31 18:42:27,187 - __main__ - INFO - 📈 平均每步耗时: 0.855s
2025-07-31 18:42:27,188 - __main__ - INFO - 💬 最终对话阶段: closing
2025-07-31 18:42:27,188 - __main__ - INFO - 📝 收集的信息: 5 项
2025-07-31 18:42:27,188 - __main__ - INFO - 🎯 成功回应数: 5
2025-07-31 18:42:27,188 - __main__ - INFO - 
📋 收集到的用户信息:
2025-07-31 18:42:27,188 - __main__ - INFO -   • name: TestUser
2025-07-31 18:42:27,188 - __main__ - INFO -   • profession: Software Engineer
2025-07-31 18:42:27,188 - __main__ - INFO -   • personality_traits: ['Extrovert']
2025-07-31 18:42:27,188 - __main__ - INFO -   • interests: ['Reading, Coding']
2025-07-31 18:42:27,188 - __main__ - INFO -   • relationship_preferences: ['Long-term commitment']
2025-07-31 18:42:27,188 - __main__ - INFO - 
😊 最终情感状态:
2025-07-31 18:42:27,188 - __main__ - INFO -   • engagement: 0.70
2025-07-31 18:42:27,188 - __main__ - INFO -   • comfort: 0.60
2025-07-31 18:42:27,188 - __main__ - INFO -   • enthusiasm: 0.53
2025-07-31 18:42:27,188 - __main__ - INFO - 
✅ 时间日志测试完成！
2025-07-31 18:42:27,189 - __main__ - INFO - 
🔬 开始单个方法性能测试
2025-07-31 18:42:27,189 - __main__ - INFO - ============================================================
2025-07-31 18:42:27,189 - profilerAgent.agent.services.conversation_agent - INFO - ConversationAgent initialized with improved performance settings
2025-07-31 18:42:27,189 - __main__ - INFO - 🧪 测试信息提取方法...
2025-07-31 18:42:27,490 - profilerAgent.agent.services.conversation_agent - INFO - AI extracted and merged info: {'profession': 'Software Engineer', 'interests': 'Reading, Coding'}
2025-07-31 18:42:27,492 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 0.303s
2025-07-31 18:42:27,492 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 0.303s
2025-07-31 18:42:27,492 - __main__ - INFO - 🧪 测试情感状态更新方法...
2025-07-31 18:42:27,693 - profilerAgent.agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.6399999999999999, 'comfort': 0.57, 'enthusiasm': 0.78}
2025-07-31 18:42:27,693 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state_detailed completed in 0.201s
2025-07-31 18:42:27,693 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  update_emotional_state completed in 0.201s
2025-07-31 18:42:27,693 - __main__ - INFO - 🧪 测试智能决策方法...
2025-07-31 18:42:28,195 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 0.502s
2025-07-31 18:42:28,198 - profilerAgent.agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 0.505s
2025-07-31 18:42:28,198 - __main__ - INFO - 决策结果: What kind of work do you do?
2025-07-31 18:42:28,198 - __main__ - INFO - ✅ 单个方法测试完成！
